/*! For license information please see main.a4989988.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,i={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!l.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:i,_owner:s.current}}t.Fragment=i,t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var b=x.prototype=new y;b.constructor=x,m(b,v.prototype),b.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var a,i={},o=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(o=""+t.key),t)k.call(t,a)&&!E.hasOwnProperty(a)&&(i[a]=t[a]);var l=arguments.length-2;if(1===l)i.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(a in l=e.defaultProps)void 0===i[a]&&(i[a]=l[a]);return{$$typeof:n,type:e,key:o,ref:s,props:i,_owner:S.current}}function P(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function j(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function T(e,t,a,i,o){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return o=o(l=e),e=""===i?"."+j(l,0):i,w(o)?(a="",null!=e&&(a=e.replace(N,"$&/")+"/"),T(o,t,a,"",function(e){return e})):null!=o&&(P(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(N,"$&/")+"/")+e)),t.push(o)),1;if(l=0,i=""===i?".":i+":",w(e))for(var u=0;u<e.length;u++){var c=i+j(s=e[u],u);l+=T(s,t,a,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=T(s=s.value,t,a,c=i+j(s,u++),o);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function M(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",function(e){return t.call(n,e,a++)}),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},A={transition:null},D={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:A,ReactCurrentOwner:S};function V(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:M,forEach:function(e,t,n){M(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return M(e,function(){t++}),t},toArray:function(e){return M(e,function(e){return e})||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=o,t.PureComponent=x,t.StrictMode=i,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=D,t.act=V,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),i=e.key,o=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,s=S.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)k.call(t,u)&&!E.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];a.children=l}return{$$typeof:n,type:e.type,key:i,ref:o,props:a,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=A.transition;A.transition={};try{e()}finally{A.transition=t}},t.unstable_act=V,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,n){return R.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,n){return R.current.useReducer(e,t,n)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return R.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<i(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>i(l,n))u<a&&0>i(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<a&&0>i(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function b(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,b(e),!m)if(null!==r(u))m=!0,A(k);else{var t=r(c);null!==t&&D(w,t.startTime-e)}}function k(e,n){m=!1,g&&(g=!1,y(P),P=-1),p=!0;var i=h;try{for(b(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!T());){var o=f.callback;if("function"===typeof o){f.callback=null,h=f.priorityLevel;var s=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&a(u),b(n)}else a(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&D(w,d.startTime-n),l=!1}return l}finally{f=null,h=i,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,E=!1,C=null,P=-1,N=5,j=-1;function T(){return!(t.unstable_now()-j<N)}function M(){if(null!==C){var e=t.unstable_now();j=e;var n=!0;try{n=C(!0,e)}finally{n?S():(E=!1,C=null)}}else E=!1}if("function"===typeof x)S=function(){x(M)};else if("undefined"!==typeof MessageChannel){var L=new MessageChannel,R=L.port2;L.port1.onmessage=M,S=function(){R.postMessage(null)}}else S=function(){v(M,0)};function A(e){C=e,E||(E=!0,S())}function D(e,n){P=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,A(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,a,i){var o=t.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?o+i:o:i=o,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:s=i+s,sortIndex:-1},i>o?(e.sortIndex=i,n(c,e),null===r(u)&&e===r(c)&&(g?(y(P),P=-1):g=!0,D(w,i-o))):(e.sortIndex=s,n(u,e),m||p||(m=!0,A(k))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,a,i,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function x(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var b=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),P=Symbol.for("react.provider"),N=Symbol.for("react.context"),j=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),M=Symbol.for("react.suspense_list"),L=Symbol.for("react.memo"),R=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var A=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var D=Symbol.iterator;function V(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=D&&e[D]||e["@@iterator"])?e:null}var _,z=Object.assign;function O(e){if(void 0===_)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);_=t&&t[1]||""}return"\n"+_+e}var I=!1;function F(e,t){if(!e||I)return"";I=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),i=r.stack.split("\n"),o=a.length-1,s=i.length-1;1<=o&&0<=s&&a[o]!==i[s];)s--;for(;1<=o&&0<=s;o--,s--)if(a[o]!==i[s]){if(1!==o||1!==s)do{if(o--,0>--s||a[o]!==i[s]){var l="\n"+a[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=o&&0<=s);break}}}finally{I=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?O(e):""}function B(e){switch(e.tag){case 5:return O(e.type);case 16:return O("Lazy");case 13:return O("Suspense");case 19:return O("SuspenseList");case 0:case 2:case 15:return e=F(e.type,!1);case 11:return e=F(e.type.render,!1);case 1:return e=F(e.type,!0);default:return""}}function U(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case C:return"Profiler";case E:return"StrictMode";case T:return"Suspense";case M:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case N:return(e.displayName||"Context")+".Consumer";case P:return(e._context.displayName||"Context")+".Provider";case j:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case L:return null!==(t=e.displayName||null)?t:U(e.type)||"Memo";case R:t=e._payload,e=e._init;try{return U(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return U(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Y(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function X(e,t){var n=t.checked;return z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function K(e,t){null!=(t=t.checked)&&x(e,"checked",t,!1)}function Z(e,t){K(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(i(92));if(te(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function ie(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(he).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]})});var ve=z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(i(62))}}function xe(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var be=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ee=null;function Ce(e){if(e=xa(e)){if("function"!==typeof ke)throw Error(i(280));var t=e.stateNode;t&&(t=wa(t),ke(e.stateNode,e.type,t))}}function Pe(e){Se?Ee?Ee.push(e):Ee=[e]:Se=e}function Ne(){if(Se){var e=Se,t=Ee;if(Ee=Se=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function je(e,t){return e(t)}function Te(){}var Me=!1;function Le(e,t,n){if(Me)return e(t,n);Me=!0;try{return je(e,t,n)}finally{Me=!1,(null!==Se||null!==Ee)&&(Te(),Ne())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Ae=!1;if(c)try{var De={};Object.defineProperty(De,"passive",{get:function(){Ae=!0}}),window.addEventListener("test",De,De),window.removeEventListener("test",De,De)}catch(ce){Ae=!1}function Ve(e,t,n,r,a,i,o,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var _e=!1,ze=null,Oe=!1,Ie=null,Fe={onError:function(e){_e=!0,ze=e}};function Be(e,t,n,r,a,i,o,s,l){_e=!1,ze=null,Ve.apply(Fe,arguments)}function Ue(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function We(e){if(Ue(e)!==e)throw Error(i(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ue(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return We(a),e;if(o===r)return We(a),t;o=o.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=o;else{for(var s=!1,l=a.child;l;){if(l===n){s=!0,n=a,r=o;break}if(l===r){s=!0,r=a,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=a;break}if(l===r){s=!0,r=o,n=a;break}l=l.sibling}if(!s)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(e))?Ye(e):null}function Ye(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ye(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,qe=a.unstable_cancelCallback,Xe=a.unstable_shouldYield,Ge=a.unstable_requestPaint,Ke=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Je=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,it=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,i=e.pingedLanes,o=268435455&n;if(0!==o){var s=o&~a;0!==s?r=dt(s):0!==(i&=o)&&(r=dt(i))}else 0!==(o=n&~a)?r=dt(o):0!==i&&(r=dt(i));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(i=t&-t)||16===a&&0!==(4194240&i)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var xt=0;function bt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,kt,St,Et,Ct,Pt=!1,Nt=[],jt=null,Tt=null,Mt=null,Lt=new Map,Rt=new Map,At=[],Dt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Vt(e,t){switch(e){case"focusin":case"focusout":jt=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Mt=null;break;case"pointerover":case"pointerout":Lt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function _t(e,t,n,r,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},null!==t&&(null!==(t=xa(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function zt(e){var t=ya(e.target);if(null!==t){var n=Ue(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Ct(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ot(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Xt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=xa(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);be=r,n.target.dispatchEvent(r),be=null,t.shift()}return!0}function It(e,t,n){Ot(e)&&n.delete(t)}function Ft(){Pt=!1,null!==jt&&Ot(jt)&&(jt=null),null!==Tt&&Ot(Tt)&&(Tt=null),null!==Mt&&Ot(Mt)&&(Mt=null),Lt.forEach(It),Rt.forEach(It)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Pt||(Pt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ft)))}function Ut(e){function t(t){return Bt(t,e)}if(0<Nt.length){Bt(Nt[0],e);for(var n=1;n<Nt.length;n++){var r=Nt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==jt&&Bt(jt,e),null!==Tt&&Bt(Tt,e),null!==Mt&&Bt(Mt,e),Lt.forEach(t),Rt.forEach(t),n=0;n<At.length;n++)(r=At[n]).blockedOn===e&&(r.blockedOn=null);for(;0<At.length&&null===(n=At[0]).blockedOn;)zt(n),null===n.blockedOn&&At.shift()}var Ht=b.ReactCurrentBatchConfig,Wt=!0;function $t(e,t,n,r){var a=xt,i=Ht.transition;Ht.transition=null;try{xt=1,Qt(e,t,n,r)}finally{xt=a,Ht.transition=i}}function Yt(e,t,n,r){var a=xt,i=Ht.transition;Ht.transition=null;try{xt=4,Qt(e,t,n,r)}finally{xt=a,Ht.transition=i}}function Qt(e,t,n,r){if(Wt){var a=Xt(e,t,n,r);if(null===a)Wr(e,t,r,qt,n),Vt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return jt=_t(jt,e,t,n,r,a),!0;case"dragenter":return Tt=_t(Tt,e,t,n,r,a),!0;case"mouseover":return Mt=_t(Mt,e,t,n,r,a),!0;case"pointerover":var i=a.pointerId;return Lt.set(i,_t(Lt.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":return i=a.pointerId,Rt.set(i,_t(Rt.get(i)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Vt(e,r),4&t&&-1<Dt.indexOf(e)){for(;null!==a;){var i=xa(a);if(null!==i&&wt(i),null===(i=Xt(e,t,n,r))&&Wr(e,t,r,qt,n),i===a)break;a=i}null!==a&&r.stopPropagation()}else Wr(e,t,r,null,n)}}var qt=null;function Xt(e,t,n,r){if(qt=null,null!==(e=ya(e=we(r))))if(null===(t=Ue(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return qt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Kt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,r=n.length,a="value"in Kt?Kt.value:Kt.textContent,i=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[i-t];t++);return Jt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,i){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=z({},un,{view:0,detail:0}),fn=an(dn),hn=z({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(on=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=on=0,ln=e),on)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=an(hn),mn=an(z({},hn,{dataTransfer:0})),gn=an(z({},dn,{relatedTarget:0})),vn=an(z({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=z({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xn=an(yn),bn=an(z({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function Cn(){return En}var Pn=z({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nn=an(Pn),jn=an(z({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=an(z({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),Mn=an(z({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Ln=z({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=an(Ln),An=[9,13,27,32],Dn=c&&"CompositionEvent"in window,Vn=null;c&&"documentMode"in document&&(Vn=document.documentMode);var _n=c&&"TextEvent"in window&&!Vn,zn=c&&(!Dn||Vn&&8<Vn&&11>=Vn),On=String.fromCharCode(32),In=!1;function Fn(e,t){switch(e){case"keyup":return-1!==An.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function $n(e,t,n,r){Pe(r),0<(t=Yr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Yn=null,Qn=null;function qn(e){Or(e,0)}function Xn(e){if(Q(ba(e)))return e}function Gn(e,t){if("change"===e)return t}var Kn=!1;if(c){var Zn;if(c){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Zn=Jn}else Zn=!1;Kn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Yn&&(Yn.detachEvent("onpropertychange",nr),Qn=Yn=null)}function nr(e){if("value"===e.propertyName&&Xn(Qn)){var t=[];$n(t,Qn,e,we(e)),Le(qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(Yn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Xn(Qn)}function ir(e,t){if("click"===e)return Xn(t)}function or(e,t){if("input"===e||"change"===e)return Xn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!sr(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=q((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,i=Math.min(r.start,a);r=void 0===r.end?i:Math.min(r.end,a),!e.extend&&i>r&&(a=r,r=i,i=a),a=cr(n,i);var o=cr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,xr=!1;function br(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;xr||null==gr||gr!==q(r)||("selectionStart"in(r=gr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&lr(yr,r)||(yr=r,0<(r=Yr(vr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Sr={},Er={};function Cr(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return Sr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Pr=Cr("animationend"),Nr=Cr("animationiteration"),jr=Cr("animationstart"),Tr=Cr("transitionend"),Mr=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Mr.set(e,t),l(t,[e])}for(var Ar=0;Ar<Lr.length;Ar++){var Dr=Lr[Ar];Rr(Dr.toLowerCase(),"on"+(Dr[0].toUpperCase()+Dr.slice(1)))}Rr(Pr,"onAnimationEnd"),Rr(Nr,"onAnimationIteration"),Rr(jr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Tr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Vr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),_r=new Set("cancel close invalid load scroll toggle".split(" ").concat(Vr));function zr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,s,l,u){if(Be.apply(this,arguments),_e){if(!_e)throw Error(i(198));var c=ze;_e=!1,ze=null,Oe||(Oe=!0,Ie=c)}}(r,t,void 0,e),e.currentTarget=null}function Or(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==i&&a.isPropagationStopped())break e;zr(a,s,u),i=l}else for(o=0;o<r.length;o++){if(l=(s=r[o]).instance,u=s.currentTarget,s=s.listener,l!==i&&a.isPropagationStopped())break e;zr(a,s,u),i=l}}}if(Oe)throw e=Ie,Oe=!1,Ie=null,e}function Ir(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Fr(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[Br]){e[Br]=!0,o.forEach(function(t){"selectionchange"!==t&&(_r.has(t)||Fr(t,!1,e),Fr(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Fr("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Gt(t)){case 1:var a=$t;break;case 4:a=Yt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Ae||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Wr(e,t,n,r,a){var i=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var s=r.stateNode.containerInfo;if(s===a||8===s.nodeType&&s.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var l=o.tag;if((3===l||4===l)&&((l=o.stateNode.containerInfo)===a||8===l.nodeType&&l.parentNode===a))return;o=o.return}for(;null!==s;){if(null===(o=ya(s)))return;if(5===(l=o.tag)||6===l){r=i=o;continue e}s=s.parentNode}}r=r.return}Le(function(){var r=i,a=we(n),o=[];e:{var s=Mr.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=Nn;break;case"focusin":u="focus",l=gn;break;case"focusout":u="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Tn;break;case Pr:case Nr:case jr:l=vn;break;case Tr:l=Mn;break;case"scroll":l=fn;break;case"wheel":l=Rn;break;case"copy":case"cut":case"paste":l=xn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=jn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Re(p,f))&&c.push($r(p,m,h)))),d)break;p=p.return}0<c.length&&(s=new l(s,u,null,n,a),o.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===be||!(u=n.relatedTarget||n.fromElement)||!ya(u)&&!u[pa])&&(l||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?ya(u):null)&&(u!==(d=Ue(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=jn,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:ba(l),h=null==u?s:ba(u),(s=new c(m,p+"leave",l,n,a)).target=d,s.relatedTarget=h,m=null,ya(a)===r&&((c=new c(f,p+"enter",u,n,a)).target=h,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,p=0,h=c=l;h;h=Qr(h))p++;for(h=0,m=f;m;m=Qr(m))h++;for(;0<p-h;)c=Qr(c),p--;for(;0<h-p;)f=Qr(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==l&&qr(o,s,l,c,!1),null!==u&&null!==d&&qr(o,d,u,c,!0)}if("select"===(l=(s=r?ba(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Gn;else if(Wn(s))if(Kn)g=or;else{g=ar;var v=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=ir);switch(g&&(g=g(e,r))?$n(o,g,n,a):(v&&v(e,s,r),"focusout"===e&&(v=s._wrapperState)&&v.controlled&&"number"===s.type&&ee(s,"number",s.value)),v=r?ba(r):window,e){case"focusin":(Wn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,br(o,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":br(o,n,a)}var y;if(Dn)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else Un?Fn(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(zn&&"ko"!==n.locale&&(Un||"onCompositionStart"!==x?"onCompositionEnd"===x&&Un&&(y=en()):(Zt="value"in(Kt=a)?Kt.value:Kt.textContent,Un=!0)),0<(v=Yr(r,x)).length&&(x=new bn(x,e,null,n,a),o.push({event:x,listeners:v}),y?x.data=y:null!==(y=Bn(n))&&(x.data=y))),(y=_n?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(In=!0,On);case"textInput":return(e=t.data)===On&&In?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!Dn&&Fn(e,t)?(e=en(),Jt=Zt=Kt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return zn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Yr(r,"onBeforeInput")).length&&(a=new bn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=y))}Or(o,t)})}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Yr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,i=a.stateNode;5===a.tag&&null!==i&&(a=i,null!=(i=Re(e,n))&&r.unshift($r(e,i,a)),null!=(i=Re(e,t))&&r.push($r(e,i,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function qr(e,t,n,r,a){for(var i=t._reactName,o=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,a?null!=(l=Re(n,i))&&o.unshift($r(n,l,s)):a||null!=(l=Re(n,i))&&o.push($r(n,l,s))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Xr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Kr(e){return("string"===typeof e?e:""+e).replace(Xr,"\n").replace(Gr,"")}function Zr(e,t,n){if(t=Kr(t),Kr(e)!==t&&n)throw Error(i(425))}function Jr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,ia="function"===typeof Promise?Promise:void 0,oa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ia?function(e){return ia.resolve(null).then(e).catch(sa)}:ra;function sa(e){setTimeout(function(){throw e})}function la(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Ut(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,ha="__reactProps$"+da,pa="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,va="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pa]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function xa(e){return!(e=e[fa]||e[pa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function ba(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function wa(e){return e[ha]||null}var ka=[],Sa=-1;function Ea(e){return{current:e}}function Ca(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function Pa(e,t){Sa++,ka[Sa]=e.current,e.current=t}var Na={},ja=Ea(Na),Ta=Ea(!1),Ma=Na;function La(e,t){var n=e.type.contextTypes;if(!n)return Na;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,i={};for(a in n)i[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ra(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Aa(){Ca(Ta),Ca(ja)}function Da(e,t,n){if(ja.current!==Na)throw Error(i(168));Pa(ja,t),Pa(Ta,n)}function Va(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(i(108,H(e)||"Unknown",a));return z({},n,r)}function _a(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Na,Ma=ja.current,Pa(ja,e),Pa(Ta,Ta.current),!0}function za(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=Va(e,t,Ma),r.__reactInternalMemoizedMergedChildContext=e,Ca(Ta),Ca(ja),Pa(ja,e)):Ca(Ta),Pa(Ta,n)}var Oa=null,Ia=!1,Fa=!1;function Ba(e){null===Oa?Oa=[e]:Oa.push(e)}function Ua(){if(!Fa&&null!==Oa){Fa=!0;var e=0,t=xt;try{var n=Oa;for(xt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Oa=null,Ia=!1}catch(a){throw null!==Oa&&(Oa=Oa.slice(e+1)),Qe(Je,Ua),a}finally{xt=t,Fa=!1}}return null}var Ha=[],Wa=0,$a=null,Ya=0,Qa=[],qa=0,Xa=null,Ga=1,Ka="";function Za(e,t){Ha[Wa++]=Ya,Ha[Wa++]=$a,$a=e,Ya=t}function Ja(e,t,n){Qa[qa++]=Ga,Qa[qa++]=Ka,Qa[qa++]=Xa,Xa=e;var r=Ga;e=Ka;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var i=32-ot(t)+a;if(30<i){var o=a-a%5;i=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Ga=1<<32-ot(t)+a|n<<a|r,Ka=i+e}else Ga=1<<i|n<<a|r,Ka=e}function ei(e){null!==e.return&&(Za(e,1),Ja(e,1,0))}function ti(e){for(;e===$a;)$a=Ha[--Wa],Ha[Wa]=null,Ya=Ha[--Wa],Ha[Wa]=null;for(;e===Xa;)Xa=Qa[--qa],Qa[qa]=null,Ka=Qa[--qa],Qa[qa]=null,Ga=Qa[--qa],Qa[qa]=null}var ni=null,ri=null,ai=!1,ii=null;function oi(e,t){var n=Lu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function si(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ni=e,ri=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ni=e,ri=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Xa?{id:Ga,overflow:Ka}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Lu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ni=e,ri=null,!0);default:return!1}}function li(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ui(e){if(ai){var t=ri;if(t){var n=t;if(!si(e,t)){if(li(e))throw Error(i(418));t=ua(n.nextSibling);var r=ni;t&&si(e,t)?oi(r,n):(e.flags=-4097&e.flags|2,ai=!1,ni=e)}}else{if(li(e))throw Error(i(418));e.flags=-4097&e.flags|2,ai=!1,ni=e}}}function ci(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ni=e}function di(e){if(e!==ni)return!1;if(!ai)return ci(e),ai=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ri)){if(li(e))throw fi(),Error(i(418));for(;t;)oi(e,t),t=ua(t.nextSibling)}if(ci(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ri=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ri=null}}else ri=ni?ua(e.stateNode.nextSibling):null;return!0}function fi(){for(var e=ri;e;)e=ua(e.nextSibling)}function hi(){ri=ni=null,ai=!1}function pi(e){null===ii?ii=[e]:ii.push(e)}var mi=b.ReactCurrentBatchConfig;function gi(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function vi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function yi(e){return(0,e._init)(e._payload)}function xi(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Au(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=zu(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var i=n.type;return i===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===R&&yi(i)===t.type)?((r=a(t,n.props)).ref=gi(e,t,n),r.return=e,r):((r=Du(n.type,n.key,n.props,null,e.mode,r)).ref=gi(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ou(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=Vu(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=zu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Du(t.type,t.key,t.props,null,e.mode,n)).ref=gi(e,null,t),n.return=e,n;case k:return(t=Ou(t,e.mode,n)).return=e,t;case R:return f(e,(0,t._init)(t._payload),n)}if(te(t)||V(t))return(t=Vu(t,e.mode,n,null)).return=e,t;vi(e,t)}return null}function h(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case R:return h(e,t,(a=n._init)(n._payload),r)}if(te(n)||V(n))return null!==a?null:d(e,t,n,r,null);vi(e,n)}return null}function p(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case R:return p(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||V(r))return d(t,e=e.get(n)||null,r,a,null);vi(t,r)}return null}function m(a,i,s,l){for(var u=null,c=null,d=i,m=i=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=h(a,d,s[m],l);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),i=o(v,i,m),null===c?u=v:c.sibling=v,c=v,d=g}if(m===s.length)return n(a,d),ai&&Za(a,m),u;if(null===d){for(;m<s.length;m++)null!==(d=f(a,s[m],l))&&(i=o(d,i,m),null===c?u=d:c.sibling=d,c=d);return ai&&Za(a,m),u}for(d=r(a,d);m<s.length;m++)null!==(g=p(d,a,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),i=o(g,i,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),ai&&Za(a,m),u}function g(a,s,l,u){var c=V(l);if("function"!==typeof c)throw Error(i(150));if(null==(l=c.call(l)))throw Error(i(151));for(var d=c=null,m=s,g=s=0,v=null,y=l.next();null!==m&&!y.done;g++,y=l.next()){m.index>g?(v=m,m=null):v=m.sibling;var x=h(a,m,y.value,u);if(null===x){null===m&&(m=v);break}e&&m&&null===x.alternate&&t(a,m),s=o(x,s,g),null===d?c=x:d.sibling=x,d=x,m=v}if(y.done)return n(a,m),ai&&Za(a,g),c;if(null===m){for(;!y.done;g++,y=l.next())null!==(y=f(a,y.value,u))&&(s=o(y,s,g),null===d?c=y:d.sibling=y,d=y);return ai&&Za(a,g),c}for(m=r(a,m);!y.done;g++,y=l.next())null!==(y=p(m,a,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),s=o(y,s,g),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach(function(e){return t(a,e)}),ai&&Za(a,g),c}return function e(r,i,o,l){if("object"===typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var u=o.key,c=i;null!==c;){if(c.key===u){if((u=o.type)===S){if(7===c.tag){n(r,c.sibling),(i=a(c,o.props.children)).return=r,r=i;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===R&&yi(u)===c.type){n(r,c.sibling),(i=a(c,o.props)).ref=gi(r,c,o),i.return=r,r=i;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===S?((i=Vu(o.props.children,r.mode,l,o.key)).return=r,r=i):((l=Du(o.type,o.key,o.props,null,r.mode,l)).ref=gi(r,i,o),l.return=r,r=l)}return s(r);case k:e:{for(c=o.key;null!==i;){if(i.key===c){if(4===i.tag&&i.stateNode.containerInfo===o.containerInfo&&i.stateNode.implementation===o.implementation){n(r,i.sibling),(i=a(i,o.children||[])).return=r,r=i;break e}n(r,i);break}t(r,i),i=i.sibling}(i=Ou(o,r.mode,l)).return=r,r=i}return s(r);case R:return e(r,i,(c=o._init)(o._payload),l)}if(te(o))return m(r,i,o,l);if(V(o))return g(r,i,o,l);vi(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==i&&6===i.tag?(n(r,i.sibling),(i=a(i,o)).return=r,r=i):(n(r,i),(i=zu(o,r.mode,l)).return=r,r=i),s(r)):n(r,i)}}var bi=xi(!0),wi=xi(!1),ki=Ea(null),Si=null,Ei=null,Ci=null;function Pi(){Ci=Ei=Si=null}function Ni(e){var t=ki.current;Ca(ki),e._currentValue=t}function ji(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ti(e,t){Si=e,Ci=Ei=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(xs=!0),e.firstContext=null)}function Mi(e){var t=e._currentValue;if(Ci!==e)if(e={context:e,memoizedValue:t,next:null},null===Ei){if(null===Si)throw Error(i(308));Ei=e,Si.dependencies={lanes:0,firstContext:e}}else Ei=Ei.next=e;return t}var Li=null;function Ri(e){null===Li?Li=[e]:Li.push(e)}function Ai(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Ri(t)):(n.next=a.next,a.next=n),t.interleaved=n,Di(e,r)}function Di(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Vi=!1;function _i(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function zi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Oi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ii(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&jl)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Di(e,n)}return null===(a=r.interleaved)?(t.next=t,Ri(r)):(t.next=a.next,a.next=t),r.interleaved=t,Di(e,n)}function Fi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Bi(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?a=i=o:i=i.next=o,n=n.next}while(null!==n);null===i?a=i=t:i=i.next=t}else a=i=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ui(e,t,n,r){var a=e.updateQueue;Vi=!1;var i=a.firstBaseUpdate,o=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,u=l.next;l.next=null,null===o?i=u:o.next=u,o=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==i){var d=a.baseState;for(o=0,c=u=l=null,s=i;;){var f=s.lane,h=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=z({},d,f);break e;case 2:Vi=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,o|=f;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(f=s).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(l=d),a.baseState=l,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===i&&(a.shared.lanes=0);_l|=o,e.lanes=o,e.memoizedState=d}}function Hi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(i(191,a));a.call(r)}}}var Wi={},$i=Ea(Wi),Yi=Ea(Wi),Qi=Ea(Wi);function qi(e){if(e===Wi)throw Error(i(174));return e}function Xi(e,t){switch(Pa(Qi,t),Pa(Yi,e),Pa($i,Wi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ca($i),Pa($i,t)}function Gi(){Ca($i),Ca(Yi),Ca(Qi)}function Ki(e){qi(Qi.current);var t=qi($i.current),n=le(t,e.type);t!==n&&(Pa(Yi,e),Pa($i,n))}function Zi(e){Yi.current===e&&(Ca($i),Ca(Yi))}var Ji=Ea(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=b.ReactCurrentDispatcher,ao=b.ReactCurrentBatchConfig,io=0,oo=null,so=null,lo=null,uo=!1,co=!1,fo=0,ho=0;function po(){throw Error(i(321))}function mo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function go(e,t,n,r,a,o){if(io=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Jo:es,e=n(r,a),co){o=0;do{if(co=!1,fo=0,25<=o)throw Error(i(301));o+=1,lo=so=null,t.updateQueue=null,ro.current=ts,e=n(r,a)}while(co)}if(ro.current=Zo,t=null!==so&&null!==so.next,io=0,lo=so=oo=null,uo=!1,t)throw Error(i(300));return e}function vo(){var e=0!==fo;return fo=0,e}function yo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===lo?oo.memoizedState=lo=e:lo=lo.next=e,lo}function xo(){if(null===so){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=so.next;var t=null===lo?oo.memoizedState:lo.next;if(null!==t)lo=t,so=e;else{if(null===e)throw Error(i(310));e={memoizedState:(so=e).memoizedState,baseState:so.baseState,baseQueue:so.baseQueue,queue:so.queue,next:null},null===lo?oo.memoizedState=lo=e:lo=lo.next=e}return lo}function bo(e,t){return"function"===typeof t?t(e):t}function wo(e){var t=xo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=so,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var s=a.next;a.next=o.next,o.next=s}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var l=s=null,u=null,c=o;do{var d=c.lane;if((io&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,oo.lanes|=d,_l|=d}c=c.next}while(null!==c&&c!==o);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(xs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,_l|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ko(e){var t=xo(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{o=e(o,s.action),s=s.next}while(s!==a);sr(o,t.memoizedState)||(xs=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function So(){}function Eo(e,t){var n=oo,r=xo(),a=t(),o=!sr(r.memoizedState,a);if(o&&(r.memoizedState=a,xs=!0),r=r.queue,_o(No.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==lo&&1&lo.memoizedState.tag){if(n.flags|=2048,Lo(9,Po.bind(null,n,r,a,t),void 0,null),null===Tl)throw Error(i(349));0!==(30&io)||Co(n,t,a)}return a}function Co(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Po(e,t,n,r){t.value=n,t.getSnapshot=r,jo(t)&&To(e)}function No(e,t,n){return n(function(){jo(t)&&To(e)})}function jo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function To(e){var t=Di(e,1);null!==t&&nu(t,e,1,-1)}function Mo(e){var t=yo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:bo,lastRenderedState:e},t.queue=e,e=e.dispatch=qo.bind(null,oo,e),[t.memoizedState,e]}function Lo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ro(){return xo().memoizedState}function Ao(e,t,n,r){var a=yo();oo.flags|=e,a.memoizedState=Lo(1|t,n,void 0,void 0===r?null:r)}function Do(e,t,n,r){var a=xo();r=void 0===r?null:r;var i=void 0;if(null!==so){var o=so.memoizedState;if(i=o.destroy,null!==r&&mo(r,o.deps))return void(a.memoizedState=Lo(t,n,i,r))}oo.flags|=e,a.memoizedState=Lo(1|t,n,i,r)}function Vo(e,t){return Ao(8390656,8,e,t)}function _o(e,t){return Do(2048,8,e,t)}function zo(e,t){return Do(4,2,e,t)}function Oo(e,t){return Do(4,4,e,t)}function Io(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Fo(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Do(4,4,Io.bind(null,t,e),n)}function Bo(){}function Uo(e,t){var n=xo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ho(e,t){var n=xo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Wo(e,t,n){return 0===(21&io)?(e.baseState&&(e.baseState=!1,xs=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),oo.lanes|=n,_l|=n,e.baseState=!0),t)}function $o(e,t){var n=xt;xt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{xt=n,ao.transition=r}}function Yo(){return xo().memoizedState}function Qo(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Xo(e))Go(t,n);else if(null!==(n=Ai(e,t,n,r))){nu(n,e,r,eu()),Ko(n,t,r)}}function qo(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Xo(e))Go(t,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var o=t.lastRenderedState,s=i(o,n);if(a.hasEagerState=!0,a.eagerState=s,sr(s,o)){var l=t.interleaved;return null===l?(a.next=a,Ri(t)):(a.next=l.next,l.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Ai(e,t,a,r))&&(nu(n,e,r,a=eu()),Ko(n,t,r))}}function Xo(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Go(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ko(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Zo={readContext:Mi,useCallback:po,useContext:po,useEffect:po,useImperativeHandle:po,useInsertionEffect:po,useLayoutEffect:po,useMemo:po,useReducer:po,useRef:po,useState:po,useDebugValue:po,useDeferredValue:po,useTransition:po,useMutableSource:po,useSyncExternalStore:po,useId:po,unstable_isNewReconciler:!1},Jo={readContext:Mi,useCallback:function(e,t){return yo().memoizedState=[e,void 0===t?null:t],e},useContext:Mi,useEffect:Vo,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ao(4194308,4,Io.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ao(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ao(4,2,e,t)},useMemo:function(e,t){var n=yo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yo().memoizedState=e},useState:Mo,useDebugValue:Bo,useDeferredValue:function(e){return yo().memoizedState=e},useTransition:function(){var e=Mo(!1),t=e[0];return e=$o.bind(null,e[1]),yo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=yo();if(ai){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===Tl)throw Error(i(349));0!==(30&io)||Co(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Vo(No.bind(null,r,o,e),[e]),r.flags|=2048,Lo(9,Po.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=yo(),t=Tl.identifierPrefix;if(ai){var n=Ka;t=":"+t+"R"+(n=(Ga&~(1<<32-ot(Ga)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ho++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Mi,useCallback:Uo,useContext:Mi,useEffect:_o,useImperativeHandle:Fo,useInsertionEffect:zo,useLayoutEffect:Oo,useMemo:Ho,useReducer:wo,useRef:Ro,useState:function(){return wo(bo)},useDebugValue:Bo,useDeferredValue:function(e){return Wo(xo(),so.memoizedState,e)},useTransition:function(){return[wo(bo)[0],xo().memoizedState]},useMutableSource:So,useSyncExternalStore:Eo,useId:Yo,unstable_isNewReconciler:!1},ts={readContext:Mi,useCallback:Uo,useContext:Mi,useEffect:_o,useImperativeHandle:Fo,useInsertionEffect:zo,useLayoutEffect:Oo,useMemo:Ho,useReducer:ko,useRef:Ro,useState:function(){return ko(bo)},useDebugValue:Bo,useDeferredValue:function(e){var t=xo();return null===so?t.memoizedState=e:Wo(t,so.memoizedState,e)},useTransition:function(){return[ko(bo)[0],xo().memoizedState]},useMutableSource:So,useSyncExternalStore:Eo,useId:Yo,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=z({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:z({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return!!(e=e._reactInternals)&&Ue(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),i=Oi(r,a);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Ii(e,i,a))&&(nu(t,e,a,r),Fi(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),i=Oi(r,a);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Ii(e,i,a))&&(nu(t,e,a,r),Fi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Oi(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Ii(e,a,r))&&(nu(t,e,r,n),Fi(t,e,r))}};function is(e,t,n,r,a,i,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,o):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(a,i))}function os(e,t,n){var r=!1,a=Na,i=t.contextType;return"object"===typeof i&&null!==i?i=Mi(i):(a=Ra(t)?Ma:ja.current,i=(r=null!==(r=t.contextTypes)&&void 0!==r)?La(e,a):Na),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},_i(e);var i=t.contextType;"object"===typeof i&&null!==i?a.context=Mi(i):(i=Ra(t)?Ma:ja.current,a.context=La(e,i)),a.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(rs(e,t,i,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&as.enqueueReplaceState(a,a.state,null),Ui(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(i){a="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:a,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fs="function"===typeof WeakMap?WeakMap:Map;function hs(e,t,n){(n=Oi(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Wl||(Wl=!0,$l=r),ds(0,t)},n}function ps(e,t,n){(n=Oi(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ds(0,t)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===Yl?Yl=new Set([this]):Yl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Cu.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vs(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Oi(-1,1)).tag=2,Ii(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var ys=b.ReactCurrentOwner,xs=!1;function bs(e,t,n,r){t.child=null===e?wi(t,null,n,r):bi(t,e.child,n,r)}function ws(e,t,n,r,a){n=n.render;var i=t.ref;return Ti(t,a),r=go(e,t,n,r,i,a),n=vo(),null===e||xs?(ai&&n&&ei(t),t.flags|=1,bs(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function ks(e,t,n,r,a){if(null===e){var i=n.type;return"function"!==typeof i||Ru(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Du(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Ss(e,t,i,r,a))}if(i=e.child,0===(e.lanes&a)){var o=i.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(o,r)&&e.ref===t.ref)return Ws(e,t,a)}return t.flags|=1,(e=Au(i,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,a){if(null!==e){var i=e.memoizedProps;if(lr(i,r)&&e.ref===t.ref){if(xs=!1,t.pendingProps=r=i,0===(e.lanes&a))return t.lanes=e.lanes,Ws(e,t,a);0!==(131072&e.flags)&&(xs=!0)}}return Ps(e,t,n,r,a)}function Es(e,t,n){var r=t.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Pa(Al,Rl),Rl|=n;else{if(0===(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Pa(Al,Rl),Rl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==i?i.baseLanes:n,Pa(Al,Rl),Rl|=r}else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,Pa(Al,Rl),Rl|=r;return bs(e,t,a,n),t.child}function Cs(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ps(e,t,n,r,a){var i=Ra(n)?Ma:ja.current;return i=La(t,i),Ti(t,a),n=go(e,t,n,r,i,a),r=vo(),null===e||xs?(ai&&r&&ei(t),t.flags|=1,bs(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function Ns(e,t,n,r,a){if(Ra(n)){var i=!0;_a(t)}else i=!1;if(Ti(t,a),null===t.stateNode)Hs(e,t),os(t,n,r),ls(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,s=t.memoizedProps;o.props=s;var l=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=Mi(u):u=La(t,u=Ra(n)?Ma:ja.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==r||l!==u)&&ss(t,o,r,u),Vi=!1;var f=t.memoizedState;o.state=f,Ui(t,r,o,a),l=t.memoizedState,s!==r||f!==l||Ta.current||Vi?("function"===typeof c&&(rs(t,n,c,r),l=t.memoizedState),(s=Vi||is(t,n,s,r,f,l,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=s):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,zi(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ns(t.type,s),o.props=u,d=t.pendingProps,f=o.context,"object"===typeof(l=n.contextType)&&null!==l?l=Mi(l):l=La(t,l=Ra(n)?Ma:ja.current);var h=n.getDerivedStateFromProps;(c="function"===typeof h||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,o,r,l),Vi=!1,f=t.memoizedState,o.state=f,Ui(t,r,o,a);var p=t.memoizedState;s!==d||f!==p||Ta.current||Vi?("function"===typeof h&&(rs(t,n,h,r),p=t.memoizedState),(u=Vi||is(t,n,u,r,f,p,l)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,p,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),o.props=r,o.state=p,o.context=l,r=u):("function"!==typeof o.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return js(e,t,n,r,i,a)}function js(e,t,n,r,a,i){Cs(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&za(t,n,!1),Ws(e,t,i);r=t.stateNode,ys.current=t;var s=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=bi(t,e.child,null,i),t.child=bi(t,null,s,i)):bs(e,t,s,i),t.memoizedState=r.state,a&&za(t,n,!0),t.child}function Ts(e){var t=e.stateNode;t.pendingContext?Da(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Da(0,t.context,!1),Xi(e,t.containerInfo)}function Ms(e,t,n,r,a){return hi(),pi(a),t.flags|=256,bs(e,t,n,r),t.child}var Ls,Rs,As,Ds,Vs={dehydrated:null,treeContext:null,retryLane:0};function _s(e){return{baseLanes:e,cachePool:null,transitions:null}}function zs(e,t,n){var r,a=t.pendingProps,o=Ji.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Pa(Ji,1&o),null===e)return ui(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=a.children,e=a.fallback,s?(a=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&a)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=_u(l,a,0,null),e=Vu(e,a,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=_s(n),t.memoizedState=Vs,e):Os(t,l));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,s){if(n)return 256&t.flags?(t.flags&=-257,Is(e,t,s,r=cs(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=_u({mode:"visible",children:r.children},a,0,null),(o=Vu(o,a,s,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&bi(t,e.child,null,s),t.child.memoizedState=_s(s),t.memoizedState=Vs,o);if(0===(1&t.mode))return Is(e,t,s,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var l=r.dgst;return r=l,Is(e,t,s,r=cs(o=Error(i(419)),r,void 0))}if(l=0!==(s&e.childLanes),xs||l){if(null!==(r=Tl)){switch(s&-s){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|s))?0:a)&&a!==o.retryLane&&(o.retryLane=a,Di(e,a),nu(r,e,a,-1))}return mu(),Is(e,t,s,r=cs(Error(i(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Nu.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,ri=ua(a.nextSibling),ni=t,ai=!0,ii=null,null!==e&&(Qa[qa++]=Ga,Qa[qa++]=Ka,Qa[qa++]=Xa,Ga=e.id,Ka=e.overflow,Xa=t),t=Os(t,r.children),t.flags|=4096,t)}(e,t,l,a,r,o,n);if(s){s=a.fallback,l=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&l)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Au(o,u)).subtreeFlags=14680064&o.subtreeFlags,null!==r?s=Au(r,s):(s=Vu(s,l,n,null)).flags|=2,s.return=t,a.return=t,a.sibling=s,t.child=a,a=s,s=t.child,l=null===(l=e.child.memoizedState)?_s(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Vs,a}return e=(s=e.child).sibling,a=Au(s,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Os(e,t){return(t=_u({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Is(e,t,n,r){return null!==r&&pi(r),bi(t,e.child,null,n),(e=Os(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Fs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ji(e.return,t,n)}function Bs(e,t,n,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function Us(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(bs(e,t,r.children,n),0!==(2&(r=Ji.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Fs(e,n,t);else if(19===e.tag)Fs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Pa(Ji,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bs(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bs(t,!0,n,null,i);break;case"together":Bs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hs(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ws(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),_l|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Au(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Au(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $s(e,t){if(!ai)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ys(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qs(e,t,n){var r=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ys(t),null;case 1:case 17:return Ra(t.type)&&Aa(),Ys(t),null;case 3:return r=t.stateNode,Gi(),Ca(Ta),Ca(ja),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(di(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ii&&(ou(ii),ii=null))),Rs(e,t),Ys(t),null;case 5:Zi(t);var a=qi(Qi.current);if(n=t.type,null!==e&&null!=t.stateNode)As(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(i(166));return Ys(t),null}if(e=qi($i.current),di(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[ha]=o,e=0!==(1&t.mode),n){case"dialog":Ir("cancel",r),Ir("close",r);break;case"iframe":case"object":case"embed":Ir("load",r);break;case"video":case"audio":for(a=0;a<Vr.length;a++)Ir(Vr[a],r);break;case"source":Ir("error",r);break;case"img":case"image":case"link":Ir("error",r),Ir("load",r);break;case"details":Ir("toggle",r);break;case"input":G(r,o),Ir("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Ir("invalid",r);break;case"textarea":ae(r,o),Ir("invalid",r)}for(var l in ye(n,o),a=null,o)if(o.hasOwnProperty(l)){var u=o[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&Zr(r.textContent,u,e),a=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&Ir("scroll",r)}switch(n){case"input":Y(r),J(r,o,!0);break;case"textarea":Y(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=Jr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fa]=t,e[ha]=r,Ls(e,t,!1,!1),t.stateNode=e;e:{switch(l=xe(n,r),n){case"dialog":Ir("cancel",e),Ir("close",e),a=r;break;case"iframe":case"object":case"embed":Ir("load",e),a=r;break;case"video":case"audio":for(a=0;a<Vr.length;a++)Ir(Vr[a],e);a=r;break;case"source":Ir("error",e),a=r;break;case"img":case"image":case"link":Ir("error",e),Ir("load",e),a=r;break;case"details":Ir("toggle",e),a=r;break;case"input":G(e,r),a=X(e,r),Ir("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=z({},r,{value:void 0}),Ir("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ir("invalid",e)}for(o in ye(n,a),u=a)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?ge(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(s.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Ir("scroll",e):null!=c&&x(e,o,c,l))}switch(n){case"input":Y(e),J(e,r,!1);break;case"textarea":Y(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ys(t),null;case 6:if(e&&null!=t.stateNode)Ds(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(n=qi(Qi.current),qi($i.current),di(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=ni))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Ys(t),null;case 13:if(Ca(Ji),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ai&&null!==ri&&0!==(1&t.mode)&&0===(128&t.flags))fi(),hi(),t.flags|=98560,o=!1;else if(o=di(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(i(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(i(317));o[fa]=t}else hi(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ys(t),o=!1}else null!==ii&&(ou(ii),ii=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Ji.current)?0===Dl&&(Dl=3):mu())),null!==t.updateQueue&&(t.flags|=4),Ys(t),null);case 4:return Gi(),Rs(e,t),null===e&&Ur(t.stateNode.containerInfo),Ys(t),null;case 10:return Ni(t.type._context),Ys(t),null;case 19:if(Ca(Ji),null===(o=t.memoizedState))return Ys(t),null;if(r=0!==(128&t.flags),null===(l=o.rendering))if(r)$s(o,!1);else{if(0!==Dl||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=eo(e))){for(t.flags|=128,$s(o,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(l=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=l.childLanes,o.lanes=l.lanes,o.child=l.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=l.memoizedProps,o.memoizedState=l.memoizedState,o.updateQueue=l.updateQueue,o.type=l.type,e=l.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Pa(Ji,1&Ji.current|2),t.child}e=e.sibling}null!==o.tail&&Ke()>Ul&&(t.flags|=128,r=!0,$s(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$s(o,!0),null===o.tail&&"hidden"===o.tailMode&&!l.alternate&&!ai)return Ys(t),null}else 2*Ke()-o.renderingStartTime>Ul&&1073741824!==n&&(t.flags|=128,r=!0,$s(o,!1),t.lanes=4194304);o.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=o.last)?n.sibling=l:t.child=l,o.last=l)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ke(),t.sibling=null,n=Ji.current,Pa(Ji,r?1&n|2:1&n),t):(Ys(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rl)&&(Ys(t),6&t.subtreeFlags&&(t.flags|=8192)):Ys(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function qs(e,t){switch(ti(t),t.tag){case 1:return Ra(t.type)&&Aa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Gi(),Ca(Ta),Ca(ja),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zi(t),null;case 13:if(Ca(Ji),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));hi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ca(Ji),null;case 4:return Gi(),null;case 10:return Ni(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ls=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Rs=function(){},As=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,qi($i.current);var i,o=null;switch(n){case"input":a=X(e,a),r=X(e,r),o=[];break;case"select":a=z({},a,{value:void 0}),r=z({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Jr)}for(c in ye(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var l=a[c];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(i in l)!l.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in u)u.hasOwnProperty(i)&&l[i]!==u[i]&&(n||(n={}),n[i]=u[i])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ir("scroll",e),o||l===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},Ds=function(e,t,n,r){n!==r&&(t.flags|=4)};var Xs=!1,Gs=!1,Ks="function"===typeof WeakSet?WeakSet:Set,Zs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&el(t,n,i)}a=a.next}while(a!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function al(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function il(e){var t=e.alternate;null!==t&&(e.alternate=null,il(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[ha],delete t[ma],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ol(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ol(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(it&&"function"===typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(at,n)}catch(s){}switch(n.tag){case 5:Gs||Js(n,t);case 6:var r=cl,a=dl;cl=null,fl(e,t,n),dl=a,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?la(e.parentNode,n):1===e.nodeType&&la(e,n),Ut(e)):la(cl,n.stateNode));break;case 4:r=cl,a=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=a;break;case 0:case 11:case 14:case 15:if(!Gs&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var i=a,o=i.destroy;i=i.tag,void 0!==o&&(0!==(2&i)||0!==(4&i))&&el(n,t,o),a=a.next}while(a!==r)}fl(e,t,n);break;case 1:if(!Gs&&(Js(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Eu(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Gs=(r=Gs)||null!==n.memoizedState,fl(e,t,n),Gs=r):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Ks),t.forEach(function(t){var r=ju.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(i(160));hl(o,s,a),cl=null,dl=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Eu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),vl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(g){Eu(e,e.return,g)}try{nl(5,e,e.return)}catch(g){Eu(e,e.return,g)}}break;case 1:ml(t,e),vl(e),512&r&&null!==n&&Js(n,n.return);break;case 5:if(ml(t,e),vl(e),512&r&&null!==n&&Js(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Eu(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,s=null!==n?n.memoizedProps:o,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===o.type&&null!=o.name&&K(a,o),xe(l,s);var c=xe(l,o);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):x(a,d,f,c)}switch(l){case"input":Z(a,o);break;case"textarea":ie(a,o);break;case"select":var h=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var p=o.value;null!=p?ne(a,!!o.multiple,p,!1):h!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[ha]=o}catch(g){Eu(e,e.return,g)}}break;case 6:if(ml(t,e),vl(e),4&r){if(null===e.stateNode)throw Error(i(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(g){Eu(e,e.return,g)}}break;case 3:if(ml(t,e),vl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(g){Eu(e,e.return,g)}break;case 4:default:ml(t,e),vl(e);break;case 13:ml(t,e),vl(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Bl=Ke())),4&r&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Gs=(c=Gs)||d,ml(t,e),Gs=c):ml(t,e),vl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Zs=e,d=e.child;null!==d;){for(f=Zs=d;null!==Zs;){switch(p=(h=Zs).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Js(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Eu(r,n,g)}}break;case 5:Js(h,h.return);break;case 22:if(null!==h.memoizedState){wl(f);continue}}null!==p?(p.return=h,Zs=p):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",s))}catch(g){Eu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Eu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),vl(e),4&r&&pl(e);case 21:}}function vl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ol(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),ul(e,sl(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ll(e,sl(e),o);break;default:throw Error(i(161))}}catch(s){Eu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yl(e,t,n){Zs=e,xl(e,t,n)}function xl(e,t,n){for(var r=0!==(1&e.mode);null!==Zs;){var a=Zs,i=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Xs;if(!o){var s=a.alternate,l=null!==s&&null!==s.memoizedState||Gs;s=Xs;var u=Gs;if(Xs=o,(Gs=l)&&!u)for(Zs=a;null!==Zs;)l=(o=Zs).child,22===o.tag&&null!==o.memoizedState?kl(a):null!==l?(l.return=o,Zs=l):kl(a);for(;null!==i;)Zs=i,xl(i,t,n),i=i.sibling;Zs=a,Xs=s,Gs=u}bl(e)}else 0!==(8772&a.subtreeFlags)&&null!==i?(i.return=a,Zs=i):bl(e)}}function bl(e){for(;null!==Zs;){var t=Zs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Gs||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Gs)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Hi(t,o,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Hi(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ut(f)}}}break;default:throw Error(i(163))}Gs||512&t.flags&&al(t)}catch(h){Eu(t,t.return,h)}}if(t===e){Zs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zs=n;break}Zs=t.return}}function wl(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function kl(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Eu(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(l){Eu(t,a,l)}}var i=t.return;try{al(t)}catch(l){Eu(t,i,l)}break;case 5:var o=t.return;try{al(t)}catch(l){Eu(t,o,l)}}}catch(l){Eu(t,t.return,l)}if(t===e){Zs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Zs=s;break}Zs=t.return}}var Sl,El=Math.ceil,Cl=b.ReactCurrentDispatcher,Pl=b.ReactCurrentOwner,Nl=b.ReactCurrentBatchConfig,jl=0,Tl=null,Ml=null,Ll=0,Rl=0,Al=Ea(0),Dl=0,Vl=null,_l=0,zl=0,Ol=0,Il=null,Fl=null,Bl=0,Ul=1/0,Hl=null,Wl=!1,$l=null,Yl=null,Ql=!1,ql=null,Xl=0,Gl=0,Kl=null,Zl=-1,Jl=0;function eu(){return 0!==(6&jl)?Ke():-1!==Zl?Zl:Zl=Ke()}function tu(e){return 0===(1&e.mode)?1:0!==(2&jl)&&0!==Ll?Ll&-Ll:null!==mi.transition?(0===Jl&&(Jl=mt()),Jl):0!==(e=xt)?e:e=void 0===(e=window.event)?16:Gt(e.type)}function nu(e,t,n,r){if(50<Gl)throw Gl=0,Kl=null,Error(i(185));vt(e,n,r),0!==(2&jl)&&e===Tl||(e===Tl&&(0===(2&jl)&&(zl|=n),4===Dl&&su(e,Ll)),ru(e,r),1===n&&0===jl&&0===(1&t.mode)&&(Ul=Ke()+500,Ia&&Ua()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-ot(i),s=1<<o,l=a[o];-1===l?0!==(s&n)&&0===(s&r)||(a[o]=ht(s,t)):l<=t&&(e.expiredLanes|=s),i&=~s}}(e,t);var r=ft(e,e===Tl?Ll:0);if(0===r)null!==n&&qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&qe(n),1===t)0===e.tag?function(e){Ia=!0,Ba(e)}(lu.bind(null,e)):Ba(lu.bind(null,e)),oa(function(){0===(6&jl)&&Ua()}),n=null;else{switch(bt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Tu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Zl=-1,Jl=0,0!==(6&jl))throw Error(i(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===Tl?Ll:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=jl;jl|=2;var o=pu();for(Tl===e&&Ll===t||(Hl=null,Ul=Ke()+500,fu(e,t));;)try{yu();break}catch(l){hu(e,l)}Pi(),Cl.current=o,jl=a,null!==Ml?t=0:(Tl=null,Ll=0,t=Dl)}if(0!==t){if(2===t&&(0!==(a=pt(e))&&(r=a,t=iu(e,a))),1===t)throw n=Vl,fu(e,0),su(e,r),ru(e,Ke()),n;if(6===t)su(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!sr(i(),a))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gu(e,r))&&(0!==(o=pt(e))&&(r=o,t=iu(e,o))),1===t))throw n=Vl,fu(e,0),su(e,r),ru(e,Ke()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:case 5:wu(e,Fl,Hl);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Bl+500-Ke())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,Fl,Hl),t);break}wu(e,Fl,Hl);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var s=31-ot(r);o=1<<s,(s=t[s])>a&&(a=s),r&=~o}if(r=a,10<(r=(120>(r=Ke()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*El(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,Fl,Hl),r);break}wu(e,Fl,Hl);break;default:throw Error(i(329))}}}return ru(e,Ke()),e.callbackNode===n?au.bind(null,e):null}function iu(e,t){var n=Il;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Fl,Fl=n,null!==t&&ou(t)),e}function ou(e){null===Fl?Fl=e:Fl.push.apply(Fl,e)}function su(e,t){for(t&=~Ol,t&=~zl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&jl))throw Error(i(327));ku();var t=ft(e,0);if(0===(1&t))return ru(e,Ke()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=iu(e,r))}if(1===n)throw n=Vl,fu(e,0),su(e,t),ru(e,Ke()),n;if(6===n)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Fl,Hl),ru(e,Ke()),null}function uu(e,t){var n=jl;jl|=1;try{return e(t)}finally{0===(jl=n)&&(Ul=Ke()+500,Ia&&Ua())}}function cu(e){null!==ql&&0===ql.tag&&0===(6&jl)&&ku();var t=jl;jl|=1;var n=Nl.transition,r=xt;try{if(Nl.transition=null,xt=1,e)return e()}finally{xt=r,Nl.transition=n,0===(6&(jl=t))&&Ua()}}function du(){Rl=Al.current,Ca(Al)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Ml)for(n=Ml.return;null!==n;){var r=n;switch(ti(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Aa();break;case 3:Gi(),Ca(Ta),Ca(ja),no();break;case 5:Zi(r);break;case 4:Gi();break;case 13:case 19:Ca(Ji);break;case 10:Ni(r.type._context);break;case 22:case 23:du()}n=n.return}if(Tl=e,Ml=e=Au(e.current,null),Ll=Rl=t,Dl=0,Vl=null,Ol=zl=_l=0,Fl=Il=null,null!==Li){for(t=0;t<Li.length;t++)if(null!==(r=(n=Li[t]).interleaved)){n.interleaved=null;var a=r.next,i=n.pending;if(null!==i){var o=i.next;i.next=a,r.next=o}n.pending=r}Li=null}return e}function hu(e,t){for(;;){var n=Ml;try{if(Pi(),ro.current=Zo,uo){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}uo=!1}if(io=0,lo=so=oo=null,co=!1,fo=0,Pl.current=null,null===n||null===n.return){Dl=1,Vl=t,Ml=null;break}e:{var o=e,s=n.return,l=n,u=t;if(t=Ll,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=gs(s);if(null!==p){p.flags&=-257,vs(p,s,l,0,t),1&p.mode&&ms(o,c,t),u=c;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){ms(o,c,t),mu();break e}u=Error(i(426))}else if(ai&&1&l.mode){var v=gs(s);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vs(v,s,l,0,t),pi(us(u,l));break e}}o=u=us(u,l),4!==Dl&&(Dl=2),null===Il?Il=[o]:Il.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Bi(o,hs(0,u,t));break e;case 1:l=u;var y=o.type,x=o.stateNode;if(0===(128&o.flags)&&("function"===typeof y.getDerivedStateFromError||null!==x&&"function"===typeof x.componentDidCatch&&(null===Yl||!Yl.has(x)))){o.flags|=65536,t&=-t,o.lanes|=t,Bi(o,ps(o,l,t));break e}}o=o.return}while(null!==o)}bu(n)}catch(b){t=b,Ml===n&&null!==n&&(Ml=n=n.return);continue}break}}function pu(){var e=Cl.current;return Cl.current=Zo,null===e?Zo:e}function mu(){0!==Dl&&3!==Dl&&2!==Dl||(Dl=4),null===Tl||0===(268435455&_l)&&0===(268435455&zl)||su(Tl,Ll)}function gu(e,t){var n=jl;jl|=2;var r=pu();for(Tl===e&&Ll===t||(Hl=null,fu(e,t));;)try{vu();break}catch(a){hu(e,a)}if(Pi(),jl=n,Cl.current=r,null!==Ml)throw Error(i(261));return Tl=null,Ll=0,Dl}function vu(){for(;null!==Ml;)xu(Ml)}function yu(){for(;null!==Ml&&!Xe();)xu(Ml)}function xu(e){var t=Sl(e.alternate,e,Rl);e.memoizedProps=e.pendingProps,null===t?bu(e):Ml=t,Pl.current=null}function bu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qs(n,t,Rl)))return void(Ml=n)}else{if(null!==(n=qs(n,t)))return n.flags&=32767,void(Ml=n);if(null===e)return Dl=6,void(Ml=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ml=t);Ml=t=e}while(null!==t);0===Dl&&(Dl=5)}function wu(e,t,n){var r=xt,a=Nl.transition;try{Nl.transition=null,xt=1,function(e,t,n,r){do{ku()}while(null!==ql);if(0!==(6&jl))throw Error(i(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),i=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~i}}(e,o),e===Tl&&(Ml=Tl=null,Ll=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ql||(Ql=!0,Tu(tt,function(){return ku(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Nl.transition,Nl.transition=null;var s=xt;xt=1;var l=jl;jl|=4,Pl.current=null,function(e,t){if(ea=Wt,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(l=s+a),f!==o||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===a&&(l=s),h===o&&++d===r&&(u=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Wt=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,x=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:ns(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(i(163))}}catch(w){Eu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}m=tl,tl=!1}(e,n),gl(n,e),pr(ta),Wt=!!ea,ta=ea=null,e.current=n,yl(n,e,a),Ge(),jl=l,xt=s,Nl.transition=o}else e.current=n;if(Ql&&(Ql=!1,ql=e,Xl=a),o=e.pendingLanes,0===o&&(Yl=null),function(e){if(it&&"function"===typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ke()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Wl)throw Wl=!1,e=$l,$l=null,e;0!==(1&Xl)&&0!==e.tag&&ku(),o=e.pendingLanes,0!==(1&o)?e===Kl?Gl++:(Gl=0,Kl=e):Gl=0,Ua()}(e,t,n,r)}finally{Nl.transition=a,xt=r}return null}function ku(){if(null!==ql){var e=bt(Xl),t=Nl.transition,n=xt;try{if(Nl.transition=null,xt=16>e?16:e,null===ql)var r=!1;else{if(e=ql,ql=null,Xl=0,0!==(6&jl))throw Error(i(331));var a=jl;for(jl|=4,Zs=e.current;null!==Zs;){var o=Zs,s=o.child;if(0!==(16&Zs.flags)){var l=o.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Zs=c;null!==Zs;){var d=Zs;switch(d.tag){case 0:case 11:case 15:nl(8,d,o)}var f=d.child;if(null!==f)f.return=d,Zs=f;else for(;null!==Zs;){var h=(d=Zs).sibling,p=d.return;if(il(d),d===c){Zs=null;break}if(null!==h){h.return=p,Zs=h;break}Zs=p}}}var m=o.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Zs=o}}if(0!==(2064&o.subtreeFlags)&&null!==s)s.return=o,Zs=s;else e:for(;null!==Zs;){if(0!==(2048&(o=Zs).flags))switch(o.tag){case 0:case 11:case 15:nl(9,o,o.return)}var y=o.sibling;if(null!==y){y.return=o.return,Zs=y;break e}Zs=o.return}}var x=e.current;for(Zs=x;null!==Zs;){var b=(s=Zs).child;if(0!==(2064&s.subtreeFlags)&&null!==b)b.return=s,Zs=b;else e:for(s=x;null!==Zs;){if(0!==(2048&(l=Zs).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(k){Eu(l,l.return,k)}if(l===s){Zs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Zs=w;break e}Zs=l.return}}if(jl=a,Ua(),it&&"function"===typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(at,e)}catch(k){}r=!0}return r}finally{xt=n,Nl.transition=t}}return!1}function Su(e,t,n){e=Ii(e,t=hs(0,t=us(n,t),1),1),t=eu(),null!==e&&(vt(e,1,t),ru(e,t))}function Eu(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Yl||!Yl.has(r))){t=Ii(t,e=ps(t,e=us(n,e),1),1),e=eu(),null!==t&&(vt(t,1,e),ru(t,e));break}}t=t.return}}function Cu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Tl===e&&(Ll&n)===n&&(4===Dl||3===Dl&&(130023424&Ll)===Ll&&500>Ke()-Bl?fu(e,0):Ol|=n),ru(e,t)}function Pu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Di(e,t))&&(vt(e,t,n),ru(e,n))}function Nu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Pu(e,n)}function ju(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}null!==r&&r.delete(t),Pu(e,n)}function Tu(e,t){return Qe(e,t)}function Mu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lu(e,t,n,r){return new Mu(e,t,n,r)}function Ru(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Au(e,t){var n=e.alternate;return null===n?((n=Lu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Du(e,t,n,r,a,o){var s=2;if(r=e,"function"===typeof e)Ru(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case S:return Vu(n.children,a,o,t);case E:s=8,a|=8;break;case C:return(e=Lu(12,n,t,2|a)).elementType=C,e.lanes=o,e;case T:return(e=Lu(13,n,t,a)).elementType=T,e.lanes=o,e;case M:return(e=Lu(19,n,t,a)).elementType=M,e.lanes=o,e;case A:return _u(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case P:s=10;break e;case N:s=9;break e;case j:s=11;break e;case L:s=14;break e;case R:s=16,r=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Lu(s,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Vu(e,t,n,r){return(e=Lu(7,e,r,t)).lanes=n,e}function _u(e,t,n,r){return(e=Lu(22,e,r,t)).elementType=A,e.lanes=n,e.stateNode={isHidden:!1},e}function zu(e,t,n){return(e=Lu(6,e,null,t)).lanes=n,e}function Ou(e,t,n){return(t=Lu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Iu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Fu(e,t,n,r,a,i,o,s,l){return e=new Iu(e,t,n,s,l),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Lu(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},_i(i),e}function Bu(e){if(!e)return Na;e:{if(Ue(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ra(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var n=e.type;if(Ra(n))return Va(e,n,t)}return t}function Uu(e,t,n,r,a,i,o,s,l){return(e=Fu(n,r,!0,e,0,i,0,s,l)).context=Bu(null),n=e.current,(i=Oi(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Ii(n,i,a),e.current.lanes=a,vt(e,a,r),ru(e,r),e}function Hu(e,t,n,r){var a=t.current,i=eu(),o=tu(a);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Oi(i,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ii(a,t,o))&&(nu(e,a,o,i),Fi(e,a,o)),o}function Wu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $u(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Yu(e,t){$u(e,t),(e=e.alternate)&&$u(e,t)}Sl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ta.current)xs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return xs=!1,function(e,t,n){switch(t.tag){case 3:Ts(t),hi();break;case 5:Ki(t);break;case 1:Ra(t.type)&&_a(t);break;case 4:Xi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Pa(ki,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Pa(Ji,1&Ji.current),t.flags|=128,null):0!==(n&t.child.childLanes)?zs(e,t,n):(Pa(Ji,1&Ji.current),null!==(e=Ws(e,t,n))?e.sibling:null);Pa(Ji,1&Ji.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Us(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Pa(Ji,Ji.current),r)break;return null;case 22:case 23:return t.lanes=0,Es(e,t,n)}return Ws(e,t,n)}(e,t,n);xs=0!==(131072&e.flags)}else xs=!1,ai&&0!==(1048576&t.flags)&&Ja(t,Ya,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Hs(e,t),e=t.pendingProps;var a=La(t,ja.current);Ti(t,n),a=go(null,t,r,e,a,n);var o=vo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ra(r)?(o=!0,_a(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,_i(t),a.updater=as,t.stateNode=a,a._reactInternals=t,ls(t,r,e,n),t=js(null,t,r,!0,o,n)):(t.tag=0,ai&&o&&ei(t),bs(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Hs(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Ru(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===j)return 11;if(e===L)return 14}return 2}(r),e=ns(r,e),a){case 0:t=Ps(null,t,r,e,n);break e;case 1:t=Ns(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=ks(null,t,r,ns(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ps(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 1:return r=t.type,a=t.pendingProps,Ns(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 3:e:{if(Ts(t),null===e)throw Error(i(387));r=t.pendingProps,a=(o=t.memoizedState).element,zi(e,t),Ui(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ms(e,t,r,n,a=us(Error(i(423)),t));break e}if(r!==a){t=Ms(e,t,r,n,a=us(Error(i(424)),t));break e}for(ri=ua(t.stateNode.containerInfo.firstChild),ni=t,ai=!0,ii=null,n=wi(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(hi(),r===a){t=Ws(e,t,n);break e}bs(e,t,r,n)}t=t.child}return t;case 5:return Ki(t),null===e&&ui(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,s=a.children,na(r,a)?s=null:null!==o&&na(r,o)&&(t.flags|=32),Cs(e,t),bs(e,t,s,n),t.child;case 6:return null===e&&ui(t),null;case 13:return zs(e,t,n);case 4:return Xi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=bi(t,null,r,n):bs(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,ws(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 7:return bs(e,t,t.pendingProps,n),t.child;case 8:case 12:return bs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,s=a.value,Pa(ki,r._currentValue),r._currentValue=s,null!==o)if(sr(o.value,s)){if(o.children===a.children&&!Ta.current){t=Ws(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var l=o.dependencies;if(null!==l){s=o.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=Oi(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),ji(o.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===o.tag)s=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(s=o.return))throw Error(i(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),ji(s,n,t),s=o.sibling}else s=o.child;if(null!==s)s.return=o;else for(s=o;null!==s;){if(s===t){s=null;break}if(null!==(o=s.sibling)){o.return=s.return,s=o;break}s=s.return}o=s}bs(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ti(t,n),r=r(a=Mi(a)),t.flags|=1,bs(e,t,r,n),t.child;case 14:return a=ns(r=t.type,t.pendingProps),ks(e,t,r,a=ns(r.type,a),n);case 15:return Ss(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ns(r,a),Hs(e,t),t.tag=1,Ra(r)?(e=!0,_a(t)):e=!1,Ti(t,n),os(t,r,a),ls(t,r,a,n),js(null,t,r,!0,e,n);case 19:return Us(e,t,n);case 22:return Es(e,t,n)}throw Error(i(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function qu(e){this._internalRoot=e}function Xu(e){this._internalRoot=e}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Ku(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function Ju(e,t,n,r,a){var i=n._reactRootContainer;if(i){var o=i;if("function"===typeof a){var s=a;a=function(){var e=Wu(o);s.call(e)}}Hu(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var i=r;r=function(){var e=Wu(o);i.call(e)}}var o=Uu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=o,e[pa]=o.current,Ur(8===e.nodeType?e.parentNode:e),cu(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var s=r;r=function(){var e=Wu(l);s.call(e)}}var l=Fu(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=l,e[pa]=l.current,Ur(8===e.nodeType?e.parentNode:e),cu(function(){Hu(t,l,n,r)}),l}(n,t,e,a,r);return Wu(o)}Xu.prototype.render=qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Hu(e,t,null,null)},Xu.prototype.unmount=qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Hu(null,e,null,null)}),t[pa]=null}},Xu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<At.length&&0!==t&&t<At[n].priority;n++);At.splice(n,0,e),0===n&&zt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ru(t,Ke()),0===(6&jl)&&(Ul=Ke()+500,Ua()))}break;case 13:cu(function(){var t=Di(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Yu(e,1)}},kt=function(e){if(13===e.tag){var t=Di(e,134217728);if(null!==t)nu(t,e,134217728,eu());Yu(e,134217728)}},St=function(e){if(13===e.tag){var t=tu(e),n=Di(e,t);if(null!==n)nu(n,e,t,eu());Yu(e,t)}},Et=function(){return xt},Ct=function(e,t){var n=xt;try{return xt=e,t()}finally{xt=n}},ke=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(i(90));Q(r),Z(r,a)}}}break;case"textarea":ie(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},je=uu,Te=cu;var ec={usingClientEntryPoint:!1,Events:[xa,ba,wa,Pe,Ne,uu]},tc={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),it=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gu(t))throw Error(i(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Gu(e))throw Error(i(299));var n=!1,r="",a=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Fu(e,1,!1,null,0,n,0,r,a),e[pa]=t.current,Ur(8===e.nodeType?e.parentNode:e),new qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Ku(t))throw Error(i(200));return Ju(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Gu(e))throw Error(i(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",s=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Uu(t,null,e,1,null!=n?n:null,a,0,o,s),e[pa]=t.current,Ur(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Xu(t)},t.render=function(e,t,n){if(!Ku(t))throw Error(i(200));return Ju(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Ku(e))throw Error(i(40));return!!e._reactRootContainer&&(cu(function(){Ju(null,null,e,!1,function(){e._reactRootContainer=null,e[pa]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ku(n))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return Ju(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}var r=n(43),a=n(391);const i="undefined"!==typeof document,o=i?r.useLayoutEffect:r.useEffect;function s(){const e=(0,r.useRef)(!1);return o(()=>(e.current=!0,()=>{e.current=!1}),[]),e}const l=e=>e;class u{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}const c=["prepare","read","update","preRender","render","postRender"];const{schedule:d,cancel:f,state:h,steps:p}=function(e,t){let n=!1,r=!0;const a={delta:0,timestamp:0,isProcessing:!1},i=c.reduce((e,t)=>(e[t]=function(e){let t=new u,n=new u,r=0,a=!1,i=!1;const o=new WeakSet,s={schedule:function(e){const i=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&a,s=i?t:n;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&o.add(e),s.add(e)&&i&&a&&(r=t.order.length),e},cancel:e=>{n.remove(e),o.delete(e)},process:l=>{if(a)i=!0;else{if(a=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let n=0;n<r;n++){const r=t.order[n];r(l),o.has(r)&&(s.schedule(r),e())}a=!1,i&&(i=!1,s.process(l))}}};return s}(()=>n=!0),e),{}),o=e=>i[e].process(a),s=()=>{const i=performance.now();n=!1,a.delta=r?1e3/60:Math.max(Math.min(i-a.timestamp,40),1),a.timestamp=i,a.isProcessing=!0,c.forEach(o),a.isProcessing=!1,n&&t&&(r=!1,e(s))},l=c.reduce((t,o)=>{const l=i[o];return t[o]=function(t){let i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,a.isProcessing||e(s)),l.schedule(t,i,o)},t},{});return{schedule:l,cancel:e=>c.forEach(t=>i[t].cancel(e)),state:a,steps:i}}("undefined"!==typeof requestAnimationFrame?requestAnimationFrame:l,!0);const m=(0,r.createContext)(null);function g(e){const t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}class v extends r.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function y(e){let{children:t,isPresent:n}=e;const a=(0,r.useId)(),i=(0,r.useRef)(null),o=(0,r.useRef)({width:0,height:0,top:0,left:0});return(0,r.useInsertionEffect)(()=>{const{width:e,height:t,top:r,left:s}=o.current;if(n||!i.current||!e||!t)return;i.current.dataset.motionPopId=a;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[n]),r.createElement(v,{isPresent:n,childRef:i,sizeRef:o},r.cloneElement(t,{ref:i}))}const x=e=>{let{children:t,initial:n,isPresent:a,onExitComplete:i,custom:o,presenceAffectsLayout:s,mode:l}=e;const u=g(b),c=(0,r.useId)(),d=(0,r.useMemo)(()=>({id:c,initial:n,isPresent:a,custom:o,onExitComplete:e=>{u.set(e,!0);for(const t of u.values())if(!t)return;i&&i()},register:e=>(u.set(e,!1),()=>u.delete(e))}),s?void 0:[a]);return(0,r.useMemo)(()=>{u.forEach((e,t)=>u.set(t,!1))},[a]),r.useEffect(()=>{!a&&!u.size&&i&&i()},[a]),"popLayout"===l&&(t=r.createElement(y,{isPresent:a},t)),r.createElement(m.Provider,{value:d},t)};function b(){return new Map}const w=(0,r.createContext)({});let k=l,S=l;const E=e=>e.key||"";const C=e=>{let{children:t,custom:n,initial:a=!0,onExitComplete:i,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:c="sync"}=e;S(!l,"Replace exitBeforeEnter with mode='wait'");const f=(0,r.useContext)(w).forceRender||function(){const e=s(),[t,n]=(0,r.useState)(0),a=(0,r.useCallback)(()=>{e.current&&n(t+1)},[t]);return[(0,r.useCallback)(()=>d.postRender(a),[a]),t]}()[0],h=s(),p=function(e){const t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}(t);let m=p;const g=(0,r.useRef)(new Map).current,v=(0,r.useRef)(m),y=(0,r.useRef)(new Map).current,b=(0,r.useRef)(!0);var k;if(o(()=>{b.current=!1,function(e,t){e.forEach(e=>{const n=E(e);t.set(n,e)})}(p,y),v.current=m}),k=()=>{b.current=!0,y.clear(),g.clear()},(0,r.useEffect)(()=>()=>k(),[]),b.current)return r.createElement(r.Fragment,null,m.map(e=>r.createElement(x,{key:E(e),isPresent:!0,initial:!!a&&void 0,presenceAffectsLayout:u,mode:c},e)));m=[...m];const C=v.current.map(E),P=p.map(E),N=C.length;for(let r=0;r<N;r++){const e=C[r];-1!==P.indexOf(e)||g.has(e)||g.set(e,void 0)}return"wait"===c&&g.size&&(m=[]),g.forEach((e,t)=>{if(-1!==P.indexOf(t))return;const a=y.get(t);if(!a)return;const o=C.indexOf(t);let s=e;if(!s){const e=()=>{g.delete(t);const e=Array.from(y.keys()).filter(e=>!P.includes(e));if(e.forEach(e=>y.delete(e)),v.current=p.filter(n=>{const r=E(n);return r===t||e.includes(r)}),!g.size){if(!1===h.current)return;f(),i&&i()}};s=r.createElement(x,{key:E(a),isPresent:!1,onExitComplete:e,custom:n,presenceAffectsLayout:u,mode:c},a),g.set(t,s)}m.splice(o,0,s)}),m=m.map(e=>{const t=e.key;return g.has(t)?e:r.createElement(x,{key:E(e),isPresent:!0,presenceAffectsLayout:u,mode:c},e)}),r.createElement(r.Fragment,null,g.size?m:m.map(e=>(0,r.cloneElement)(e)))};function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}function N(e){var t=function(e,t){if("object"!=P(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=P(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==P(t)?t:t+""}function j(e,t,n){return(t=N(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?T(Object(n),!0).forEach(function(t){j(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):T(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}const L=(0,r.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),R=(0,r.createContext)({}),A=(0,r.createContext)({strict:!1}),D=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),V="data-"+D("framerAppearId");function _(e){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function z(e){return"string"===typeof e||Array.isArray(e)}function O(e){return null!==e&&"object"===typeof e&&"function"===typeof e.start}const I=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],F=["initial",...I];function B(e){return O(e.animate)||F.some(t=>z(e[t]))}function U(e){return Boolean(B(e)||e.variants)}function H(e){const{initial:t,animate:n}=function(e,t){if(B(e)){const{initial:t,animate:n}=e;return{initial:!1===t||z(t)?t:void 0,animate:z(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,r.useContext)(R));return(0,r.useMemo)(()=>({initial:t,animate:n}),[W(t),W(n)])}function W(e){return Array.isArray(e)?e.join(" "):e}const $={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Y={};for(const Zo in $)Y[Zo]={isEnabled:e=>$[Zo].some(t=>!!e[t])};const Q=(0,r.createContext)({}),q=Symbol.for("motionComponentSymbol");function X(e){let{preloadedFeatures:t,createVisualElement:n,useRender:a,useVisualState:s,Component:l}=e;t&&function(e){for(const t in e)Y[t]=M(M({},Y[t]),e[t])}(t);const u=(0,r.forwardRef)(function(e,u){let c;const d=M(M(M({},(0,r.useContext)(L)),e),{},{layoutId:G(e)}),{isStatic:f}=d,h=H(e),p=s(e,f);if(!f&&i){h.visualElement=function(e,t,n,a){const{visualElement:i}=(0,r.useContext)(R),s=(0,r.useContext)(A),l=(0,r.useContext)(m),u=(0,r.useContext)(L).reducedMotion,c=(0,r.useRef)();a=a||s.renderer,!c.current&&a&&(c.current=a(e,{visualState:t,parent:i,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));const d=c.current;(0,r.useInsertionEffect)(()=>{d&&d.update(n,l)});const f=(0,r.useRef)(Boolean(n[V]&&!window.HandoffComplete));return o(()=>{d&&(d.render(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,r.useEffect)(()=>{d&&(d.updateFeatures(),!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(f.current=!1,window.HandoffComplete=!0))}),d}(l,p,d,n);const e=(0,r.useContext)(Q),a=(0,r.useContext)(A).strict;h.visualElement&&(c=h.visualElement.loadFeatures(d,a,t,e))}return r.createElement(R.Provider,{value:h},c&&h.visualElement?r.createElement(c,M({visualElement:h.visualElement},d)):null,a(l,e,function(e,t,n){return(0,r.useCallback)(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):_(n)&&(n.current=r))},[t])}(p,h.visualElement,u),p,f,h.visualElement))});return u[q]=l,u}function G(e){let{layoutId:t}=e;const n=(0,r.useContext)(w).id;return n&&void 0!==t?n+"-"+t:t}function K(e){function t(t){return X(e(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}))}if("undefined"===typeof Proxy)return t;const n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}const Z=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function J(e){return"string"===typeof e&&!e.includes("-")&&!!(Z.indexOf(e)>-1||/[A-Z]/.test(e))}const ee={};const te=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ne=new Set(te);function re(e,t){let{layout:n,layoutId:r}=t;return ne.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!ee[e]||"opacity"===e)}const ae=e=>Boolean(e&&e.getVelocity),ie={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},oe=te.length;const se=e=>t=>"string"===typeof t&&t.startsWith(e),le=se("--"),ue=se("var(--"),ce=(e,t)=>t&&"number"===typeof e?t.transform(e):e,de=(e,t,n)=>Math.min(Math.max(n,e),t),fe={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},he=M(M({},fe),{},{transform:e=>de(0,1,e)}),pe=M(M({},fe),{},{default:1}),me=e=>Math.round(1e5*e)/1e5,ge=/(-)?([\d]*\.?[\d])+/g,ve=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,ye=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function xe(e){return"string"===typeof e}const be=e=>({test:t=>xe(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),we=be("deg"),ke=be("%"),Se=be("px"),Ee=be("vh"),Ce=be("vw"),Pe=M(M({},ke),{},{parse:e=>ke.parse(e)/100,transform:e=>ke.transform(100*e)}),Ne=M(M({},fe),{},{transform:Math.round}),je={borderWidth:Se,borderTopWidth:Se,borderRightWidth:Se,borderBottomWidth:Se,borderLeftWidth:Se,borderRadius:Se,radius:Se,borderTopLeftRadius:Se,borderTopRightRadius:Se,borderBottomRightRadius:Se,borderBottomLeftRadius:Se,width:Se,maxWidth:Se,height:Se,maxHeight:Se,size:Se,top:Se,right:Se,bottom:Se,left:Se,padding:Se,paddingTop:Se,paddingRight:Se,paddingBottom:Se,paddingLeft:Se,margin:Se,marginTop:Se,marginRight:Se,marginBottom:Se,marginLeft:Se,rotate:we,rotateX:we,rotateY:we,rotateZ:we,scale:pe,scaleX:pe,scaleY:pe,scaleZ:pe,skew:we,skewX:we,skewY:we,distance:Se,translateX:Se,translateY:Se,translateZ:Se,x:Se,y:Se,z:Se,perspective:Se,transformPerspective:Se,opacity:he,originX:Pe,originY:Pe,originZ:Se,zIndex:Ne,fillOpacity:he,strokeOpacity:he,numOctaves:Ne};function Te(e,t,n,r){const{style:a,vars:i,transform:o,transformOrigin:s}=e;let l=!1,u=!1,c=!0;for(const d in t){const e=t[d];if(le(d)){i[d]=e;continue}const n=je[d],r=ce(e,n);if(ne.has(d)){if(l=!0,o[d]=r,!c)continue;e!==(n.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,s[d]=r):a[d]=r}if(t.transform||(l||r?a.transform=function(e,t,n,r){let{enableHardwareAcceleration:a=!0,allowTransformNone:i=!0}=t,o="";for(let s=0;s<oe;s++){const t=te[s];void 0!==e[t]&&(o+="".concat(ie[t]||t,"(").concat(e[t],") "))}return a&&!e.z&&(o+="translateZ(0)"),o=o.trim(),r?o=r(e,n?"":o):i&&n&&(o="none"),o}(e.transform,n,c,r):a.transform&&(a.transform="none")),u){const{originX:e="50%",originY:t="50%",originZ:n=0}=s;a.transformOrigin="".concat(e," ").concat(t," ").concat(n)}}const Me=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Le(e,t,n){for(const r in t)ae(t[r])||re(r,n)||(e[r]=t[r])}function Re(e,t,n){const a={};return Le(a,e.style||{},e),Object.assign(a,function(e,t,n){let{transformTemplate:a}=e;return(0,r.useMemo)(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Te(e,t,{enableHardwareAcceleration:!n},a),Object.assign({},e.vars,e.style)},[t])}(e,t,n)),e.transformValues?e.transformValues(a):a}function Ae(e,t,n){const r={},a=Re(e,t,n);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,a.userSelect=a.WebkitUserSelect=a.WebkitTouchCallout="none",a.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=a,r}const De=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ve(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||De.has(e)}let _e=e=>!Ve(e);try{(ze=require("@emotion/is-prop-valid").default)&&(_e=e=>e.startsWith("on")?!Ve(e):ze(e))}catch(Ko){}var ze;function Oe(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Ie(e,t,n){return"string"===typeof e?e:Se.transform(t+n*e)}const Fe={offset:"stroke-dashoffset",array:"stroke-dasharray"},Be={offset:"strokeDashoffset",array:"strokeDasharray"};const Ue=["attrX","attrY","attrScale","originX","originY","pathLength","pathSpacing","pathOffset"];function He(e,t,n,r,a){let{attrX:i,attrY:o,attrScale:s,originX:l,originY:u,pathLength:c,pathSpacing:d=1,pathOffset:f=0}=t;if(Te(e,Oe(t,Ue),n,a),r)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:h,style:p,dimensions:m}=e;h.transform&&(m&&(p.transform=h.transform),delete h.transform),m&&(void 0!==l||void 0!==u||p.transform)&&(p.transformOrigin=function(e,t,n){const r=Ie(t,e.x,e.width),a=Ie(n,e.y,e.height);return"".concat(r," ").concat(a)}(m,void 0!==l?l:.5,void 0!==u?u:.5)),void 0!==i&&(h.x=i),void 0!==o&&(h.y=o),void 0!==s&&(h.scale=s),void 0!==c&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const i=a?Fe:Be;e[i.offset]=Se.transform(-r);const o=Se.transform(t),s=Se.transform(n);e[i.array]="".concat(o," ").concat(s)}(h,c,d,f,!1)}const We=()=>M(M({},{style:{},transform:{},transformOrigin:{},vars:{}}),{},{attrs:{}}),$e=e=>"string"===typeof e&&"svg"===e.toLowerCase();function Ye(e,t,n,a){const i=(0,r.useMemo)(()=>{const n=We();return He(n,t,{enableHardwareAcceleration:!1},$e(a),e.transformTemplate),M(M({},n.attrs),{},{style:M({},n.style)})},[t]);if(e.style){const t={};Le(t,e.style,e),i.style=M(M({},t),i.style)}return i}function Qe(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(t,n,a,i,o)=>{let{latestValues:s}=i;const l=(J(t)?Ye:Ae)(n,s,o,t),u=function(e,t,n){const r={};for(const a in e)"values"===a&&"object"===typeof e.values||(_e(a)||!0===n&&Ve(a)||!t&&!Ve(a)||e.draggable&&a.startsWith("onDrag"))&&(r[a]=e[a]);return r}(n,"string"===typeof t,e),c=M(M(M({},u),l),{},{ref:a}),{children:d}=n,f=(0,r.useMemo)(()=>ae(d)?d.get():d,[d]);return(0,r.createElement)(t,M(M({},c),{},{children:f}))}}function qe(e,t,n,r){let{style:a,vars:i}=t;Object.assign(e.style,a,r&&r.getProjectionStyles(n));for(const o in i)e.style.setProperty(o,i[o])}const Xe=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ge(e,t,n,r){qe(e,t,void 0,r);for(const a in t.attrs)e.setAttribute(Xe.has(a)?a:D(a),t.attrs[a])}function Ke(e,t){const{style:n}=e,r={};for(const a in n)(ae(n[a])||t.style&&ae(t.style[a])||re(a,e))&&(r[a]=n[a]);return r}function Ze(e,t){const n=Ke(e,t);for(const r in e)if(ae(e[r])||ae(t[r])){n[-1!==te.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]}return n}function Je(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,a)),"string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,a)),t}const et=e=>Array.isArray(e),tt=e=>et(e)?e[e.length-1]||0:e;function nt(e){const t=ae(e)?e.get():e;return n=t,Boolean(n&&"object"===typeof n&&n.mix&&n.toValue)?t.toValue():t;var n}const rt=["transitionEnd","transition"];const at=e=>(t,n)=>{const a=(0,r.useContext)(R),i=(0,r.useContext)(m),o=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:a,createRenderState:i,onMount:o}=e;const s={latestValues:it(t,n,r,a),renderState:i()};return o&&(s.mount=e=>o(t,e,s)),s}(e,t,a,i);return n?o():g(o)};function it(e,t,n,r){const a={},i=r(e,{});for(const f in i)a[f]=nt(i[f]);let{initial:o,animate:s}=e;const l=B(e),u=U(e);t&&u&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===s&&(s=t.animate));let c=!!n&&!1===n.initial;c=c||!1===o;const d=c?s:o;if(d&&"boolean"!==typeof d&&!O(d)){(Array.isArray(d)?d:[d]).forEach(t=>{const n=Je(e,t);if(!n)return;const{transitionEnd:r,transition:i}=n,o=Oe(n,rt);for(const e in o){let t=o[e];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(a[e]=t)}for(const e in r)a[e]=r[e]})}return a}const ot={useVisualState:at({scrapeMotionValuesFromProps:Ze,createRenderState:We,onMount:(e,t,n)=>{let{renderState:r,latestValues:a}=n;d.read(()=>{try{r.dimensions="function"===typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),d.render(()=>{He(r,a,{enableHardwareAcceleration:!1},$e(t.tagName),e.transformTemplate),Ge(t,r)})}})},st={useVisualState:at({scrapeMotionValuesFromProps:Ke,createRenderState:Me})};function lt(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const ut=e=>"mouse"===e.pointerType?"number"!==typeof e.button||e.button<=0:!1!==e.isPrimary;function ct(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{point:{x:e[t+"X"],y:e[t+"Y"]}}}function dt(e,t,n,r){return lt(e,t,(e=>t=>ut(t)&&e(t,ct(t)))(n),r)}const ft=(e,t)=>n=>t(e(n)),ht=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(ft)};function pt(e){let t=null;return()=>{const n=()=>{t=null};return null===t&&(t=e,n)}}const mt=pt("dragHorizontal"),gt=pt("dragVertical");function vt(e){let t=!1;if("y"===e)t=gt();else if("x"===e)t=mt();else{const e=mt(),n=gt();e&&n?t=()=>{e(),n()}:(e&&e(),n&&n())}return t}function yt(){const e=vt(!0);return!e||(e(),!1)}class xt{constructor(e){this.isMounted=!1,this.node=e}update(){}}function bt(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End");return dt(e.current,n,(n,a)=>{if("touch"===n.pointerType||yt())return;const i=e.getProps();e.animationState&&i.whileHover&&e.animationState.setActive("whileHover",t),i[r]&&d.update(()=>i[r](n,a))},{passive:!e.getProps()[r]})}const wt=(e,t)=>!!t&&(e===t||wt(e,t.parentElement));function kt(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,ct(n))}const St=["root"],Et=new WeakMap,Ct=new WeakMap,Pt=e=>{const t=Et.get(e.target);t&&t(e)},Nt=e=>{e.forEach(Pt)};function jt(e,t,n){const r=function(e){let{root:t}=e,n=Oe(e,St);const r=t||document;Ct.has(r)||Ct.set(r,{});const a=Ct.get(r),i=JSON.stringify(n);return a[i]||(a[i]=new IntersectionObserver(Nt,M({root:t},n))),a[i]}(t);return Et.set(e,n),r.observe(e),()=>{Et.delete(e),r.unobserve(e)}}const Tt={some:0,all:1};const Mt={inView:{Feature:class extends xt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:a}=e,i={root:t?t.current:void 0,rootMargin:n,threshold:"number"===typeof r?r:Tt[r]};return jt(this.node.current,i,e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,a&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),i=t?n:r;i&&i(e)})}mount(){this.startObserver()}update(){if("undefined"===typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node,n=["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:n={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==n[e]}(e,t));n&&this.startObserver()}unmount(){}}},tap:{Feature:class extends xt{constructor(){super(...arguments),this.removeStartListeners=l,this.removeEndListeners=l,this.removeAccessibleListeners=l,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),r=dt(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:r,globalTapTarget:a}=this.node.getProps();d.update(()=>{a||wt(this.node.current,e.target)?n&&n(e,t):r&&r(e,t)})},{passive:!(n.onTap||n.onPointerUp)}),a=dt(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=ht(r,a),this.startPress(e,t)},this.startAccessiblePress=()=>{const e=lt(this.node.current,"keydown",e=>{if("Enter"!==e.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=lt(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&kt("up",(e,t)=>{const{onTap:n}=this.node.getProps();n&&d.update(()=>n(e,t))})}),kt("down",(e,t)=>{this.startPress(e,t)})}),t=lt(this.node.current,"blur",()=>{this.isPressing&&kt("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=ht(e,t)}}startPress(e,t){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&d.update(()=>n(e,t))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!yt()}cancelPress(e,t){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&d.update(()=>n(e,t))}mount(){const e=this.node.getProps(),t=dt(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=lt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=ht(t,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends xt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ht(lt(this.node.current,"focus",()=>this.onFocus()),lt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends xt{mount(){this.unmount=ht(bt(this.node,!0),bt(this.node,!1))}unmount(){}}}};function Lt(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Rt(e,t,n){const r=e.getProps();return Je(r,t,void 0!==n?n:r.custom,function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.get()),t}(e),function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.getVelocity()),t}(e))}const At=e=>1e3*e,Dt=e=>e/1e3,Vt=!1,_t=e=>Array.isArray(e)&&"number"===typeof e[0];function zt(e){return Boolean(!e||"string"===typeof e&&It[e]||_t(e)||Array.isArray(e)&&e.every(zt))}const Ot=e=>{let[t,n,r,a]=e;return"cubic-bezier(".concat(t,", ").concat(n,", ").concat(r,", ").concat(a,")")},It={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ot([0,.65,.55,1]),circOut:Ot([.55,0,1,.45]),backIn:Ot([.31,.01,.66,-.59]),backOut:Ot([.33,1.53,.69,.99])};function Ft(e){if(e)return _t(e)?Ot(e):Array.isArray(e)?e.map(Ft):It[e]}const Bt=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function Ut(e,t,n,r){if(e===t&&n===r)return l;const a=t=>function(e,t,n,r,a){let i,o,s=0;do{o=t+(n-t)/2,i=Bt(o,r,a)-e,i>0?n=o:t=o}while(Math.abs(i)>1e-7&&++s<12);return o}(t,0,1,e,n);return e=>0===e||1===e?e:Bt(a(e),t,r)}const Ht=Ut(.42,0,1,1),Wt=Ut(0,0,.58,1),$t=Ut(.42,0,.58,1),Yt=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Qt=e=>t=>1-e(1-t),qt=e=>1-Math.sin(Math.acos(e)),Xt=Qt(qt),Gt=Yt(qt),Kt=Ut(.33,1.53,.69,.99),Zt=Qt(Kt),Jt=Yt(Zt),en={linear:l,easeIn:Ht,easeInOut:$t,easeOut:Wt,circIn:qt,circInOut:Gt,circOut:Xt,backIn:Zt,backInOut:Jt,backOut:Kt,anticipate:e=>(e*=2)<1?.5*Zt(e):.5*(2-Math.pow(2,-10*(e-1)))},tn=e=>{if(Array.isArray(e)){S(4===e.length,"Cubic bezier arrays must contain four numerical values.");const[t,n,r,a]=e;return Ut(t,n,r,a)}return"string"===typeof e?(S(void 0!==en[e],"Invalid easing type '".concat(e,"'")),en[e]):e},nn=(e,t)=>n=>Boolean(xe(n)&&ye.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),rn=(e,t,n)=>r=>{if(!xe(r))return r;const[a,i,o,s]=r.match(ge);return{[e]:parseFloat(a),[t]:parseFloat(i),[n]:parseFloat(o),alpha:void 0!==s?parseFloat(s):1}},an=M(M({},fe),{},{transform:e=>Math.round((e=>de(0,255,e))(e))}),on={test:nn("rgb","red"),parse:rn("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:a=1}=e;return"rgba("+an.transform(t)+", "+an.transform(n)+", "+an.transform(r)+", "+me(he.transform(a))+")"}};const sn={test:nn("#"),parse:function(e){let t="",n="",r="",a="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),a=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),a=e.substring(4,5),t+=t,n+=n,r+=r,a+=a),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:a?parseInt(a,16)/255:1}},transform:on.transform},ln={test:nn("hsl","hue"),parse:rn("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:a=1}=e;return"hsla("+Math.round(t)+", "+ke.transform(me(n))+", "+ke.transform(me(r))+", "+me(he.transform(a))+")"}},un={test:e=>on.test(e)||sn.test(e)||ln.test(e),parse:e=>on.test(e)?on.parse(e):ln.test(e)?ln.parse(e):sn.parse(e),transform:e=>xe(e)?e:e.hasOwnProperty("red")?on.transform(e):ln.transform(e)},cn=(e,t,n)=>-n*e+n*t+e;function dn(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}const fn=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},hn=[sn,on,ln];function pn(e){const t=(n=e,hn.find(e=>e.test(n)));var n;S(Boolean(t),"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."));let r=t.parse(e);return t===ln&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:a}=e;t/=360,n/=100,r/=100;let i=0,o=0,s=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,a=2*r-e;i=dn(a,e,t+1/3),o=dn(a,e,t),s=dn(a,e,t-1/3)}else i=o=s=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:a}}(r)),r}const mn=(e,t)=>{const n=pn(e),r=pn(t),a=M({},n);return e=>(a.red=fn(n.red,r.red,e),a.green=fn(n.green,r.green,e),a.blue=fn(n.blue,r.blue,e),a.alpha=cn(n.alpha,r.alpha,e),on.transform(a))};const gn={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:l},vn={regex:ve,countKey:"Colors",token:"${c}",parse:un.parse},yn={regex:ge,countKey:"Numbers",token:"${n}",parse:fe.parse};function xn(e,t){let{regex:n,countKey:r,token:a,parse:i}=t;const o=e.tokenised.match(n);o&&(e["num"+r]=o.length,e.tokenised=e.tokenised.replace(n,a),e.values.push(...o.map(i)))}function bn(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&xn(n,gn),xn(n,vn),xn(n,yn),n}function wn(e){return bn(e).values}function kn(e){const{values:t,numColors:n,numVars:r,tokenised:a}=bn(e),i=t.length;return e=>{let t=a;for(let a=0;a<i;a++)t=a<r?t.replace(gn.token,e[a]):a<r+n?t.replace(vn.token,un.transform(e[a])):t.replace(yn.token,me(e[a]));return t}}const Sn=e=>"number"===typeof e?0:e;const En={test:function(e){var t,n;return isNaN(e)&&xe(e)&&((null===(t=e.match(ge))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(ve))||void 0===n?void 0:n.length)||0)>0},parse:wn,createTransformer:kn,getAnimatableNone:function(e){const t=wn(e);return kn(e)(t.map(Sn))}},Cn=(e,t)=>n=>"".concat(n>0?t:e);function Pn(e,t){return"number"===typeof e?n=>cn(e,t,n):un.test(e)?mn(e,t):e.startsWith("var(")?Cn(e,t):Tn(e,t)}const Nn=(e,t)=>{const n=[...e],r=n.length,a=e.map((e,n)=>Pn(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=a[t](e);return n}},jn=(e,t)=>{const n=M(M({},e),t),r={};for(const a in n)void 0!==e[a]&&void 0!==t[a]&&(r[a]=Pn(e[a],t[a]));return e=>{for(const t in r)n[t]=r[t](e);return n}},Tn=(e,t)=>{const n=En.createTransformer(t),r=bn(e),a=bn(t);return r.numVars===a.numVars&&r.numColors===a.numColors&&r.numNumbers>=a.numNumbers?ht(Nn(r.values,a.values),n):(k(!0,"Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.")),Cn(e,t))},Mn=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r},Ln=(e,t)=>n=>cn(e,t,n);function Rn(e,t,n){const r=[],a=n||("number"===typeof(i=e[0])?Ln:"string"===typeof i?un.test(i)?mn:Tn:Array.isArray(i)?Nn:"object"===typeof i?jn:Ln);var i;const o=e.length-1;for(let s=0;s<o;s++){let n=a(e[s],e[s+1]);if(t){const e=Array.isArray(t)?t[s]||l:t;n=ht(e,n)}r.push(n)}return r}function An(e,t){let{clamp:n=!0,ease:r,mixer:a}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=e.length;if(S(i===t.length,"Both input and output ranges must be the same length"),1===i)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=Rn(t,r,a),s=o.length,l=t=>{let n=0;if(s>1)for(;n<e.length-2&&!(t<e[n+1]);n++);const r=Mn(e[n],e[n+1],t);return o[n](r)};return n?t=>l(de(e[0],e[i-1],t)):l}function Dn(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const a=Mn(0,t,r);e.push(cn(n,1,a))}}(t,e.length-1),t}function Vn(e){let{duration:t=300,keyframes:n,times:r,ease:a="easeInOut"}=e;const i=(e=>Array.isArray(e)&&"number"!==typeof e[0])(a)?a.map(tn):tn(a),o={done:!1,value:n[0]},s=function(e,t){return e.map(e=>e*t)}(r&&r.length===n.length?r:Dn(n),t),l=An(s,n,{ease:Array.isArray(i)?i:(u=n,c=i,u.map(()=>c||$t).splice(0,u.length-1))});var u,c;return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}function _n(e,t){return t?e*(1e3/t):0}function zn(e,t,n){const r=Math.max(t-5,0);return _n(n-e(r),t-r)}const On=.001;function In(e){let t,n,{duration:r=800,bounce:a=.25,velocity:i=0,mass:o=1}=e;k(r<=At(10),"Spring duration must be 10 seconds or less");let s=1-a;s=de(.05,1,s),r=de(.01,10,Dt(r)),s<1?(t=e=>{const t=e*s,n=t*r,a=t-i,o=Bn(e,s),l=Math.exp(-n);return On-a/o*l},n=e=>{const n=e*s*r,a=n*i+i,o=Math.pow(s,2)*Math.pow(e,2)*r,l=Math.exp(-n),u=Bn(Math.pow(e,2),s);return(-t(e)+On>0?-1:1)*((a-o)*l)/u}):(t=e=>Math.exp(-e*r)*((e-i)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(i-e)));const l=function(e,t,n){let r=n;for(let a=1;a<Fn;a++)r-=e(r)/t(r);return r}(t,n,5/r);if(r=At(r),isNaN(l))return{stiffness:100,damping:10,duration:r};{const e=Math.pow(l,2)*o;return{stiffness:e,damping:2*s*Math.sqrt(o*e),duration:r}}}const Fn=12;function Bn(e,t){return e*Math.sqrt(1-t*t)}const Un=["keyframes","restDelta","restSpeed"],Hn=["duration","bounce"],Wn=["stiffness","damping","mass"];function $n(e,t){return t.some(t=>void 0!==e[t])}function Yn(e){let{keyframes:t,restDelta:n,restSpeed:r}=e,a=Oe(e,Un);const i=t[0],o=t[t.length-1],s={done:!1,value:i},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:h}=function(e){let t=M({velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1},e);if(!$n(e,Wn)&&$n(e,Hn)){const n=In(e);t=M(M(M({},t),n),{},{mass:1}),t.isResolvedFromDuration=!0}return t}(M(M({},a),{},{velocity:-Dt(a.velocity||0)})),p=f||0,m=u/(2*Math.sqrt(l*c)),g=o-i,v=Dt(Math.sqrt(l/c)),y=Math.abs(g)<5;let x;if(r||(r=y?.01:2),n||(n=y?.005:.5),m<1){const e=Bn(v,m);x=t=>{const n=Math.exp(-m*v*t);return o-n*((p+m*v*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}}else if(1===m)x=e=>o-Math.exp(-v*e)*(g+(p+v*g)*e);else{const e=v*Math.sqrt(m*m-1);x=t=>{const n=Math.exp(-m*v*t),r=Math.min(e*t,300);return o-n*((p+m*v*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}return{calculatedDuration:h&&d||null,next:e=>{const t=x(e);if(h)s.done=e>=d;else{let a=p;0!==e&&(a=m<1?zn(x,e,t):0);const i=Math.abs(a)<=r,l=Math.abs(o-t)<=n;s.done=i&&l}return s.value=s.done?o:t,s}}}function Qn(e){let{keyframes:t,velocity:n=0,power:r=.8,timeConstant:a=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:u,restDelta:c=.5,restSpeed:d}=e;const f=t[0],h={done:!1,value:f},p=e=>void 0===l?u:void 0===u||Math.abs(l-e)<Math.abs(u-e)?l:u;let m=r*n;const g=f+m,v=void 0===s?g:s(g);v!==g&&(m=v-f);const y=e=>-m*Math.exp(-e/a),x=e=>v+y(e),b=e=>{const t=y(e),n=x(e);h.done=Math.abs(t)<=c,h.value=h.done?v:n};let w,k;const S=e=>{var t;(t=h.value,void 0!==l&&t<l||void 0!==u&&t>u)&&(w=e,k=Yn({keyframes:[h.value,p(h.value)],velocity:zn(x,e,h.value),damping:i,stiffness:o,restDelta:c,restSpeed:d}))};return S(0),{calculatedDuration:null,next:e=>{let t=!1;return k||void 0!==w||(t=!0,b(e),S(e)),void 0!==w&&e>w?k.next(e-w):(!t&&b(e),h)}}}const qn=e=>{const t=t=>{let{timestamp:n}=t;return e(n)};return{start:()=>d.update(t,!0),stop:()=>f(t),now:()=>h.isProcessing?h.timestamp:performance.now()}};function Xn(e){let t=0;let n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}const Gn=["autoplay","delay","driver","keyframes","type","repeat","repeatDelay","repeatType","onPlay","onStop","onComplete","onUpdate"],Kn={decay:Qn,inertia:Qn,tween:Vn,keyframes:Vn,spring:Yn};function Zn(e){let t,n,{autoplay:r=!0,delay:a=0,driver:i=qn,keyframes:o,type:s="keyframes",repeat:l=0,repeatDelay:u=0,repeatType:c="loop",onPlay:d,onStop:f,onComplete:h,onUpdate:p}=e,m=Oe(e,Gn),g=1,v=!1;const y=()=>{n=new Promise(e=>{t=e})};let x;y();const b=Kn[s]||Vn;let w;b!==Vn&&"number"!==typeof o[0]&&(w=An([0,100],o,{clamp:!1}),o=[0,100]);const k=b(M(M({},m),{},{keyframes:o}));let S;"mirror"===c&&(S=b(M(M({},m),{},{keyframes:[...o].reverse(),velocity:-(m.velocity||0)})));let E="idle",C=null,P=null,N=null;null===k.calculatedDuration&&l&&(k.calculatedDuration=Xn(k));const{calculatedDuration:j}=k;let T=1/0,L=1/0;null!==j&&(T=j+u,L=T*(l+1)-u);let R=0;const A=e=>{if(null===P)return;g>0&&(P=Math.min(P,e)),g<0&&(P=Math.min(e-L/g,P)),R=null!==C?C:Math.round(e-P)*g;const t=R-a*(g>=0?1:-1),n=g>=0?t<0:t>L;R=Math.max(t,0),"finished"===E&&null===C&&(R=L);let r=R,i=k;if(l){const e=Math.min(R,L)/T;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,l+1);Boolean(t%2)&&("reverse"===c?(n=1-n,u&&(n-=u/T)):"mirror"===c&&(i=S)),r=de(0,1,n)*T}const s=n?{done:!1,value:o[0]}:i.next(r);w&&(s.value=w(s.value));let{done:d}=s;n||null===j||(d=g>=0?R>=L:R<=0);const f=null===C&&("finished"===E||"running"===E&&d);return p&&p(s.value),f&&_(),s},D=()=>{x&&x.stop(),x=void 0},V=()=>{E="idle",D(),t(),y(),P=N=null},_=()=>{E="finished",h&&h(),D(),t()},z=()=>{if(v)return;x||(x=i(A));const e=x.now();d&&d(),null!==C?P=e-C:P&&"finished"!==E||(P=e),"finished"===E&&y(),N=P,C=null,E="running",x.start()};r&&z();const O={then:(e,t)=>n.then(e,t),get time(){return Dt(R)},set time(e){e=At(e),R=e,null===C&&x&&0!==g?P=x.now()-e/g:C=e},get duration(){const e=null===k.calculatedDuration?Xn(k):k.calculatedDuration;return Dt(e)},get speed(){return g},set speed(e){e!==g&&x&&(g=e,O.time=Dt(R))},get state(){return E},play:z,pause:()=>{E="paused",C=R},stop:()=>{v=!0,"idle"!==E&&(E="idle",f&&f(),V())},cancel:()=>{null!==N&&A(N),V()},complete:()=>{E="finished"},sample:e=>(P=0,A(e))};return O}const Jn=["onUpdate","onComplete"],er=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),tr=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function nr(e,t,n){let{onUpdate:r,onComplete:a}=n,i=Oe(n,Jn);if(!(er()&&tr.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type))return!1;let o,s,u=!1,c=!1;const h=()=>{s=new Promise(e=>{o=e})};h();let{keyframes:p,duration:m=300,ease:g,times:v}=i;if(((e,t)=>"spring"===t.type||"backgroundColor"===e||!zt(t.ease))(t,i)){const e=Zn(M(M({},i),{},{repeat:0,delay:0}));let t={done:!1,value:p[0]};const n=[];let r=0;for(;!t.done&&r<2e4;)t=e.sample(r),n.push(t.value),r+=10;v=void 0,p=n,m=r-10,g="linear"}const y=function(e,t,n){let{delay:r=0,duration:a,repeat:i=0,repeatType:o="loop",ease:s,times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const u={[t]:n};l&&(u.offset=l);const c=Ft(s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:a,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:"reverse"===o?"alternate":"normal"})}(e.owner.current,t,p,M(M({},i),{},{duration:m,ease:g,times:v})),x=()=>{c=!1,y.cancel()},b=()=>{c=!0,d.update(x),o(),h()};y.onfinish=()=>{c||(e.set(function(e,t){let{repeat:n,repeatType:r="loop"}=t;return e[n&&"loop"!==r&&n%2===1?0:e.length-1]}(p,i)),a&&a(),b())};return{then:(e,t)=>s.then(e,t),attachTimeline:e=>(y.timeline=e,y.onfinish=null,l),get time(){return Dt(y.currentTime||0)},set time(e){y.currentTime=At(e)},get speed(){return y.playbackRate},set speed(e){y.playbackRate=e},get duration(){return Dt(m)},play:()=>{u||(y.play(),f(x))},pause:()=>y.pause(),stop:()=>{if(u=!0,"idle"===y.playState)return;const{currentTime:t}=y;if(t){const n=Zn(M(M({},i),{},{autoplay:!1}));e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}b()},complete:()=>{c||y.finish()},cancel:b}}const rr={type:"spring",stiffness:500,damping:25,restSpeed:10},ar={type:"keyframes",duration:.8},ir={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},or=(e,t)=>{let{keyframes:n}=t;return n.length>2?ar:ne.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:rr:ir},sr=(e,t)=>"zIndex"!==e&&(!("number"!==typeof t&&!Array.isArray(t))||!("string"!==typeof t||!En.test(t)&&"0"!==t||t.startsWith("url("))),lr=new Set(["brightness","contrast","saturate","opacity"]);function ur(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(ge)||[];if(!r)return e;const a=n.replace(r,"");let i=lr.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+a+")"}const cr=/([a-z-]*)\(.*?\)/g,dr=M(M({},En),{},{getAnimatableNone:e=>{const t=e.match(cr);return t?t.map(ur).join(" "):e}}),fr=M(M({},je),{},{color:un,backgroundColor:un,outlineColor:un,fill:un,stroke:un,borderColor:un,borderTopColor:un,borderRightColor:un,borderBottomColor:un,borderLeftColor:un,filter:dr,WebkitFilter:dr}),hr=e=>fr[e];function pr(e,t){let n=hr(e);return n!==dr&&(n=En),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const mr=e=>/^0[^.\s]+$/.test(e);function gr(e){return"number"===typeof e?0===e:null!==e?"none"===e||"0"===e||mr(e):void 0}const vr=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"];function yr(e,t){return e[t]||e.default||e}const xr=!1,br=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return a=>{const i=yr(r,e)||{},o=i.delay||r.delay||0;let{elapsed:s=0}=r;s-=At(o);const u=function(e,t,n,r){const a=sr(t,n);let i;i=Array.isArray(n)?[...n]:[null,n];const o=void 0!==r.from?r.from:e.get();let s;const l=[];for(let u=0;u<i.length;u++)null===i[u]&&(i[u]=0===u?o:i[u-1]),gr(i[u])&&l.push(u),"string"===typeof i[u]&&"none"!==i[u]&&"0"!==i[u]&&(s=i[u]);if(a&&l.length&&s)for(let u=0;u<l.length;u++)i[l[u]]=pr(t,s);return i}(t,e,n,i),c=u[0],d=u[u.length-1],f=sr(e,c),h=sr(e,d);k(f===h,"You are trying to animate ".concat(e,' from "').concat(c,'" to "').concat(d,'". ').concat(c," is not an animatable value - to enable this animation set ").concat(c," to a value animatable to ").concat(d," via the `style` property."));let p=M(M({keyframes:u,velocity:t.getVelocity(),ease:"easeOut"},i),{},{delay:-s,onUpdate:e=>{t.set(e),i.onUpdate&&i.onUpdate(e)},onComplete:()=>{a(),i.onComplete&&i.onComplete()}});if(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:a,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:u,elapsed:c}=e,d=Oe(e,vr);return!!Object.keys(d).length}(i)||(p=M(M({},p),or(e,p))),p.duration&&(p.duration=At(p.duration)),p.repeatDelay&&(p.repeatDelay=At(p.repeatDelay)),!f||!h||Vt||!1===i.type||xr)return function(e){let{keyframes:t,delay:n,onUpdate:r,onComplete:a}=e;const i=()=>(r&&r(t[t.length-1]),a&&a(),{time:0,speed:1,duration:0,play:l,pause:l,stop:l,then:e=>(e(),Promise.resolve()),cancel:l,complete:l});return n?Zn({keyframes:[0,1],duration:0,delay:n,onComplete:i}):i()}(Vt?M(M({},p),{},{delay:0}):p);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const n=nr(t,e,p);if(n)return n}return Zn(p)}};function wr(e){return Boolean(ae(e)&&e.add)}const kr=e=>/^\-?\d*\.?\d+$/.test(e);function Sr(e,t){-1===e.indexOf(t)&&e.push(t)}function Er(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Cr{constructor(){this.subscriptions=[]}add(e){return Sr(this.subscriptions,e),()=>Er(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let a=0;a<r;a++){const r=this.subscriptions[a];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Pr={current:void 0};class Nr{constructor(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var r;this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t.prev=t.current,t.current=e;const{delta:r,timestamp:a}=h;t.lastUpdated!==a&&(t.timeDelta=r,t.lastUpdated=a,d.postRender(t.scheduleVelocityCheck)),t.prev!==t.current&&t.events.change&&t.events.change.notify(t.current),t.events.velocityChange&&t.events.velocityChange.notify(t.getVelocity()),n&&t.events.renderRequest&&t.events.renderRequest.notify(t.current)},this.scheduleVelocityCheck=()=>d.postRender(this.velocityCheck),this.velocityCheck=e=>{let{timestamp:t}=e;t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=(r=this.current,!isNaN(parseFloat(r))),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new Cr);const n=this.events[e].add(t);return"change"===e?()=>{n(),d.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=e,this.timeDelta=n}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Pr.current&&Pr.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?_n(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function jr(e,t){return new Nr(e,t)}const Tr=e=>t=>t.test(e),Mr=[fe,Se,ke,we,Ce,Ee,{test:e=>"auto"===e,parse:e=>e}],Lr=e=>Mr.find(Tr(e)),Rr=[...Mr,un,En],Ar=e=>Rr.find(Tr(e)),Dr=["transitionEnd","transition"];function Vr(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,jr(n))}function _r(e,t){const n=Rt(e,t);let r=n?e.makeTargetAnimatable(n,!1):{},{transitionEnd:a={},transition:i={}}=r,o=Oe(r,Dr);o=M(M({},o),a);for(const s in o){Vr(e,s,tt(o[s]))}}function zr(e,t){if(!t)return;return(t[e]||t.default||t).from}const Or=["transition","transitionEnd"];function Ir(e,t){let{protectedKeys:n,needsAnimating:r}=e;const a=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,a}function Fr(e,t){const n=e.get();if(!Array.isArray(t))return n!==t;for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}function Br(e,t){let{delay:n=0,transitionOverride:r,type:a}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=e.makeTargetAnimatable(t),{transition:o=e.getDefaultTransition(),transitionEnd:s}=i,l=Oe(i,Or);const u=e.getValue("willChange");r&&(o=r);const c=[],f=a&&e.animationState&&e.animationState.getState()[a];for(const h in l){const t=e.getValue(h),r=l[h];if(!t||void 0===r||f&&Ir(f,h))continue;const a=M({delay:n,elapsed:0},yr(o||{},h));if(window.HandoffAppearAnimations){const n=e.getProps()[V];if(n){const e=window.HandoffAppearAnimations(n,h,t,d);null!==e&&(a.elapsed=e,a.isHandoff=!0)}}let i=!a.isHandoff&&!Fr(t,r);if("spring"===a.type&&(t.getVelocity()||a.velocity)&&(i=!1),t.animation&&(i=!1),i)continue;t.start(br(h,t,r,e.shouldReduceMotion&&ne.has(h)?{type:!1}:a));const s=t.animation;wr(u)&&(u.add(h),s.then(()=>u.remove(h))),c.push(s)}return s&&Promise.all(c).then(()=>{s&&_r(e,s)}),c}function Ur(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=Rt(e,t,n.custom);let{transition:a=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(a=n.transitionOverride);const i=r?()=>Promise.all(Br(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:i=0,staggerChildren:o,staggerDirection:s}=a;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,i=arguments.length>5?arguments[5]:void 0;const o=[],s=(e.variantChildren.size-1)*r,l=1===a?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r}:function(){return s-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r};return Array.from(e.variantChildren).sort(Hr).forEach((e,r)=>{e.notify("AnimationStart",t),o.push(Ur(e,t,M(M({},i),{},{delay:n+l(r)})).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,i+r,o,s,n)}:()=>Promise.resolve(),{when:s}=a;if(s){const[e,t]="beforeChildren"===s?[i,o]:[o,i];return e().then(()=>t())}return Promise.all([i(),o(n.delay)])}function Hr(e,t){return e.sortNodePosition(t)}const Wr=["transition","transitionEnd"],$r=[...I].reverse(),Yr=I.length;function Qr(e){return t=>Promise.all(t.map(t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const a=t.map(t=>Ur(e,t,r));n=Promise.all(a)}else if("string"===typeof t)n=Ur(e,t,r);else{const a="function"===typeof t?Rt(e,t,r.custom):t;n=Promise.all(Br(e,a,r))}return n.then(()=>e.notify("AnimationComplete",t))}(e,n,r)}))}function qr(e){let t=Qr(e);const n={animate:Gr(!0),whileInView:Gr(),whileHover:Gr(),whileTap:Gr(),whileDrag:Gr(),whileFocus:Gr(),exit:Gr()};let r=!0;const a=(t,n)=>{const r=Rt(e,n);if(r){const{transition:e,transitionEnd:n}=r,a=Oe(r,Wr);t=M(M(M({},t),a),n)}return t};function i(i,o){const s=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set;let d={},f=1/0;for(let t=0;t<Yr;t++){const h=$r[t],p=n[h],m=void 0!==s[h]?s[h]:l[h],g=z(m),v=h===o?p.isActive:null;!1===v&&(f=t);let y=m===l[h]&&m!==s[h]&&g;if(y&&r&&e.manuallyAnimateOnMount&&(y=!1),p.protectedKeys=M({},d),!p.isActive&&null===v||!m&&!p.prevProp||O(m)||"boolean"===typeof m)continue;let x=Xr(p.prevProp,m)||h===o&&p.isActive&&!y&&g||t>f&&g,b=!1;const w=Array.isArray(m)?m:[m];let k=w.reduce(a,{});!1===v&&(k={});const{prevResolvedValues:S={}}=p,E=M(M({},S),k),C=e=>{x=!0,c.has(e)&&(b=!0,c.delete(e)),p.needsAnimating[e]=!0};for(const e in E){const t=k[e],n=S[e];if(d.hasOwnProperty(e))continue;let r=!1;r=et(t)&&et(n)?!Lt(t,n):t!==n,r?void 0!==t?C(e):c.add(e):void 0!==t&&c.has(e)?C(e):p.protectedKeys[e]=!0}p.prevProp=m,p.prevResolvedValues=k,p.isActive&&(d=M(M({},d),k)),r&&e.blockInitialAnimation&&(x=!1),!x||y&&!b||u.push(...w.map(e=>({animation:e,options:M({type:h},i)})))}if(c.size){const t={};c.forEach(n=>{const r=e.getBaseTarget(n);void 0!==r&&(t[n]=r)}),u.push({animation:t})}let h=Boolean(u.length);return!r||!1!==s.initial&&s.initial!==s.animate||e.manuallyAnimateOnMount||(h=!1),r=!1,h?t(u):Promise.resolve()}return{animateChanges:i,setActive:function(t,r,a){var o;if(n[t].isActive===r)return Promise.resolve();null===(o=e.variantChildren)||void 0===o||o.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)}),n[t].isActive=r;const s=i(a,t);for(const e in n)n[e].protectedKeys={};return s},setAnimateFunction:function(n){t=n(e)},getState:()=>n}}function Xr(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!Lt(t,e)}function Gr(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let Kr=0;const Zr={animation:{Feature:class extends xt{constructor(e){super(e),e.animationState||(e.animationState=qr(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),O(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends xt{constructor(){super(...arguments),this.id=Kr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t,custom:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const a=this.node.animationState.setActive("exit",!e,{custom:null!==n&&void 0!==n?n:this.node.getProps().custom});t&&!e&&a.then(()=>t(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}}},Jr=(e,t)=>Math.abs(e-t);class ea{constructor(e,t){let{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=ra(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=Jr(e.x,t.x),r=Jr(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:r}=e,{timestamp:a}=h;this.history.push(M(M({},r),{},{timestamp:a}));const{onStart:i,onMove:o}=this.handlers;t||(i&&i(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=ta(t,this.transformPagePoint),d.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r,resumeAnimation:a}=this.handlers;if(this.dragSnapToOrigin&&a&&a(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const i=ra("pointercancel"===e.type?this.lastMoveEventInfo:ta(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,i),r&&r(e,i)},!ut(e))return;this.dragSnapToOrigin=a,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;const i=ta(ct(e),this.transformPagePoint),{point:o}=i,{timestamp:s}=h;this.history=[M(M({},o),{},{timestamp:s})];const{onSessionStart:l}=t;l&&l(e,ra(i,this.history)),this.removeListeners=ht(dt(this.contextWindow,"pointermove",this.handlePointerMove),dt(this.contextWindow,"pointerup",this.handlePointerUp),dt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function ta(e,t){return t?{point:t(e.point)}:e}function na(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ra(e,t){let{point:n}=e;return{point:n,delta:na(n,ia(t)),offset:na(n,aa(t)),velocity:oa(t,.1)}}function aa(e){return e[0]}function ia(e){return e[e.length-1]}function oa(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const a=ia(e);for(;n>=0&&(r=e[n],!(a.timestamp-r.timestamp>At(t)));)n--;if(!r)return{x:0,y:0};const i=Dt(a.timestamp-r.timestamp);if(0===i)return{x:0,y:0};const o={x:(a.x-r.x)/i,y:(a.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function sa(e){return e.max-e.min}function la(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01;return Math.abs(e-t)<=n}function ua(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=cn(t.min,t.max,e.origin),e.scale=sa(n)/sa(t),(la(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=cn(n.min,n.max,e.origin)-e.originPoint,(la(e.translate)||isNaN(e.translate))&&(e.translate=0)}function ca(e,t,n,r){ua(e.x,t.x,n.x,r?r.originX:void 0),ua(e.y,t.y,n.y,r?r.originY:void 0)}function da(e,t,n){e.min=n.min+t.min,e.max=e.min+sa(t)}function fa(e,t,n){e.min=t.min-n.min,e.max=e.min+sa(t)}function ha(e,t,n){fa(e.x,t.x,n.x),fa(e.y,t.y,n.y)}function pa(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function ma(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const ga=.35;function va(e,t,n){return{min:ya(e,t),max:ya(e,n)}}function ya(e,t){return"number"===typeof e?e:e[t]||0}const xa=()=>({x:{min:0,max:0},y:{min:0,max:0}});function ba(e){return[e("x"),e("y")]}function wa(e){let{top:t,left:n,right:r,bottom:a}=e;return{x:{min:n,max:r},y:{min:t,max:a}}}function ka(e){return void 0===e||1===e}function Sa(e){let{scale:t,scaleX:n,scaleY:r}=e;return!ka(t)||!ka(n)||!ka(r)}function Ea(e){return Sa(e)||Ca(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Ca(e){return Pa(e.x)||Pa(e.y)}function Pa(e){return e&&"0%"!==e}function Na(e,t,n){return n+t*(e-n)}function ja(e,t,n,r,a){return void 0!==a&&(e=Na(e,a,r)),Na(e,n,r)+t}function Ta(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0;e.min=ja(e.min,t,n,r,a),e.max=ja(e.max,t,n,r,a)}function Ma(e,t){let{x:n,y:r}=t;Ta(e.x,n.translate,n.scale,n.originPoint),Ta(e.y,r.translate,r.scale,r.originPoint)}function La(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Ra(e,t){e.min=e.min+t,e.max=e.max+t}function Aa(e,t,n){let[r,a,i]=n;const o=void 0!==t[i]?t[i]:.5,s=cn(e.min,e.max,o);Ta(e,t[r],t[a],s,t.scale)}const Da=["x","scaleX","originX"],Va=["y","scaleY","originY"];function _a(e,t){Aa(e.x,t,Da),Aa(e.y,t,Va)}function za(e,t){return wa(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const Oa=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},Ia=new WeakMap;class Fa{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e){let{snapToCursor:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:r}=this.getProps();this.panSession=new ea(e,{onSessionStart:e=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ct(e,"page").point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:a}=this.getProps();if(n&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=vt(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ba(e=>{let t=this.getAxisMotionValue(e).get()||0;if(ke.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=sa(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t}),a&&d.update(()=>a(e,t),!1,!0);const{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:a,onDrag:i}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:o}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(o),void(null!==this.currentDirection&&a&&a(this.currentDirection));this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),i&&i(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>ba(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:Oa(this.visualElement)})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&d.update(()=>a(e,t))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!Ba(e,r,this.currentDirection))return;const a=this.getAxisMotionValue(e);let i=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(i=function(e,t,n){let{min:r,max:a}=t;return void 0!==r&&e<r?e=n?cn(r,e,n.min):Math.max(e,r):void 0!==a&&e>a&&(e=n?cn(a,e,n.max):Math.min(e,a)),e}(i,this.constraints[e],this.elastic[e])),a.set(i)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,a=this.constraints;t&&_(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!r)&&function(e,t){let{top:n,left:r,bottom:a,right:i}=t;return{x:pa(e.x,r,i),y:pa(e.y,n,a)}}(r.layoutBox,t),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ga;return!1===e?e=0:!0===e&&(e=ga),{x:va(e,"left","right"),y:va(e,"top","bottom")}}(n),a!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ba(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!_(e))return!1;const n=e.current;S(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");const{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const a=function(e,t,n){const r=za(e,n),{scroll:a}=t;return a&&(Ra(r.x,a.offset.x),Ra(r.y,a.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let i=function(e,t){return{x:ma(e.x,t.x),y:ma(e.y,t.y)}}(r.layout.layoutBox,a);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(i));this.hasMutatedConstraints=!!e,e&&(i=wa(e))}return i}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:a,dragSnapToOrigin:i,onDragTransitionEnd:o}=this.getProps(),s=this.constraints||{},l=ba(o=>{if(!Ba(o,t,this.currentDirection))return;let l=s&&s[o]||{};i&&(l={min:0,max:0});const u=r?200:1e6,c=r?40:1e7,d=M(M({type:"inertia",velocity:n?e[o]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10},a),l);return this.startAxisValueAnimation(o,d)});return Promise.all(l).then(o)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return n.start(br(e,n,0,t))}stopAnimation(){ba(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){ba(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t="_drag"+e.toUpperCase(),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){ba(t=>{const{drag:n}=this.getProps();if(!Ba(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,a=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:i}=r.layout.layoutBox[t];a.set(e[t]-cn(n,i,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!_(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};ba(e=>{const t=this.getAxisMotionValue(e);if(t){const n=t.get();r[e]=function(e,t){let n=.5;const r=sa(e),a=sa(t);return a>r?n=Mn(t.min,t.max-r,e.min):r>a&&(n=Mn(e.min,e.max-a,t.min)),de(0,1,n)}({min:n,max:n},this.constraints[e])}});const{transformTemplate:a}=this.visualElement.getProps();this.visualElement.current.style.transform=a?a({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),ba(t=>{if(!Ba(t,e,null))return;const n=this.getAxisMotionValue(t),{min:a,max:i}=this.constraints[t];n.set(cn(a,i,r[t]))})}addListeners(){if(!this.visualElement.current)return;Ia.set(this.visualElement,this);const e=dt(this.visualElement.current,"pointerdown",e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{const{dragConstraints:e}=this.getProps();_(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),t();const a=lt(window,"resize",()=>this.scalePositionWithinConstraints()),i=n.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&(ba(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{a(),e(),r(),i&&i()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:a=!1,dragElastic:i=ga,dragMomentum:o=!0}=e;return M(M({},e),{},{drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:a,dragElastic:i,dragMomentum:o})}}function Ba(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const Ua=e=>(t,n)=>{e&&d.update(()=>e(t,n))};const Ha={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Wa(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const $a={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!Se.test(e))return e;e=parseFloat(e)}const n=Wa(e,t.target.x),r=Wa(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},Ya={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const a=e,i=En.parse(e);if(i.length>5)return a;const o=En.createTransformer(e),s="number"!==typeof i[0]?1:0,l=r.x.scale*n.x,u=r.y.scale*n.y;i[0+s]/=l,i[1+s]/=u;const c=cn(l,u,.5);return"number"===typeof i[2+s]&&(i[2+s]/=c),"number"===typeof i[3+s]&&(i[3+s]/=c),o(i)}};class Qa extends r.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:a}=e;var i;i=Xa,Object.assign(ee,i),a&&(t.group&&t.group.add(a),n&&n.register&&r&&n.register(a),a.root.didUpdate(),a.addEventListener("animationComplete",()=>{this.safeToRemove()}),a.setOptions(M(M({},a.options),{},{onExitComplete:()=>this.safeToRemove()}))),Ha.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:a}=this.props,i=n.projection;return i?(i.isPresent=a,r||e.layoutDependency!==t||void 0===t?i.willUpdate():this.safeToRemove(),e.isPresent!==a&&(a?i.promote():i.relegate()||d.postRender(()=>{const e=i.getStack();e&&e.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function qa(e){const[t,n]=function(){const e=(0,r.useContext)(m);if(null===e)return[!0,null];const{isPresent:t,onExitComplete:n,register:a}=e,i=(0,r.useId)();return(0,r.useEffect)(()=>a(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}(),a=(0,r.useContext)(w);return r.createElement(Qa,M(M({},e),{},{layoutGroup:a,switchLayoutGroup:(0,r.useContext)(Q),isPresent:t,safeToRemove:n}))}const Xa={borderRadius:M(M({},$a),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:$a,borderTopRightRadius:$a,borderBottomLeftRadius:$a,borderBottomRightRadius:$a,boxShadow:Ya},Ga=["TopLeft","TopRight","BottomLeft","BottomRight"],Ka=Ga.length,Za=e=>"string"===typeof e?parseFloat(e):e,Ja=e=>"number"===typeof e||Se.test(e);function ei(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const ti=ri(0,.5,Xt),ni=ri(.5,.95,l);function ri(e,t,n){return r=>r<e?0:r>t?1:n(Mn(e,t,r))}function ai(e,t){e.min=t.min,e.max=t.max}function ii(e,t){ai(e.x,t.x),ai(e.y,t.y)}function oi(e,t,n,r,a){return e=Na(e-=t,1/n,r),void 0!==a&&(e=Na(e,1/a,r)),e}function si(e,t,n,r,a){let[i,o,s]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,a=arguments.length>4?arguments[4]:void 0,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;ke.test(t)&&(t=parseFloat(t),t=cn(o.min,o.max,t/100)-o.min);if("number"!==typeof t)return;let s=cn(i.min,i.max,r);e===i&&(s-=t),e.min=oi(e.min,t,n,s,a),e.max=oi(e.max,t,n,s,a)}(e,t[i],t[o],t[s],t.scale,r,a)}const li=["x","scaleX","originX"],ui=["y","scaleY","originY"];function ci(e,t,n,r){si(e.x,t,li,n?n.x:void 0,r?r.x:void 0),si(e.y,t,ui,n?n.y:void 0,r?r.y:void 0)}function di(e){return 0===e.translate&&1===e.scale}function fi(e){return di(e.x)&&di(e.y)}function hi(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function pi(e){return sa(e.x)/sa(e.y)}class mi{constructor(){this.members=[]}add(e){Sr(this.members,e),e.scheduleRender()}remove(e){if(Er(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex(t=>e===t);if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function gi(e,t,n){let r="";const a=e.x.translate/t.x,i=e.y.translate/t.y;if((a||i)&&(r="translate3d(".concat(a,"px, ").concat(i,"px, 0) ")),1===t.x&&1===t.y||(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{rotate:e,rotateX:t,rotateY:a}=n;e&&(r+="rotate(".concat(e,"deg) ")),t&&(r+="rotateX(".concat(t,"deg) ")),a&&(r+="rotateY(".concat(a,"deg) "))}const o=e.x.scale*t.x,s=e.y.scale*t.y;return 1===o&&1===s||(r+="scale(".concat(o,", ").concat(s,")")),r||"none"}const vi=(e,t)=>e.depth-t.depth;class yi{constructor(){this.children=[],this.isDirty=!1}add(e){Sr(this.children,e),this.isDirty=!0}remove(e){Er(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(vi),this.isDirty=!1,this.children.forEach(e)}}const xi=["","X","Y","Z"],bi={visibility:"hidden"};let wi=0;const ki={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Si(e){let{attachResizeListener:t,defaultParent:n,measureScroll:r,checkIsScrollRoot:a,resetTransform:i}=e;return class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===n||void 0===n?void 0:n();this.id=wi++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{var e;this.projectionUpdateScheduled=!1,ki.totalNodes=ki.resolvedTargetDeltas=ki.recalculatedProjection=0,this.nodes.forEach(Pi),this.nodes.forEach(Ai),this.nodes.forEach(Di),this.nodes.forEach(Ni),e=ki,window.MotionDebug&&window.MotionDebug.record(e)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new yi)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new Cr),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];t&&t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root.hasTreeAnimated;if(this.instance)return;var r;this.isSVG=(r=e)instanceof SVGElement&&"svg"!==r.tagName,this.instance=e;const{layoutId:a,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(i||a)&&(this.isLayoutDirty=!0),t){let n;const r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){const n=performance.now(),r=a=>{let{timestamp:i}=a;const o=i-n;o>=t&&(f(r),e(o-t))};return d.read(r,!0),()=>f(r)}(r,250),Ha.hasAnimatedSinceResize&&(Ha.hasAnimatedSinceResize=!1,this.nodes.forEach(Ri))})}a&&this.root.registerSharedNode(a,this),!1!==this.options.animate&&o&&(a||i)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n,hasRelativeTargetChanged:r,layout:a}=e;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const i=this.options.transition||o.getDefaultTransition()||Fi,{onLayoutAnimationStart:s,onLayoutAnimationComplete:l}=o.getProps(),u=!this.targetLayout||!hi(this.targetLayout,a)||r,c=!n&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||c||n&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,c);const e=M(M({},yr(i,"layout")),{},{onPlay:s,onComplete:l});(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else n||Ri(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=a})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Vi),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){const e=this.path[a];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Ti);this.isUpdating||this.nodes.forEach(Mi),this.isUpdating=!1,this.nodes.forEach(Li),this.nodes.forEach(Ei),this.nodes.forEach(Ci),this.clearAllSnapshots();const e=performance.now();h.delta=de(0,1e3/60,e-h.timestamp),h.timestamp=e,h.isProcessing=!0,p.update.process(h),p.preRender.process(h),p.render.process(h),h.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(ji),this.sharedNodes.forEach(_i)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,d.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){d.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:a(this.instance),offset:r(this.instance)})}resetTransform(){if(!i)return;const e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!fi(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&(t||Ea(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),Hi((r=n).x),Hi(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox(),{scroll:n}=this.root;return n&&(Ra(t.x,n.offset.x),Ra(t.y,n.offset.y)),t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};ii(t,e);for(let n=0;n<this.path.length;n++){const r=this.path[n],{scroll:a,options:i}=r;if(r!==this.root&&a&&i.layoutScroll){if(a.isRoot){ii(t,e);const{scroll:n}=this.root;n&&(Ra(t.x,-n.offset.x),Ra(t.y,-n.offset.y))}Ra(t.x,a.offset.x),Ra(t.y,a.offset.y)}}return t}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};ii(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&_a(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),Ea(e.latestValues)&&_a(n,e.latestValues)}return Ea(this.latestValues)&&_a(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};ii(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!Ea(e.latestValues))continue;Sa(e.latestValues)&&e.updateSnapshot();const r=xa();ii(r,e.measurePageBox()),ci(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return Ea(this.latestValues)&&ci(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options=M(M(M({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==h.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];var t;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:a,layoutId:i}=this.options;if(this.layout&&(a||i)){if(this.resolvedRelativeTargetAt=h.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ha(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),ii(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var o,s,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,l=this.relativeParent.target,da(o.x,s.x,l.x),da(o.y,s.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):ii(this.target,this.layout.layoutBox),Ma(this.target,this.targetDelta)):ii(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ha(this.relativeTargetOrigin,this.target,e.target),ii(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ki.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Sa(this.parent.latestValues)&&!Ca(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===h.timestamp&&(r=!1),r)return;const{layout:a,layoutId:i}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!a&&!i)return;ii(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const a=n.length;if(!a)return;let i,o;t.x=t.y=1;for(let s=0;s<a;s++){i=n[s],o=i.projectionDelta;const a=i.instance;a&&a.style&&"contents"===a.style.display||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&_a(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Ma(e,o)),r&&Ea(i.latestValues)&&_a(e,i.latestValues))}t.x=La(t.x),t.y=La(t.y)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox);const{target:l}=t;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;ca(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=gi(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===o&&this.treeScale.y===s||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),ki.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.options.scheduleRender&&this.options.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.snapshot,r=n?n.latestValues:{},a=M({},this.latestValues),i={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const o={x:{min:0,max:0},y:{min:0,max:0}},s=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(s&&!u&&!0===this.options.crossfade&&!this.path.some(Ii));let d;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;var l,f,h,p,m,g;zi(i.x,e.x,n),zi(i.y,e.y,n),this.setTargetDelta(i),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ha(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,m=o,g=n,Oi(h.x,p.x,m.x,g),Oi(h.y,p.y,m.y,g),d&&(l=this.relativeTarget,f=d,l.x.min===f.x.min&&l.x.max===f.x.max&&l.y.min===f.y.min&&l.y.max===f.y.max)&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),ii(d,this.relativeTarget)),s&&(this.animationValues=a,function(e,t,n,r,a,i){a?(e.opacity=cn(0,void 0!==n.opacity?n.opacity:1,ti(r)),e.opacityExit=cn(void 0!==t.opacity?t.opacity:1,0,ni(r))):i&&(e.opacity=cn(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let o=0;o<Ka;o++){const a="border".concat(Ga[o],"Radius");let i=ei(t,a),s=ei(n,a);void 0===i&&void 0===s||(i||(i=0),s||(s=0),0===i||0===s||Ja(i)===Ja(s)?(e[a]=Math.max(cn(Za(i),Za(s),r),0),(ke.test(s)||ke.test(i))&&(e[a]+="%")):e[a]=s)}(t.rotate||n.rotate)&&(e.rotate=cn(t.rotate||0,n.rotate||0,r))}(a,r,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=d.update(()=>{Ha.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){const r=ae(e)?e:jr(e);return r.start(br("",r,t,n)),r.animation}(0,1e3,M(M({},e),{},{onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:a}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&Wi(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=sa(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=sa(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}ii(t,n),_a(t,a),ca(this.projectionDeltaWithTransform,this.layoutCorrected,t,a)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new mi);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(t=!0),!t)return;const r={};for(let a=0;a<xi.length;a++){const t="rotate"+xi[a];n[t]&&(r[t]=n[t],e.setStaticValue(t,0))}e.render();for(const a in r)e.setStaticValue(a,r[a]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return bi;const r={visibility:""},a=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=nt(null===e||void 0===e?void 0:e.pointerEvents)||"",r.transform=a?a(this.latestValues,""):"none",r;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nt(null===e||void 0===e?void 0:e.pointerEvents)||""),this.hasProjected&&!Ea(this.latestValues)&&(t.transform=a?a({},""):"none",this.hasProjected=!1),t}const o=i.animationValues||i.latestValues;this.applyTransformsToTarget(),r.transform=gi(this.projectionDeltaWithTransform,this.treeScale,o),a&&(r.transform=a(o,r.transform));const{x:s,y:l}=this.projectionDelta;r.transformOrigin="".concat(100*s.origin,"% ").concat(100*l.origin,"% 0"),i.animationValues?r.opacity=i===this?null!==(n=null!==(t=o.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=i===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0;for(const u in ee){if(void 0===o[u])continue;const{correct:e,applyTo:t}=ee[u],n="none"===r.transform?o[u]:e(o[u],i);if(t){const e=t.length;for(let a=0;a<e;a++)r[t[a]]=n}else r[u]=n}return this.options.layoutId&&(r.pointerEvents=i===this?nt(null===e||void 0===e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(Ti),this.root.sharedNodes.clear()}}}function Ei(e){e.updateLayout()}function Ci(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:a}=e.options,i=n.source!==e.layout.source;"size"===a?ba(e=>{const r=i?n.measuredBox[e]:n.layoutBox[e],a=sa(r);r.min=t[e].min,r.max=r.min+a}):Wi(a,n.layoutBox,t)&&ba(r=>{const a=i?n.measuredBox[r]:n.layoutBox[r],o=sa(t[r]);a.max=a.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};ca(o,t,n.layoutBox);const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};i?ca(s,e.applyTransform(r,!0),n.measuredBox):ca(s,t,n.layoutBox);const l=!fi(o);let u=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:a,layout:i}=r;if(a&&i){const o={x:{min:0,max:0},y:{min:0,max:0}};ha(o,n.layoutBox,a.layoutBox);const s={x:{min:0,max:0},y:{min:0,max:0}};ha(s,t,i.layoutBox),hi(o,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function Pi(e){ki.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Ni(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function ji(e){e.clearSnapshot()}function Ti(e){e.clearMeasurements()}function Mi(e){e.isLayoutDirty=!1}function Li(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Ri(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Ai(e){e.resolveTargetDelta()}function Di(e){e.calcProjection()}function Vi(e){e.resetRotation()}function _i(e){e.removeLeadSnapshot()}function zi(e,t,n){e.translate=cn(t.translate,0,n),e.scale=cn(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Oi(e,t,n,r){e.min=cn(t.min,n.min,r),e.max=cn(t.max,n.max,r)}function Ii(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const Fi={duration:.45,ease:[.4,0,.1,1]},Bi=e=>"undefined"!==typeof navigator&&navigator.userAgent.toLowerCase().includes(e),Ui=Bi("applewebkit/")&&!Bi("chrome/")?Math.round:l;function Hi(e){e.min=Ui(e.min),e.max=Ui(e.max)}function Wi(e,t,n){return"position"===e||"preserve-aspect"===e&&!la(pi(t),pi(n),.2)}const $i=Si({attachResizeListener:(e,t)=>lt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Yi={current:void 0},Qi=Si({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Yi.current){const e=new $i({});e.mount(window),e.setOptions({layoutScroll:!0}),Yi.current=e}return Yi.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),qi={pan:{Feature:class extends xt{constructor(){super(...arguments),this.removePointerDownListener=l}onPointerDown(e){this.session=new ea(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Oa(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:Ua(e),onStart:Ua(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&d.update(()=>r(e,t))}}}mount(){this.removePointerDownListener=dt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends xt{constructor(e){super(e),this.removeGroupControls=l,this.removeListeners=l,this.controls=new Fa(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||l}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Qi,MeasureLayout:qa}};const Xi=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Gi(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;S(n<=4,'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.'));const[r,a]=function(e){const t=Xi.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const e=i.trim();return kr(e)?parseFloat(e):e}return ue(a)?Gi(a,t,n+1):a}function Ki(e,t,n){let r=Object.assign({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(t),t));const a=e.current;if(!(a instanceof Element))return{target:r,transitionEnd:n};n&&(n=M({},n)),e.values.forEach(e=>{const t=e.get();if(!ue(t))return;const n=Gi(t,a);n&&e.set(n)});for(const i in r){const e=r[i];if(!ue(e))continue;const t=Gi(e,a);t&&(r[i]=t,n||(n={}),void 0===n[i]&&(n[i]=e))}return{target:r,transitionEnd:n}}const Zi=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Ji=e=>Zi.has(e),eo=e=>e===fe||e===Se,to=(e,t)=>parseFloat(e.split(", ")[t]),no=(e,t)=>(n,r)=>{let{transform:a}=r;if("none"===a||!a)return 0;const i=a.match(/^matrix3d\((.+)\)$/);if(i)return to(i[1],t);{const t=a.match(/^matrix\((.+)\)$/);return t?to(t[1],e):0}},ro=new Set(["x","y","z"]),ao=te.filter(e=>!ro.has(e));const io={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:a="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(a)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:a="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(a)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:no(4,13),y:no(5,14)};io.translateX=io.x,io.translateY=io.y;const oo=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t=M({},t),r=M({},r);const a=Object.keys(t).filter(Ji);let o=[],s=!1;const l=[];if(a.forEach(a=>{const i=e.getValue(a);if(!e.hasValue(a))return;let u=n[a],c=Lr(u);const d=t[a];let f;if(et(d)){const e=d.length,t=null===d[0]?1:0;u=d[t],c=Lr(u);for(let n=t;n<e&&null!==d[n];n++)f?S(Lr(d[n])===f,"All keyframes must be of the same type"):(f=Lr(d[n]),S(f===c||eo(c)&&eo(f),"Keyframes must be of the same dimension as the current value"))}else f=Lr(d);if(c!==f)if(eo(c)&&eo(f)){const e=i.get();"string"===typeof e&&i.set(parseFloat(e)),"string"===typeof d?t[a]=parseFloat(d):Array.isArray(d)&&f===Se&&(t[a]=d.map(parseFloat))}else(null===c||void 0===c?void 0:c.transform)&&(null===f||void 0===f?void 0:f.transform)&&(0===u||0===d)?0===u?i.set(f.transform(u)):t[a]=c.transform(d):(s||(o=function(e){const t=[];return ao.forEach(n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),s=!0),l.push(a),r[a]=void 0!==r[a]?r[a]:t[a],i.jump(d))}),l.length){const n=l.indexOf("height")>=0?window.pageYOffset:null,a=((e,t,n)=>{const r=t.measureViewportBox(),a=t.current,i=getComputedStyle(a),{display:o}=i,s={};"none"===o&&t.setStaticValue("display",e.display||"block"),n.forEach(e=>{s[e]=io[e](r,i)}),t.render();const l=t.measureViewportBox();return n.forEach(n=>{const r=t.getValue(n);r&&r.jump(s[n]),e[n]=io[n](l,i)}),e})(t,e,l);return o.length&&o.forEach(t=>{let[n,r]=t;e.getValue(n).set(r)}),e.render(),i&&null!==n&&window.scrollTo({top:n}),{target:a,transitionEnd:r}}return{target:t,transitionEnd:r}};function so(e,t,n,r){return(e=>Object.keys(e).some(Ji))(t)?oo(e,t,n,r):{target:t,transitionEnd:r}}const lo={current:null},uo={current:!1};const co=new WeakMap,fo=["willChange"],ho=["children"],po=Object.keys(Y),mo=po.length,go=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],vo=F.length;class yo{constructor(e){let{parent:t,props:n,presenceContext:r,reducedMotionConfig:a,visualState:i}=e,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>d.render(this.render,!1,!0);const{latestValues:s,renderState:l}=i;this.latestValues=s,this.baseTarget=M({},s),this.initialValues=n.initial?M({},s):{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=a,this.options=o,this.isControllingVariants=B(n),this.isVariantNode=U(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const u=this.scrapeMotionValuesFromProps(n,{}),{willChange:c}=u,f=Oe(u,fo);for(const d in f){const e=f[d];void 0!==s[d]&&ae(e)&&(e.set(s[d],!1),wr(c)&&c.add(d))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,co.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),uo.current||function(){if(uo.current=!0,i)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>lo.current=e.matches;e.addListener(t),t()}else lo.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||lo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){co.delete(this.current),this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){const n=ne.has(e),r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&d.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{r(),a()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures(e,t,n,r){let a,i,{children:o}=e,s=Oe(e,ho);for(let l=0;l<mo;l++){const e=po[l],{isEnabled:t,Feature:n,ProjectionNode:r,MeasureLayout:o}=Y[e];r&&(a=r),t(s)&&(!this.features[e]&&n&&(this.features[e]=new n(this)),o&&(i=o))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&a){this.projection=new a(this.latestValues,this.parent&&this.parent.projection);const{layoutId:e,layout:t,drag:n,dragConstraints:i,layoutScroll:o,layoutRoot:l}=s;this.projection.setOptions({layoutId:e,layout:t,alwaysMeasureLayout:Boolean(n)||i&&_(i),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"===typeof t?t:"both",initialPromotionConfig:r,layoutScroll:o,layoutRoot:l})}return i}updateFeatures(){for(const e in this.features){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<go.length;n++){const t=go[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){const{willChange:r}=t;for(const a in t){const i=t[a],o=n[a];if(ae(i))e.addValue(a,i),wr(r)&&r.add(a);else if(ae(o))e.addValue(a,jr(i,{owner:e})),wr(r)&&r.remove(a);else if(o!==i)if(e.hasValue(a)){const t=e.getValue(a);!t.hasAnimated&&t.set(i)}else{const t=e.getStaticValue(a);e.addValue(a,jr(void 0!==t?t:i,{owner:e}))}}for(const a in n)void 0===t[a]&&e.removeValue(a);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0])return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}const e={};for(let t=0;t<vo;t++){const n=F[t],r=this.props[n];(z(r)||!1===r)&&(e[n]=r)}return e}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=jr(t,{owner:this}),this.addValue(e,n)),n}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:n}=this.props,r="string"===typeof n||"object"===typeof n?null===(t=Je(this.props,n))||void 0===t?void 0:t[e]:void 0;if(n&&void 0!==r)return r;const a=this.getBaseTargetFromProps(this.props,e);return void 0===a||ae(a)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:a}on(e,t){return this.events[e]||(this.events[e]=new Cr),this.events[e].add(t)}notify(e){if(this.events[e]){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.events[e].notify(...n)}}}const xo=["transition","transitionEnd"];class bo extends yo{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}makeTargetAnimatableFromInstance(e,t,n){let{transition:r,transitionEnd:a}=e,i=Oe(e,xo),{transformValues:o}=t,s=function(e,t,n){const r={};for(const a in e){const e=zr(a,t);if(void 0!==e)r[a]=e;else{const e=n.getValue(a);e&&(r[a]=e.get())}}return r}(i,r||{},this);if(o&&(a&&(a=o(a)),i&&(i=o(i)),s&&(s=o(s))),n){!function(e,t,n){var r,a;const i=Object.keys(t).filter(t=>!e.hasValue(t)),o=i.length;if(o)for(let s=0;s<o;s++){const o=i[s],l=t[o];let u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(a=null!==(r=n[o])&&void 0!==r?r:e.readValue(o))&&void 0!==a?a:t[o]),void 0!==u&&null!==u&&("string"===typeof u&&(kr(u)||mr(u))?u=parseFloat(u):!Ar(u)&&En.test(l)&&(u=pr(o,l)),e.addValue(o,jr(u,{owner:e})),void 0===n[o]&&(n[o]=u),null!==u&&e.setBaseTarget(o,u))}}(this,i,s);const e=((e,t,n,r)=>{const a=Ki(e,t,r);return so(e,t=a.target,n,r=a.transitionEnd)})(this,i,s,a);a=e.transitionEnd,i=e.target}return M({transition:r,transitionEnd:a},i)}}class wo extends bo{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(ne.has(t)){const e=hr(t);return e&&e.default||0}{const r=(n=e,window.getComputedStyle(n)),a=(le(t)?r.getPropertyValue(t):r[t])||0;return"string"===typeof a?a.trim():a}var n}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return za(e,n)}build(e,t,n,r){Te(e,t,n,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return Ke(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;ae(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}renderInstance(e,t,n,r){qe(e,t,n,r)}}class ko extends bo{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(ne.has(t)){const e=hr(t);return e&&e.default||0}return t=Xe.has(t)?t:D(t),e.getAttribute(t)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(e,t){return Ze(e,t)}build(e,t,n,r){He(e,t,n,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,n,r){Ge(e,t,0,r)}mount(e){this.isSVGTag=$e(e.tagName),super.mount(e)}}const So=(e,t)=>J(e)?new ko(t,{enableHardwareAcceleration:!1}):new wo(t,{enableHardwareAcceleration:!0}),Eo={layout:{ProjectionNode:Qi,MeasureLayout:qa}},Co=M(M(M(M({},Zr),Mt),qi),Eo),Po=K((e,t)=>function(e,t,n,r){let{forwardMotionProps:a=!1}=t;return M(M({},J(e)?ot:st),{},{preloadedFeatures:n,useRender:Qe(a),createVisualElement:r,Component:e})}(e,t,Co,So));var No=n(579);const jo=()=>(0,No.jsxs)("ul",{className:"w-64 flex flex-col gap-1 border-l border-cyan-500/30 pl-1",children:[(0,No.jsxs)("li",{className:"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20",children:[(0,No.jsxs)("button",{className:"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-purple-300 transition-all active:scale-95 hover:text-purple-200",children:[(0,No.jsx)("div",{className:"rounded-lg border-2 border-purple-400/50 bg-purple-900/50 p-1",children:(0,No.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",className:"size-6",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"})})}),(0,No.jsx)("div",{className:"font-semibold text-cyan-100",children:"Notifications"})]}),(0,No.jsx)("div",{className:"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]",children:(0,No.jsx)("div",{className:"overflow-hidden",children:(0,No.jsxs)("ul",{className:"divide-y divide-gray-600/50 p-4 pt-0",children:[(0,No.jsxs)("li",{className:"py-2",children:[(0,No.jsxs)("div",{className:"flex items-center justify-between",children:[(0,No.jsx)("button",{className:"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100",children:"Email"}),(0,No.jsx)("div",{className:"text-sm text-gray-400",children:"2m ago"})]}),(0,No.jsx)("div",{className:"text-xs text-gray-500",children:"from Wanye Enterprises"})]}),(0,No.jsxs)("li",{className:"py-1",children:[(0,No.jsxs)("div",{className:"flex items-center justify-between",children:[(0,No.jsx)("button",{className:"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100",children:"Request"}),(0,No.jsx)("div",{className:"text-sm text-gray-400",children:"14m ago"})]}),(0,No.jsx)("div",{className:"text-xs text-gray-500",children:"from Acme Corporation"})]})]})})})]}),(0,No.jsxs)("li",{className:"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20",children:[(0,No.jsxs)("button",{className:"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-blue-300 transition-all active:scale-95 hover:text-blue-200",children:[(0,No.jsx)("div",{className:"rounded-lg border-2 border-blue-400/50 bg-blue-900/50 p-1",children:(0,No.jsxs)("svg",{className:"size-6",stroke:"currentColor",strokeWidth:"1.5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,No.jsx)("path",{d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z",strokeLinejoin:"round",strokeLinecap:"round"}),(0,No.jsx)("path",{d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z",strokeLinejoin:"round",strokeLinecap:"round"})]})}),(0,No.jsx)("div",{className:"font-semibold text-cyan-100",children:"Settings"})]}),(0,No.jsx)("div",{className:"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]",children:(0,No.jsx)("div",{className:"overflow-hidden",children:(0,No.jsxs)("ul",{className:"divide-y divide-gray-600/50 p-4 pt-0",children:[(0,No.jsxs)("li",{className:"py-2",children:[(0,No.jsxs)("div",{className:"flex items-center justify-between",children:[(0,No.jsx)("button",{className:"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100",children:"System Preferences"}),(0,No.jsx)("div",{className:"text-sm text-gray-400 transition-all peer-hover:translate-x-1",children:(0,No.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",className:"size-4",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})})})]}),(0,No.jsx)("div",{className:"text-xs text-gray-500",children:"Default Settings / Profile"})]}),(0,No.jsxs)("li",{className:"py-1",children:[(0,No.jsxs)("div",{className:"group/title flex items-center justify-between",children:[(0,No.jsx)("button",{className:"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100",children:"Theme"}),(0,No.jsx)("div",{className:"text-sm text-gray-400 transition-all peer-hover:translate-x-1",children:(0,No.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",className:"size-4",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})})})]}),(0,No.jsx)("div",{className:"text-xs text-gray-500",children:"Light / Dark Mode"})]})]})})})]}),(0,No.jsxs)("li",{className:"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20",children:[(0,No.jsxs)("button",{className:"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-green-300 transition-all active:scale-95 hover:text-green-200",children:[(0,No.jsx)("div",{className:"rounded-lg border-2 border-green-400/50 bg-green-900/50 p-1",children:(0,No.jsx)("svg",{className:"size-6",stroke:"currentColor",strokeWidth:"1.5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,No.jsx)("path",{d:"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z",strokeLinejoin:"round",strokeLinecap:"round"})})}),(0,No.jsx)("div",{className:"font-semibold text-cyan-100",children:"Chat"})]}),(0,No.jsx)("div",{className:"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]",children:(0,No.jsxs)("div",{className:"overflow-hidden",children:[(0,No.jsxs)("ul",{className:"border-t border-gray-600/50 p-4 pt-0.5",children:[(0,No.jsxs)("li",{className:"flex flex-col items-end",children:[(0,No.jsx)("div",{className:"text-right text-xs text-gray-400",children:"8:34 AM"}),(0,No.jsx)("div",{className:"w-40 rounded-lg bg-cyan-600/70 px-2 py-1 text-right text-sm text-white",children:"Hey JARVIS, what's your status?"})]}),(0,No.jsxs)("li",{className:"flex flex-col items-start",children:[(0,No.jsx)("div",{className:"text-right text-xs text-gray-400",children:"8:37 AM"}),(0,No.jsx)("div",{className:"w-40 rounded-lg bg-gray-700/80 px-2 py-1 text-sm text-cyan-100",children:"All systems operational."})]})]}),(0,No.jsxs)("div",{className:"relative",children:[(0,No.jsx)("input",{className:"h-8 w-full rounded-b-lg border border-gray-600/50 bg-gray-800/80 pl-2 text-sm text-cyan-100 placeholder-gray-400 focus:border-cyan-400/50 focus:outline-none focus:ring-1 focus:ring-cyan-400/30",placeholder:"Reply",type:"text"}),(0,No.jsx)("button",{className:"absolute bottom-0 right-2 top-0 my-auto size-fit cursor-pointer text-cyan-400 hover:text-cyan-300",children:(0,No.jsx)("svg",{className:"size-5",stroke:"currentColor",strokeWidth:"1.5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,No.jsx)("path",{d:"m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z",strokeLinejoin:"round",strokeLinecap:"round"})})})]})]})})]})]});const To=new class{constructor(){this.baseURL="http://localhost:5000/api"}async makeRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n="".concat(this.baseURL).concat(e),r=M(M({},{headers:{"Content-Type":"application/json"}}),t);try{const e=await fetch(n,r);if(!e.ok){const t=await e.json().catch(()=>({}));throw new Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}return await e.json()}catch(a){throw console.error("API request failed for ".concat(e,":"),a),a}}async healthCheck(){return this.makeRequest("/health")}async getState(){return this.makeRequest("/state")}async setState(e){return this.makeRequest("/state",{method:"POST",body:JSON.stringify({state:e})})}async sendMessage(e){return this.makeRequest("/chat",{method:"POST",body:JSON.stringify({message:e})})}async startSpeechRecognition(){return this.makeRequest("/speech-to-text",{method:"POST"})}async textToSpeech(e){return this.makeRequest("/text-to-speech",{method:"POST",body:JSON.stringify({text:e})})}async getConversationHistory(){return this.makeRequest("/conversation-history")}async clearConversationHistory(){return this.makeRequest("/conversation-history",{method:"DELETE"})}async isServerRunning(){try{return await this.healthCheck(),!0}catch(e){return!1}}async pollState(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;const n=async()=>{try{const t=await this.getState();e(t)}catch(t){console.error("State polling error:",t)}};return await n(),setInterval(n,t)}stopPolling(e){e&&clearInterval(e)}},Mo=To,{healthCheck:Lo,getState:Ro,setState:Ao,sendMessage:Do,startSpeechRecognition:Vo,textToSpeech:_o,getConversationHistory:zo,clearConversationHistory:Oo,isServerRunning:Io,pollState:Fo,stopPolling:Bo}=To,Uo=()=>{const[e,t]=(0,r.useState)("startup"),[n,a]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!1),[s,l]=(0,r.useState)(""),[u,c]=(0,r.useState)([]),[d,f]=(0,r.useState)(null),h=(0,r.useRef)(null),p=(0,r.useRef)(!0),m=(0,r.useCallback)(async()=>{try{const e=await Mo.isServerRunning();return p.current&&(o(e),e&&f(null)),e}catch(e){return p.current&&(o(!1),f("Failed to connect to Jarvis backend")),!1}},[]),g=(0,r.useCallback)(async e=>{try{await Mo.setState(e),p.current&&(t(e),f(null))}catch(n){console.error("Failed to update state:",n),p.current&&f("Failed to update state: ".concat(n.message))}},[]),v=(0,r.useCallback)(async e=>{if(!i)throw new Error("Not connected to Jarvis backend");try{a(!0),f(null);const n=await Mo.sendMessage(e);return p.current&&(l(n.response),t(n.current_state||"rest"),c(t=>[...t,{id:Date.now(),user:e,jarvis:n.response,timestamp:n.timestamp}])),n}catch(n){throw console.error("Failed to send message:",n),p.current&&f("Failed to send message: ".concat(n.message)),n}finally{p.current&&a(!1)}},[i]),y=(0,r.useCallback)(async()=>{if(!i)throw new Error("Not connected to Jarvis backend");try{a(!0),f(null);const e=await Mo.startSpeechRecognition();return p.current&&e.text?await v(e.text):e}catch(e){throw console.error("Speech recognition failed:",e),p.current&&f("Speech recognition failed: ".concat(e.message)),e}finally{p.current&&a(!1)}},[i,v]),x=(0,r.useCallback)(async e=>{if(!i)throw new Error("Not connected to Jarvis backend");try{f(null);return await Mo.textToSpeech(e)}catch(t){throw console.error("Text-to-speech failed:",t),p.current&&f("Text-to-speech failed: ".concat(t.message)),t}},[i]),b=(0,r.useCallback)(async()=>{try{await Mo.clearConversationHistory(),p.current&&(c([]),f(null))}catch(e){console.error("Failed to clear history:",e),p.current&&f("Failed to clear history: ".concat(e.message))}},[]);(0,r.useEffect)(()=>{let e=!0;p.current=!0;return(async()=>{await m(),e&&(h.current=await Mo.pollState(e=>{p.current&&(t(e.current_state),a(e.is_processing),l(e.last_response))},2e3))})(),()=>{e=!1,p.current=!1,h.current&&Mo.stopPolling(h.current)}},[m]);const w=(0,r.useCallback)(async()=>(f(null),await m()),[m]);return{state:e,isProcessing:n,isServerConnected:i,lastResponse:s,conversationHistory:u,error:d,updateState:g,sendMessage:v,startSpeechRecognition:y,speakText:x,clearHistory:b,retryConnection:w}},Ho=e=>{let{onExitToHome:t,onEnterChat:n}=e;const[a,i]=(0,r.useState)(!1),[o,s]=(0,r.useState)(""),[l,u]=(0,r.useState)(!1),{state:c,isProcessing:d,isServerConnected:f,error:h,updateState:p,sendMessage:m,startSpeechRecognition:g,speakText:v,retryConnection:y}=Uo(),x={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},b={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5}}},w={startup:{media:"startup.gif",title:"SYSTEM INITIALIZATION",description:"J.A.R.V.I.S. ONLINE",color:"#00eeff",loop:!0},rest:{media:"rest.mp4",title:"STANDBY MODE",description:"AWAITING COMMAND",color:"#00eeff",loop:!0},listening:{media:"listening.gif",title:"LISTENING",description:"PROCESSING AUDIO INPUT",color:"#ff00aa",loop:!0},thinking:{media:"thinking.gif",title:"PROCESSING",description:"ANALYZING REQUEST",color:"#ff9900",loop:!0},speaking:{media:"speaking.gif",title:"RESPONDING",description:"OUTPUT GENERATION",color:"#00ff88",loop:!0}};return(0,No.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative",children:[(0,No.jsx)("div",{className:"fixed top-4 right-4 z-20",children:(0,No.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2 rounded-lg ".concat(f?"bg-green-900/50 border border-green-500/30":"bg-red-900/50 border border-red-500/30"),children:[(0,No.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(f?"bg-green-400":"bg-red-400")}),(0,No.jsx)("span",{className:"text-xs ".concat(f?"text-green-300":"text-red-300"),children:f?"Backend Connected":"Backend Disconnected"}),!f&&(0,No.jsx)("button",{onClick:y,className:"text-xs text-red-300 hover:text-red-100 underline ml-2",children:"Retry"})]})}),h&&(0,No.jsx)("div",{className:"fixed top-16 right-4 z-20 max-w-sm",children:(0,No.jsx)("div",{className:"bg-red-900/80 border border-red-500/50 rounded-lg p-3",children:(0,No.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,No.jsx)("svg",{className:"w-5 h-5 text-red-400 flex-shrink-0 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,No.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,No.jsx)("div",{children:(0,No.jsx)("p",{className:"text-sm text-red-300",children:h})})]})})}),(0,No.jsx)("div",{className:"fixed left-0 top-1/2 transform -translate-y-1/2 z-10",children:(0,No.jsx)(jo,{})}),(0,No.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-8",children:[(0,No.jsxs)(Po.div,{className:"relative w-96 h-96 rounded-full overflow-hidden",children:[(0,No.jsx)(C,{mode:"wait",children:(0,No.jsx)(Po.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:1.05},transition:{duration:.6,ease:[.4,0,.2,1],opacity:{duration:.4}},className:"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div",children:w[c].media.endsWith(".mp4")?(0,No.jsx)("video",{src:"/assets/".concat(w[c].media),autoPlay:!0,muted:!0,loop:!0,playsInline:!0,className:"w-full h-full object-cover rounded-full",style:{clipPath:"circle(50% at 50% 50%)"}}):(0,No.jsx)("img",{src:"/assets/".concat(w[c].media),alt:c,className:"w-full h-full object-cover rounded-full ".concat("thinking"===c?"transform translate-x-1 translate-y-1":""),style:{clipPath:"circle(50% at 50% 50%)",imageRendering:"auto",filter:"contrast(1.1) brightness(1.05)",transition:"all 0.3s ease-in-out"}})},c)}),(0,No.jsx)(Po.div,{className:"absolute inset-0 rounded-full border-4 pointer-events-none",style:{borderColor:w[c].color},animate:{opacity:[.3,.8,.3],scale:[1,1.1,1],boxShadow:"0 0 20px ".concat(w[c].color,"60")},transition:{duration:2,repeat:1/0}})]}),(0,No.jsxs)(Po.div,{className:"text-center space-y-4",variants:x,initial:"hidden",animate:"visible",children:[(0,No.jsx)(Po.h1,{className:"text-4xl font-bold tracking-tighter",style:{color:w[c].color},variants:b,children:w[c].title}),(0,No.jsx)(Po.p,{className:"text-xl text-cyan-200 font-light",variants:b,children:w[c].description})]}),(0,No.jsx)(Po.div,{className:"mt-12",variants:b,initial:"hidden",animate:"visible",children:(0,No.jsxs)("label",{className:"container",children:[(0,No.jsx)("input",{type:"checkbox",checked:a,onChange:e=>(async e=>{if(i(e),e)try{await p("listening");const e=await g();e&&e.response&&n(e.response)}catch(h){console.error("Speech recognition failed:",h),i(!1),await p("rest")}else"listening"===c&&await p("rest")})(e.target.checked)}),(0,No.jsxs)("div",{className:"checkmark",children:[(0,No.jsxs)("svg",{className:"icon No",viewBox:"0 0 24 24",fill:"#dc6b6b",style:{color:"#dc6b6b"},children:[(0,No.jsx)("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),(0,No.jsx)("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"}),(0,No.jsx)("line",{x1:"6",y1:"4",x2:"18",y2:"20",stroke:"#dc6b6b",strokeWidth:"2.5",strokeLinecap:"round"})]}),(0,No.jsx)("span",{className:"name No",children:"Mic Off"}),(0,No.jsxs)("svg",{className:"icon Yes",viewBox:"0 0 24 24",fill:"currentColor",children:[(0,No.jsx)("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),(0,No.jsx)("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"}),(0,No.jsxs)("circle",{cx:"12",cy:"8",r:"1",opacity:"0.6",children:[(0,No.jsx)("animate",{attributeName:"r",values:"1;2;1",dur:"1s",repeatCount:"indefinite"}),(0,No.jsx)("animate",{attributeName:"opacity",values:"0.6;0.2;0.6",dur:"1s",repeatCount:"indefinite"})]}),(0,No.jsxs)("circle",{cx:"12",cy:"8",r:"3",opacity:"0.3",children:[(0,No.jsx)("animate",{attributeName:"r",values:"3;4;3",dur:"1.5s",repeatCount:"indefinite"}),(0,No.jsx)("animate",{attributeName:"opacity",values:"0.3;0.1;0.3",dur:"1.5s",repeatCount:"indefinite"})]})]}),(0,No.jsx)("span",{className:"name Yes",children:"Listening"})]})]})}),(0,No.jsx)(Po.div,{className:"flex justify-center",variants:b,initial:"hidden",animate:"visible",children:(0,No.jsxs)("div",{className:"form-control ".concat(l?"focused":""),children:[(0,No.jsx)("input",{type:"text",className:"input",placeholder:"Type something intelligent (Press Enter to chat)",value:o,onChange:e=>s(e.target.value),onFocus:()=>u(!0),onBlur:()=>u(!1),onKeyDown:async e=>{if("Enter"===e.key&&o.trim()){const e=o.trim();s("");try{await m(e);n(e)}catch(h){console.error("Failed to send message:",h),n(e)}}}}),(0,No.jsx)("div",{className:"input-border"})]})})]}),(0,No.jsx)("div",{className:"absolute bottom-6 left-6",children:(0,No.jsxs)(Po.div,{className:"menu",variants:x,initial:"hidden",animate:"visible",children:[Object.keys(w).map(e=>(0,No.jsxs)(Po.button,{className:"link ".concat(c===e?"active":""),style:{backgroundColor:c===e?w[e].color+"20":"transparent",border:c===e?"1px solid ".concat(w[e].color,"70"):"1px solid transparent"},whileHover:{scale:1.05,boxShadow:"0 0 12px ".concat(w[e].color,"50")},whileTap:{scale:.95},onClick:()=>p(e),variants:b,title:e.toUpperCase(),children:[(0,No.jsx)("div",{className:"link-icon",children:(0,No.jsx)("div",{className:"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold",style:{backgroundColor:c===e?w[e].color:"rgba(0, 238, 255, 0.6)",color:"white",fontSize:"10px"},children:e.charAt(0).toUpperCase()})}),(0,No.jsx)("span",{className:"link-title",children:e.charAt(0).toUpperCase()+e.slice(1)})]},e)),t&&(0,No.jsxs)(Po.button,{className:"link",style:{backgroundColor:"transparent",border:"1px solid rgba(255, 255, 255, 0.3)"},whileHover:{scale:1.05,boxShadow:"0 0 12px rgba(255, 255, 255, 0.5)"},whileTap:{scale:.95},onClick:t,variants:b,title:"HOME",children:[(0,No.jsx)("div",{className:"link-icon",children:(0,No.jsx)("div",{className:"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold",style:{backgroundColor:"rgba(255, 255, 255, 0.6)",color:"black",fontSize:"10px"},children:"H"})}),(0,No.jsx)("span",{className:"link-title",children:"Home"})]})]})})]})},Wo=e=>{let{children:t,onClick:n,variant:r="primary",size:a="medium",disabled:i=!1,loading:o=!1,className:s=""}=e;return(0,No.jsx)("button",{onClick:n,disabled:i||o,className:"\n        ".concat({primary:"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",secondary:"bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800",success:"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700",danger:"bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700"}[r],"\n        ").concat({small:"px-4 py-2 text-sm",medium:"px-6 py-3 text-base",large:"px-8 py-4 text-lg"}[a],"\n        text-white font-semibold rounded-lg\n        transform transition-all duration-200\n        hover:scale-105 hover:shadow-lg\n        active:scale-95\n        disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\n        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\n        ").concat(s,"\n      "),children:o?(0,No.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,No.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,No.jsx)("span",{children:"Loading..."})]}):t})},$o=e=>{let{onHomeClick:t,onSettingsClick:n,onHelpClick:r,showNavigation:a=!0}=e;return(0,No.jsxs)("footer",{className:"fixed bottom-0 left-0 right-0 p-4 bg-black/20 backdrop-blur-sm",children:[a&&(0,No.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,No.jsx)(Wo,{variant:"secondary",size:"small",onClick:t,children:"\ud83c\udfe0 Home"}),(0,No.jsx)(Wo,{variant:"secondary",size:"small",onClick:n,children:"\u2699\ufe0f Settings"}),(0,No.jsx)(Wo,{variant:"secondary",size:"small",onClick:r,children:"\u2753 Help"})]}),(0,No.jsx)("div",{className:"text-center mt-2",children:(0,No.jsx)("p",{className:"text-xs text-gray-400",children:"\xa9 2024 JARVIS UI - Powered by AI"})})]})},Yo=e=>{let{onMenuToggle:t}=e;const[n,a]=(0,r.useState)(!1),[i,o]=(0,r.useState)({x:0,y:0}),[s,l]=(0,r.useState)(!1),u=(0,r.useRef)(null);(0,r.useEffect)(()=>{const e=e=>{if(u.current){const t=u.current.getBoundingClientRect(),n=t.left+t.width/2,r=t.top+t.height/2,a=6,i=Math.min(Math.sqrt(Math.pow(e.clientX-n,2)+Math.pow(e.clientY-r,2))/100,a),s=Math.atan2(e.clientY-r,e.clientX-n),l=Math.cos(s)*Math.min(i,a),c=Math.sin(s)*Math.min(i,a);o({x:l,y:c})}};return window.addEventListener("mousemove",e),()=>window.removeEventListener("mousemove",e)},[]);return(0,No.jsxs)("div",{className:"cute-menu-wrapper",children:[(0,No.jsx)("div",{ref:u,className:"cute-menu-icon-container",onClick:()=>{const e=!n;a(e),t&&t(e)},onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),children:(0,No.jsx)("div",{className:"cute-icon-background",children:(0,No.jsxs)("div",{className:"cute-eyes",children:[(0,No.jsx)("div",{className:"cute-eye left-eye ".concat(s?"hidden":""),style:{transform:"translate(".concat(i.x,"px, ").concat(i.y,"px)")}}),(0,No.jsx)("div",{className:"cute-eye right-eye ".concat(s?"hidden":""),style:{transform:"translate(".concat(i.x,"px, ").concat(i.y,"px)")}}),(0,No.jsxs)("div",{className:"cute-happy-eyes ".concat(s?"visible":""),children:[(0,No.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",className:"happy-eye",children:(0,No.jsx)("path",{fill:"white",d:"M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z"})}),(0,No.jsx)("svg",{fill:"none",viewBox:"0 0 24 24",className:"happy-eye",children:(0,No.jsx)("path",{fill:"white",d:"M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z"})})]})]})})}),(0,No.jsx)("div",{className:"click-me-label",children:"Click Me!"}),n&&(0,No.jsxs)("div",{className:"cute-dropdown-menu",children:[(0,No.jsxs)("div",{className:"menu-option",children:[(0,No.jsx)("div",{className:"menu-icon notifications-icon",children:(0,No.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"currentColor",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"})})}),(0,No.jsx)("span",{children:"Notifications"})]}),(0,No.jsxs)("div",{className:"menu-option",children:[(0,No.jsx)("div",{className:"menu-icon settings-icon",children:(0,No.jsxs)("svg",{stroke:"currentColor",strokeWidth:"1.5",viewBox:"0 0 24 24",fill:"none",children:[(0,No.jsx)("path",{d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z",strokeLinejoin:"round",strokeLinecap:"round"}),(0,No.jsx)("path",{d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z",strokeLinejoin:"round",strokeLinecap:"round"})]})}),(0,No.jsx)("span",{children:"Settings"})]}),(0,No.jsxs)("div",{className:"menu-option",children:[(0,No.jsx)("div",{className:"menu-icon chat-icon",children:(0,No.jsx)("svg",{stroke:"currentColor",strokeWidth:"1.5",viewBox:"0 0 24 24",fill:"none",children:(0,No.jsx)("path",{d:"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z",strokeLinejoin:"round",strokeLinecap:"round"})})}),(0,No.jsx)("span",{children:"Chat"})]})]})]})},Qo=e=>{let{onEnterJarvis:t}=e;const[n,a]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!1),[s,l]=(0,r.useState)(!1),u=(0,r.useRef)(null),c=(0,r.useRef)(null),d=()=>{console.log("Settings clicked")},f=()=>{console.log("Help clicked")};return(0,r.useEffect)(()=>{const e=u.current;e&&(e.style.mixBlendMode="screen",e.style.filter="brightness(0.8) contrast(1.2)")},[]),(0,No.jsxs)("div",{className:"homescreen-container",children:[(0,No.jsx)(Yo,{onMenuToggle:e=>{l(e)}}),(0,No.jsx)("div",{className:"fixed left-0 top-1/2 transform -translate-y-1/2 z-10",children:(0,No.jsx)(jo,{})}),(0,No.jsxs)("div",{className:"wallpaper-container",children:[(0,No.jsx)("img",{ref:u,src:"/assets/HomeSettingsscreen.gif",alt:"Animated Wallpaper",className:"wallpaper-background",onError:e=>{e.target.style.display="none",e.target.parentElement.style.background="linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)"}}),(0,No.jsx)("div",{className:"wallpaper-overlay"})]}),i&&(0,No.jsx)("div",{className:"exit-animation-overlay",children:(0,No.jsx)("img",{ref:c,src:"/assets/screenexit.gif",alt:"Screen Exit Animation",className:"exit-animation",onError:e=>{e.target.style.display="none",e.target.parentElement.innerHTML='<div class="fallback-exit-animation"></div>'}})}),(0,No.jsxs)("div",{className:"homescreen-content ".concat(n?"entering":""),children:[(0,No.jsxs)("div",{className:"title-section",children:[(0,No.jsx)("div",{className:"marvel-logo",children:"MARVEL"}),(0,No.jsx)("h1",{className:"main-title",children:"JARVIS"}),(0,No.jsx)("div",{className:"subtitle",children:"PRESS \u26a1 TO START"})]}),(0,No.jsxs)("div",{className:"jarvis-icon-container",children:[(0,No.jsxs)("div",{className:"jarvis-icon",children:[(0,No.jsx)("div",{className:"icon-glow"}),(0,No.jsx)("div",{className:"icon-core",children:(0,No.jsxs)("svg",{viewBox:"0 0 100 100",className:"jarvis-svg",children:[(0,No.jsx)("circle",{cx:"50",cy:"50",r:"30",className:"outer-ring"}),(0,No.jsx)("circle",{cx:"50",cy:"50",r:"20",className:"middle-ring"}),(0,No.jsx)("circle",{cx:"50",cy:"50",r:"10",className:"inner-core"}),(0,No.jsx)("path",{d:"M30 50 L70 50 M50 30 L50 70",className:"cross-lines"})]})})]}),(0,No.jsx)(Wo,{onClick:()=>{a(!0),o(!0),setTimeout(()=>{t()},2e3)},variant:"primary",size:"large",loading:n,className:"mt-4",children:n?"ACTIVATING...":"ACTIVATE JARVIS"})]}),(0,No.jsxs)("div",{className:"bottom-menu",children:[(0,No.jsx)(Wo,{onClick:d,variant:"secondary",size:"small",className:"menu-button",children:"\u2699\ufe0f Settings"}),(0,No.jsx)(Wo,{onClick:()=>{console.log("Voice Commands clicked")},variant:"secondary",size:"small",className:"menu-button",children:"\ud83c\udfa4 Voice"}),(0,No.jsx)(Wo,{onClick:f,variant:"secondary",size:"small",className:"menu-button",children:"\u2753 Help"}),(0,No.jsx)(Wo,{onClick:()=>{console.log("About clicked")},variant:"secondary",size:"small",className:"menu-button",children:"\u2139\ufe0f About"})]})]}),(0,No.jsx)($o,{onHomeClick:()=>console.log("Home clicked"),onSettingsClick:d,onHelpClick:f,showNavigation:!s})]})},qo=e=>{var t;let{onExpand:n,isProcessing:a=!1}=e;const[i,o]=(0,r.useState)("rest"),[s,l]=(0,r.useState)(!0),[u,c]=(0,r.useState)({x:0,y:0}),[d,f]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),m={startup:{media:"startup.gif",title:"SYSTEM INITIALIZATION",description:"J.A.R.V.I.S. ONLINE",color:"#00eeff",loop:!0,scale:1,translateX:"-50%",translateY:"-50%"},rest:{media:"rest.mp4",title:"STANDBY MODE",description:"AWAITING COMMAND",color:"#00eeff",loop:!0,scale:1.1,translateX:"-45%",translateY:"-45%"},listening:{media:"listening.gif",title:"LISTENING",description:"PROCESSING AUDIO INPUT",color:"#ff00aa",loop:!0,scale:1,translateX:"-50%",translateY:"-50%"},thinking:{media:"thinking.gif",title:"PROCESSING",description:"ANALYZING REQUEST",color:"#ff9900",loop:!0,scale:1.1,translateX:"-45%",translateY:"-48%"},speaking:{media:"speaking.gif",title:"RESPONDING",description:"OUTPUT GENERATION",color:"#00ff88",loop:!0,scale:1,translateX:"-50%",translateY:"-50%"}},[g,v]=(0,r.useState)(0),[y,x]=(0,r.useState)(null);(0,r.useEffect)(()=>{if(a){o("thinking");const e=setTimeout(()=>{o("speaking"),setTimeout(()=>o("rest"),2e3)},1500);return()=>clearTimeout(e)}o("rest")},[a]),(0,r.useEffect)(()=>{const e=setInterval(()=>{a||o(e=>{switch(e){case"rest":return"listening";case"listening":return"thinking";case"thinking":return"speaking";default:return"rest"}})},3e3);return()=>clearInterval(e)},[a]);const b=()=>{var e;return(null===(e=m[i])||void 0===e?void 0:e.color)||"#00eeff"};return s?(0,No.jsx)(No.Fragment,{children:(0,No.jsxs)(Po.div,{drag:!0,dragMomentum:!1,dragElastic:.1,dragConstraints:{left:0,right:window.innerWidth-280,top:0,bottom:window.innerHeight-360},onDragStart:()=>{f(!0)},onDrag:(e,t)=>{},onDragEnd:(e,t)=>{f(!1);const n=((e,t)=>{const n=window.innerWidth,r=window.innerHeight,a=280,i=360,o=12,s=[{x:n-a-o,y:80,corner:"top-right",priority:1,magnetRadius:150},{x:o,y:80,corner:"top-left",priority:2,magnetRadius:150},{x:n-a-o,y:(r-i)/2,corner:"middle-right",priority:3,magnetRadius:120},{x:o,y:(r-i)/2,corner:"middle-left",priority:4,magnetRadius:120},{x:n-a-o,y:r-i-100,corner:"bottom-right",priority:5,magnetRadius:130},{x:o,y:r-i-100,corner:"bottom-left",priority:6,magnetRadius:130},{x:n-a-12,y:12,corner:"extreme-top-right",priority:7,magnetRadius:100},{x:n-a-o,y:r-i-12,corner:"extreme-bottom-right",priority:8,magnetRadius:100}];let l=s[0],u=1/0;return s.forEach(n=>{const r=Math.sqrt(Math.pow(e-n.x,2)+Math.pow(t-n.y,2)),a=r*(r<=n.magnetRadius?.5:1)+.1*n.priority;a<u&&(u=a,l=n)}),{x:l.x,y:l.y,corner:l.corner}})(t.point.x-140,t.point.y-180);setTimeout(()=>{c(n)},50)},onClick:()=>{v(e=>e+1),y&&clearTimeout(y);const e=setTimeout(()=>{g+1>=2&&(p(!0),setTimeout(()=>{p(!1)},3e3)),v(0)},300);x(e)},initial:{scale:0,opacity:0,x:window.innerWidth-196,y:76},animate:{scale:1,opacity:1,x:u.x||window.innerWidth-296,y:u.y||76,transition:{type:"spring",damping:25,stiffness:200,duration:.6}},exit:{scale:0,opacity:0},className:"fixed z-30 select-none ".concat(d?"cursor-grabbing":"cursor-grab"),style:{width:"280px",height:"360px"},whileHover:{scale:1.02},whileDrag:{scale:1.05,zIndex:50},children:[(0,No.jsx)(C,{children:h&&(0,No.jsxs)(No.Fragment,{children:[(0,No.jsx)(Po.button,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{type:"spring",damping:15,stiffness:300},onClick:()=>l(!1),className:"absolute -top-2 -right-2 w-10 h-10 rounded-full bg-red-500/30 hover:bg-red-500/50 border-2 border-red-500/70 flex items-center justify-center text-red-300 hover:text-red-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-red-500/30",onMouseDown:e=>e.stopPropagation(),style:{boxShadow:"0 0 20px rgba(239, 68, 68, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)"},children:"\xd7"}),(0,No.jsx)(Po.button,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{type:"spring",damping:15,stiffness:300,delay:.1},onClick:n,className:"absolute -top-2 -left-2 w-10 h-10 rounded-full bg-cyan-500/30 hover:bg-cyan-500/50 border-2 border-cyan-500/70 flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-cyan-500/30",title:"Expand JARVIS",onMouseDown:e=>e.stopPropagation(),style:{boxShadow:"0 0 20px rgba(6, 182, 212, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)"},children:"\u2197"})]})}),(0,No.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,No.jsxs)(Po.div,{className:"relative w-56 h-56 rounded-full overflow-hidden mb-3",children:[(0,No.jsx)(C,{mode:"wait",children:(0,No.jsx)(Po.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:1.05},transition:{duration:.6,ease:[.4,0,.2,1],opacity:{duration:.4}},className:"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden",children:m[i].media.endsWith(".mp4")?(0,No.jsx)("video",{src:"/assets/".concat(m[i].media),autoPlay:!0,muted:!0,loop:!0,playsInline:!0,className:"rounded-full",style:{clipPath:"circle(50% at 50% 50%)",width:"120%",height:"120%",objectFit:"cover",position:"absolute",top:"50%",left:"50%",transform:"scale(".concat(m[i].scale,") translate(").concat(m[i].translateX,", ").concat(m[i].translateY,")"),pointerEvents:"none",userSelect:"none",transition:"all 0.3s ease-in-out"},onError:e=>{e.target.style.display="none"}}):(0,No.jsx)("img",{src:"/assets/".concat(m[i].media),alt:i,draggable:!1,className:"rounded-full",style:{clipPath:"circle(50% at 50% 50%)",width:"120%",height:"120%",objectFit:"cover",imageRendering:"auto",filter:"contrast(1.1) brightness(1.05)",position:"absolute",top:"50%",left:"50%",transform:"scale(".concat(m[i].scale,") translate(").concat(m[i].translateX,", ").concat(m[i].translateY,")"),transition:"all 0.3s ease-in-out",pointerEvents:"none",userSelect:"none"},onError:e=>{e.target.style.display="none",e.target.parentElement.innerHTML='\n                      <div class="w-full h-full rounded-full border-2 flex items-center justify-center"\n                           style="border-color: '.concat(b(),"; box-shadow: 0 0 20px ").concat(b(),'40;">\n                        <svg viewBox="0 0 100 100" class="w-16 h-16">\n                          <circle cx="50" cy="50" r="30" fill="none" stroke="').concat(b(),'" stroke-width="2"/>\n                          <circle cx="50" cy="50" r="20" fill="none" stroke="').concat(b(),'" stroke-width="1.5" opacity="0.7"/>\n                          <circle cx="50" cy="50" r="8" fill="').concat(b(),'"/>\n                          <path d="M30 50 L70 50 M50 30 L50 70" stroke="').concat(b(),'" stroke-width="1.5" opacity="0.8"/>\n                        </svg>\n                      </div>\n                    ')}})},i)}),(0,No.jsx)(Po.div,{className:"absolute inset-0 rounded-full pointer-events-none border-2",style:{borderColor:b(),animation:"blinkingCircle 2s ease-in-out infinite",boxShadow:"0 0 20px ".concat(b(),"60")}})]}),(0,No.jsxs)("div",{className:"text-center",children:[(0,No.jsx)("div",{className:"text-base font-bold transition-colors duration-300 mb-1",style:{color:b()},children:(()=>{var e;return(null===(e=m[i])||void 0===e?void 0:e.title)||"STANDBY MODE"})()}),(0,No.jsx)("div",{className:"text-sm text-gray-300 font-medium tracking-wider",children:"JARVIS"}),(0,No.jsx)("div",{className:"text-xs text-gray-400",children:null===(t=m[i])||void 0===t?void 0:t.description})]})]})]})}):null},Xo=e=>{let{onExitToHome:t,initialMessage:n=""}=e;const[a,i]=(0,r.useState)([]),[o,s]=(0,r.useState)(""),[l,u]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1),[f,h]=(0,r.useState)(!1),[p,m]=(0,r.useState)(!1),g=(0,r.useRef)(null),v=(0,r.useRef)(null),y=(0,r.useRef)(null),{state:x,isProcessing:b,isServerConnected:w,error:k,sendMessage:S,speakText:E,conversationHistory:P,clearHistory:N}=Uo();(0,r.useEffect)(()=>{if(n.trim()){const e={id:Date.now(),text:n,sender:"user",timestamp:new Date};i([e]);const t=async()=>{try{d(!0);const e=await S(n);i(t=>[...t,{id:Date.now()+1,text:e.response,sender:"jarvis",timestamp:new Date(e.timestamp)}])}catch(k){console.error("Failed to process initial message:",k),i(e=>[...e,{id:Date.now()+1,text:"I'm sorry, I'm having trouble connecting to my backend systems. Please try again.",sender:"jarvis",timestamp:new Date}])}finally{d(!1)}};t()}},[n,S]),(0,r.useEffect)(()=>{var e;null===(e=g.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[a,c]),(0,r.useEffect)(()=>{var e;null===(e=v.current)||void 0===e||e.focus()},[]);const j={hidden:{opacity:0,y:10,scale:.95},visible:{opacity:1,y:0,scale:1,transition:{duration:.3}}};return(0,No.jsxs)("div",{className:"flex flex-col h-screen bg-gradient-to-br from-gray-900 to-black text-white relative",children:[!w&&(0,No.jsx)("div",{className:"fixed top-4 left-1/2 transform -translate-x-1/2 z-20",children:(0,No.jsx)("div",{className:"bg-red-900/80 border border-red-500/50 rounded-lg px-4 py-2",children:(0,No.jsx)("span",{className:"text-sm text-red-300",children:"Backend Disconnected - Chat functionality limited"})})}),(0,No.jsx)("div",{className:"fixed left-0 top-1/2 transform -translate-y-1/2 z-10",children:(0,No.jsx)(jo,{})}),(0,No.jsx)(qo,{onExpand:t,isProcessing:b}),(0,No.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 border-b border-cyan-500/20",children:[(0,No.jsx)("div",{className:"flex items-center space-x-2",children:(0,No.jsxs)("div",{className:"relative",children:[(0,No.jsxs)("button",{onClick:()=>h(!f),className:"flex items-center space-x-2 text-white hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors",children:[(0,No.jsx)("span",{className:"font-semibold",children:"JARVIS"}),(0,No.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),f&&(0,No.jsx)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-filter backdrop-blur-12 rounded-lg shadow-lg border border-cyan-500/30 z-50",children:(0,No.jsxs)("div",{className:"py-1",children:[(0,No.jsx)("button",{className:"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors",children:"New Chat"}),(0,No.jsx)("button",{className:"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors",children:"Chat History"}),(0,No.jsx)("button",{className:"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors",children:"Settings"})]})})]})}),(0,No.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,No.jsxs)("button",{className:"flex items-center space-x-2 bg-gradient-to-r from-cyan-600/20 to-blue-600/20 hover:from-cyan-500/30 hover:to-blue-500/30 border border-cyan-500/30 px-3 py-2 rounded-lg transition-all duration-300 text-cyan-100 hover:text-cyan-400",children:[(0,No.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})}),(0,No.jsx)("span",{className:"text-sm",children:"Share"})]}),(0,No.jsx)("button",{onClick:t,className:"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400",title:"Return to JARVIS",children:(0,No.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})})}),(0,No.jsx)("button",{className:"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400",children:(0,No.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})})})]})]}),(0,No.jsxs)("div",{className:"flex-1 overflow-y-auto relative",onScroll:e=>{const{scrollTop:t,scrollHeight:n,clientHeight:r}=e.target;m(!(n-t-r<100)&&a.length>0)},ref:y,children:[(0,No.jsxs)("div",{className:"max-w-3xl mx-auto",children:[0===a.length?(0,No.jsx)("div",{className:"flex flex-col items-center justify-center h-full text-center py-20",children:(0,No.jsxs)("div",{className:"mb-8",children:[(0,No.jsx)("div",{className:"w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center mx-auto mb-4",children:(0,No.jsx)("div",{className:"w-8 h-8 rounded-full bg-cyan-400"})}),(0,No.jsx)("h2",{className:"text-2xl font-semibold text-gray-300 mb-2",children:"How can I help you today?"}),(0,No.jsx)("p",{className:"text-gray-500",children:"I'm JARVIS, your AI assistant. Ask me anything!"})]})}):(0,No.jsxs)("div",{className:"py-8",children:[(0,No.jsx)(C,{children:a.map(e=>(0,No.jsx)(Po.div,{variants:j,initial:"hidden",animate:"visible",className:"group mb-8 ".concat("user"===e.sender?"ml-auto":""),children:(0,No.jsxs)("div",{className:"flex items-start space-x-4 px-4",children:["jarvis"===e.sender&&(0,No.jsx)("div",{className:"flex-shrink-0",children:(0,No.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center",children:(0,No.jsx)("div",{className:"w-4 h-4 rounded-full bg-cyan-400"})})}),(0,No.jsxs)("div",{className:"flex-1 ".concat("user"===e.sender?"max-w-2xl ml-auto":"max-w-2xl"),children:["user"===e.sender&&(0,No.jsx)("div",{className:"text-right mb-2",children:(0,No.jsx)("span",{className:"text-sm text-gray-400",children:"You"})}),(0,No.jsx)("div",{className:"".concat("user"===e.sender?"bg-gray-700 text-white rounded-2xl px-4 py-3 ml-auto inline-block max-w-fit":"text-gray-100"),children:(0,No.jsx)("p",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:e.text})}),(0,No.jsx)("div",{className:"mt-2 ".concat("user"===e.sender?"text-right":"text-left"),children:(0,No.jsx)("span",{className:"text-xs text-gray-500",children:e.timestamp.toLocaleTimeString()})})]}),"user"===e.sender&&(0,No.jsx)("div",{className:"flex-shrink-0",children:(0,No.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center",children:(0,No.jsx)("span",{className:"text-sm font-medium",children:"U"})})})]})},e.id))}),c&&(0,No.jsx)(Po.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mb-8 px-4",children:(0,No.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,No.jsx)("div",{className:"flex-shrink-0",children:(0,No.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center",children:(0,No.jsx)("div",{className:"w-4 h-4 rounded-full bg-cyan-400"})})}),(0,No.jsx)("div",{className:"flex-1 max-w-2xl",children:(0,No.jsxs)("div",{className:"flex space-x-1 py-2",children:[(0,No.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,No.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,No.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})})]}),(0,No.jsx)("div",{ref:g})]}),p&&(0,No.jsx)("div",{className:"absolute bottom-4 right-4",children:(0,No.jsx)("button",{onClick:()=>{var e;null===(e=g.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},className:"bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow-lg transition-colors",children:(0,No.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,No.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}),(0,No.jsx)("div",{className:"flex justify-center p-6",children:(0,No.jsxs)("div",{className:"form-control ".concat(l?"focused":""),children:[(0,No.jsx)("input",{ref:v,type:"text",className:"input",placeholder:"Type something intelligent (Press Enter to chat)",value:o,onChange:e=>s(e.target.value),onFocus:()=>u(!0),onBlur:()=>u(!1),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),(async()=>{if(!o.trim()||!w)return;const e=o.trim(),t={id:Date.now(),text:e,sender:"user",timestamp:new Date};i(e=>[...e,t]),s("");try{d(!0);const t=await S(e);i(e=>[...e,{id:Date.now()+1,text:t.response,sender:"jarvis",timestamp:new Date(t.timestamp)}])}catch(k){console.error("Failed to send message:",k),i(e=>[...e,{id:Date.now()+1,text:"I'm sorry, I encountered an error processing your request. Please try again.",sender:"jarvis",timestamp:new Date}])}finally{d(!1)}})())}}),(0,No.jsx)("div",{className:"input-border"})]})})]})};const Go=function(){const[e,t]=(0,r.useState)("home"),[n,a]=(0,r.useState)(""),i={initial:{opacity:0},animate:{opacity:1,transition:{duration:.5}},exit:{opacity:0,transition:{duration:.3}}};return(0,No.jsx)("div",{className:"App bg-gradient-to-br from-gray-900 to-black min-h-screen text-white relative overflow-hidden",children:(0,No.jsxs)(C,{mode:"wait",children:["home"===e&&(0,No.jsx)(Po.div,{variants:i,initial:"initial",animate:"animate",exit:"exit",className:"absolute inset-0",children:(0,No.jsx)(Qo,{onEnterJarvis:()=>{t("jarvis")}})},"home"),"jarvis"===e&&(0,No.jsx)(Po.div,{variants:i,initial:"initial",animate:"animate",exit:"exit",className:"absolute inset-0",children:(0,No.jsx)(Ho,{onExitToHome:()=>{t("home")},onEnterChat:function(){a(arguments.length>0&&void 0!==arguments[0]?arguments[0]:""),t("chat")}})},"jarvis"),"chat"===e&&(0,No.jsx)(Po.div,{variants:{initial:{y:"100%",opacity:0},animate:{y:0,opacity:1,transition:{type:"spring",damping:25,stiffness:120,duration:.8}},exit:{y:"-100%",opacity:0,transition:{type:"spring",damping:25,stiffness:120,duration:.6}}},initial:"initial",animate:"animate",exit:"exit",className:"absolute inset-0",children:(0,No.jsx)(Xo,{onExitToHome:()=>{t("jarvis"),a("")},initialMessage:n})},"chat")]})})};a.createRoot(document.getElementById("root")).render((0,No.jsx)(r.StrictMode,{children:(0,No.jsx)(Go,{})}))})();
//# sourceMappingURL=main.a4989988.js.map