#!/usr/bin/env python3
"""
API Server for Jarvis AI - Flask-based REST API
Provides HTTP endpoints for React frontend to communicate with Python backend
"""

import os
import sys
import json
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
import threading
import time
from datetime import datetime

# Add Backend directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import Jarvis backend modules
from Main import process_query, gui_mode
from SpeechToText import SpeechRecognition
from TextToSpeech import TextToSpeech
from Chatbot import ChatBot
from RealtimeSearchEngine import RealtimeSearchEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for React frontend

# Global state management
class JarvisState:
    def __init__(self):
        self.current_state = 'rest'  # startup, rest, listening, thinking, speaking
        self.is_processing = False
        self.last_response = ""
        self.conversation_history = []
    
    def set_state(self, state):
        self.current_state = state
        logger.info(f"State changed to: {state}")
    
    def add_to_history(self, user_message, jarvis_response):
        self.conversation_history.append({
            'timestamp': datetime.now().isoformat(),
            'user': user_message,
            'jarvis': jarvis_response
        })

# Global state instance
jarvis_state = JarvisState()

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'current_state': jarvis_state.current_state
    })

@app.route('/api/state', methods=['GET'])
def get_state():
    """Get current Jarvis state"""
    return jsonify({
        'current_state': jarvis_state.current_state,
        'is_processing': jarvis_state.is_processing,
        'last_response': jarvis_state.last_response
    })

@app.route('/api/state', methods=['POST'])
def set_state():
    """Set Jarvis state"""
    try:
        data = request.get_json()
        new_state = data.get('state')
        
        if new_state in ['startup', 'rest', 'listening', 'thinking', 'speaking']:
            jarvis_state.set_state(new_state)
            return jsonify({'success': True, 'current_state': new_state})
        else:
            return jsonify({'error': 'Invalid state'}), 400
            
    except Exception as e:
        logger.error(f"Error setting state: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    """Process chat message and return response"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Set processing state
        jarvis_state.is_processing = True
        jarvis_state.set_state('thinking')
        
        logger.info(f"Processing message: {user_message}")
        
        # Process the query using backend
        response = process_query(user_message)
        
        # Update state
        jarvis_state.last_response = response
        jarvis_state.add_to_history(user_message, response)
        jarvis_state.is_processing = False
        jarvis_state.set_state('rest')
        
        logger.info(f"Response generated: {response[:100]}...")
        
        return jsonify({
            'response': response,
            'timestamp': datetime.now().isoformat(),
            'current_state': jarvis_state.current_state
        })
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        jarvis_state.is_processing = False
        jarvis_state.set_state('rest')
        return jsonify({'error': f'Failed to process message: {str(e)}'}), 500

@app.route('/api/speech-to-text', methods=['POST'])
def speech_to_text():
    """Start speech recognition"""
    try:
        jarvis_state.set_state('listening')
        jarvis_state.is_processing = True
        
        # Start speech recognition
        logger.info("Starting speech recognition...")
        recognized_text = SpeechRecognition()
        
        jarvis_state.is_processing = False
        jarvis_state.set_state('rest')
        
        return jsonify({
            'text': recognized_text,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in speech-to-text: {str(e)}")
        jarvis_state.is_processing = False
        jarvis_state.set_state('rest')
        return jsonify({'error': f'Speech recognition failed: {str(e)}'}), 500

@app.route('/api/text-to-speech', methods=['POST'])
def text_to_speech():
    """Convert text to speech"""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()
        
        if not text:
            return jsonify({'error': 'Text is required'}), 400
        
        jarvis_state.set_state('speaking')
        
        # Convert text to speech
        logger.info(f"Converting to speech: {text[:50]}...")
        TextToSpeech(text)
        
        jarvis_state.set_state('rest')
        
        return jsonify({
            'success': True,
            'message': 'Text converted to speech',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error in text-to-speech: {str(e)}")
        jarvis_state.set_state('rest')
        return jsonify({'error': f'Text-to-speech failed: {str(e)}'}), 500

@app.route('/api/conversation-history', methods=['GET'])
def get_conversation_history():
    """Get conversation history"""
    return jsonify({
        'history': jarvis_state.conversation_history,
        'count': len(jarvis_state.conversation_history)
    })

@app.route('/api/conversation-history', methods=['DELETE'])
def clear_conversation_history():
    """Clear conversation history"""
    jarvis_state.conversation_history = []
    return jsonify({'success': True, 'message': 'Conversation history cleared'})

if __name__ == '__main__':
    logger.info("Starting Jarvis API Server...")
    logger.info("Server will be available at http://localhost:5000")
    
    # Run Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
