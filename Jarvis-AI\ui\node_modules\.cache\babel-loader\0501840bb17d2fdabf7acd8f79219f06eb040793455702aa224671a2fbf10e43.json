{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * useJarvis Hook\n * Custom React hook for managing Jarvis state and API interactions\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport jarvisApi from '../services/jarvisApi';\nexport const useJarvis = () => {\n  _s();\n  // State management\n  const [state, setState] = useState('startup');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isServerConnected, setIsServerConnected] = useState(false);\n  const [lastResponse, setLastResponse] = useState('');\n  const [conversationHistory, setConversationHistory] = useState([]);\n  const [error, setError] = useState(null);\n\n  // Refs for cleanup\n  const pollingIntervalRef = useRef(null);\n  const mountedRef = useRef(true);\n\n  /**\n   * Check server connection\n   */\n  const checkServerConnection = useCallback(async () => {\n    try {\n      const isRunning = await jarvisApi.isServerRunning();\n      if (mountedRef.current) {\n        setIsServerConnected(isRunning);\n        if (isRunning) {\n          setError(null);\n        }\n      }\n      return isRunning;\n    } catch (err) {\n      if (mountedRef.current) {\n        setIsServerConnected(false);\n        setError('Failed to connect to Jarvis backend');\n      }\n      return false;\n    }\n  }, []);\n\n  /**\n   * Update Jarvis state\n   */\n  const updateState = useCallback(async newState => {\n    try {\n      await jarvisApi.setState(newState);\n      if (mountedRef.current) {\n        setState(newState);\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Failed to update state:', err);\n      if (mountedRef.current) {\n        setError(`Failed to update state: ${err.message}`);\n      }\n    }\n  }, []);\n\n  /**\n   * Send message to Jarvis\n   */\n  const sendMessage = useCallback(async message => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n    try {\n      setIsProcessing(true);\n      setError(null);\n      const response = await jarvisApi.sendMessage(message);\n      if (mountedRef.current) {\n        setLastResponse(response.response);\n        setState(response.current_state || 'rest');\n\n        // Update conversation history\n        setConversationHistory(prev => [...prev, {\n          id: Date.now(),\n          user: message,\n          jarvis: response.response,\n          timestamp: response.timestamp\n        }]);\n\n        // Automatically speak the response\n        if (response.response) {\n          try {\n            await jarvisApi.textToSpeech(response.response);\n          } catch (speechError) {\n            console.warn('Text-to-speech failed:', speechError);\n            // Don't throw error for TTS failure, just log it\n          }\n        }\n      }\n      return response;\n    } catch (err) {\n      console.error('Failed to send message:', err);\n      if (mountedRef.current) {\n        setError(`Failed to send message: ${err.message}`);\n      }\n      throw err;\n    } finally {\n      if (mountedRef.current) {\n        setIsProcessing(false);\n      }\n    }\n  }, [isServerConnected]);\n\n  /**\n   * Start speech recognition\n   */\n  const startSpeechRecognition = useCallback(async () => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n    try {\n      setIsProcessing(true);\n      setError(null);\n      const response = await jarvisApi.startSpeechRecognition();\n      if (mountedRef.current && response.text) {\n        // Automatically send the recognized text as a message\n        // The sendMessage function will automatically speak the response\n        return await sendMessage(response.text);\n      }\n      return response;\n    } catch (err) {\n      console.error('Speech recognition failed:', err);\n      if (mountedRef.current) {\n        setError(`Speech recognition failed: ${err.message}`);\n      }\n      throw err;\n    } finally {\n      if (mountedRef.current) {\n        setIsProcessing(false);\n      }\n    }\n  }, [isServerConnected, sendMessage]);\n\n  /**\n   * Convert text to speech\n   */\n  const speakText = useCallback(async text => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n    try {\n      setError(null);\n      const response = await jarvisApi.textToSpeech(text);\n      return response;\n    } catch (err) {\n      console.error('Text-to-speech failed:', err);\n      if (mountedRef.current) {\n        setError(`Text-to-speech failed: ${err.message}`);\n      }\n      throw err;\n    }\n  }, [isServerConnected]);\n\n  /**\n   * Clear conversation history\n   */\n  const clearHistory = useCallback(async () => {\n    try {\n      await jarvisApi.clearConversationHistory();\n      if (mountedRef.current) {\n        setConversationHistory([]);\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Failed to clear history:', err);\n      if (mountedRef.current) {\n        setError(`Failed to clear history: ${err.message}`);\n      }\n    }\n  }, []);\n\n  /**\n   * Initialize connection and start polling\n   */\n  useEffect(() => {\n    let mounted = true;\n    mountedRef.current = true;\n    const initialize = async () => {\n      // Check initial connection\n      await checkServerConnection();\n\n      // Start polling for state updates\n      if (mounted) {\n        pollingIntervalRef.current = await jarvisApi.pollState(stateData => {\n          if (mountedRef.current) {\n            setState(stateData.current_state);\n            setIsProcessing(stateData.is_processing);\n            setLastResponse(stateData.last_response);\n          }\n        }, 2000 // Poll every 2 seconds\n        );\n      }\n    };\n    initialize();\n\n    // Cleanup function\n    return () => {\n      mounted = false;\n      mountedRef.current = false;\n      if (pollingIntervalRef.current) {\n        jarvisApi.stopPolling(pollingIntervalRef.current);\n      }\n    };\n  }, [checkServerConnection]);\n\n  /**\n   * Retry connection\n   */\n  const retryConnection = useCallback(async () => {\n    setError(null);\n    return await checkServerConnection();\n  }, [checkServerConnection]);\n  return {\n    // State\n    state,\n    isProcessing,\n    isServerConnected,\n    lastResponse,\n    conversationHistory,\n    error,\n    // Actions\n    updateState,\n    sendMessage,\n    startSpeechRecognition,\n    speakText,\n    clearHistory,\n    retryConnection\n  };\n};\n_s(useJarvis, \"pNeX0CuPI3FBuWRg8LZfx8Dd8qM=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_s", "state", "setState", "isProcessing", "setIsProcessing", "isServerConnected", "setIsServerConnected", "lastResponse", "setLastResponse", "conversationHistory", "setConversationHistory", "error", "setError", "pollingIntervalRef", "mountedRef", "checkServerConnection", "isRunning", "isServerRunning", "current", "err", "updateState", "newState", "console", "message", "sendMessage", "Error", "response", "current_state", "prev", "id", "Date", "now", "user", "jarvis", "timestamp", "textToSpeech", "speechError", "warn", "startSpeechRecognition", "text", "speakText", "clearHistory", "clearConversationHistory", "mounted", "initialize", "pollState", "stateData", "is_processing", "last_response", "stopPolling", "retryConnection"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/hooks/useJarvis.js"], "sourcesContent": ["/**\n * use<PERSON><PERSON><PERSON> Hook\n * Custom React hook for managing Jarvis state and API interactions\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport jarvisApi from '../services/jarvisApi';\n\nexport const useJarvis = () => {\n  // State management\n  const [state, setState] = useState('startup');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isServerConnected, setIsServerConnected] = useState(false);\n  const [lastResponse, setLastResponse] = useState('');\n  const [conversationHistory, setConversationHistory] = useState([]);\n  const [error, setError] = useState(null);\n\n  // Refs for cleanup\n  const pollingIntervalRef = useRef(null);\n  const mountedRef = useRef(true);\n\n  /**\n   * Check server connection\n   */\n  const checkServerConnection = useCallback(async () => {\n    try {\n      const isRunning = await jarvisApi.isServerRunning();\n      if (mountedRef.current) {\n        setIsServerConnected(isRunning);\n        if (isRunning) {\n          setError(null);\n        }\n      }\n      return isRunning;\n    } catch (err) {\n      if (mountedRef.current) {\n        setIsServerConnected(false);\n        setError('Failed to connect to Jarvis backend');\n      }\n      return false;\n    }\n  }, []);\n\n  /**\n   * Update Jarvis state\n   */\n  const updateState = useCallback(async (newState) => {\n    try {\n      await jarvisApi.setState(newState);\n      if (mountedRef.current) {\n        setState(newState);\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Failed to update state:', err);\n      if (mountedRef.current) {\n        setError(`Failed to update state: ${err.message}`);\n      }\n    }\n  }, []);\n\n  /**\n   * Send message to Jarvis\n   */\n  const sendMessage = useCallback(async (message) => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n\n    try {\n      setIsProcessing(true);\n      setError(null);\n\n      const response = await jarvisApi.sendMessage(message);\n\n      if (mountedRef.current) {\n        setLastResponse(response.response);\n        setState(response.current_state || 'rest');\n\n        // Update conversation history\n        setConversationHistory(prev => [...prev, {\n          id: Date.now(),\n          user: message,\n          jarvis: response.response,\n          timestamp: response.timestamp\n        }]);\n\n        // Automatically speak the response\n        if (response.response) {\n          try {\n            await jarvisApi.textToSpeech(response.response);\n          } catch (speechError) {\n            console.warn('Text-to-speech failed:', speechError);\n            // Don't throw error for TTS failure, just log it\n          }\n        }\n      }\n\n      return response;\n    } catch (err) {\n      console.error('Failed to send message:', err);\n      if (mountedRef.current) {\n        setError(`Failed to send message: ${err.message}`);\n      }\n      throw err;\n    } finally {\n      if (mountedRef.current) {\n        setIsProcessing(false);\n      }\n    }\n  }, [isServerConnected]);\n\n  /**\n   * Start speech recognition\n   */\n  const startSpeechRecognition = useCallback(async () => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n\n    try {\n      setIsProcessing(true);\n      setError(null);\n\n      const response = await jarvisApi.startSpeechRecognition();\n\n      if (mountedRef.current && response.text) {\n        // Automatically send the recognized text as a message\n        // The sendMessage function will automatically speak the response\n        return await sendMessage(response.text);\n      }\n\n      return response;\n    } catch (err) {\n      console.error('Speech recognition failed:', err);\n      if (mountedRef.current) {\n        setError(`Speech recognition failed: ${err.message}`);\n      }\n      throw err;\n    } finally {\n      if (mountedRef.current) {\n        setIsProcessing(false);\n      }\n    }\n  }, [isServerConnected, sendMessage]);\n\n  /**\n   * Convert text to speech\n   */\n  const speakText = useCallback(async (text) => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n\n    try {\n      setError(null);\n      const response = await jarvisApi.textToSpeech(text);\n      return response;\n    } catch (err) {\n      console.error('Text-to-speech failed:', err);\n      if (mountedRef.current) {\n        setError(`Text-to-speech failed: ${err.message}`);\n      }\n      throw err;\n    }\n  }, [isServerConnected]);\n\n  /**\n   * Clear conversation history\n   */\n  const clearHistory = useCallback(async () => {\n    try {\n      await jarvisApi.clearConversationHistory();\n      if (mountedRef.current) {\n        setConversationHistory([]);\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Failed to clear history:', err);\n      if (mountedRef.current) {\n        setError(`Failed to clear history: ${err.message}`);\n      }\n    }\n  }, []);\n\n  /**\n   * Initialize connection and start polling\n   */\n  useEffect(() => {\n    let mounted = true;\n    mountedRef.current = true;\n\n    const initialize = async () => {\n      // Check initial connection\n      await checkServerConnection();\n\n      // Start polling for state updates\n      if (mounted) {\n        pollingIntervalRef.current = await jarvisApi.pollState(\n          (stateData) => {\n            if (mountedRef.current) {\n              setState(stateData.current_state);\n              setIsProcessing(stateData.is_processing);\n              setLastResponse(stateData.last_response);\n            }\n          },\n          2000 // Poll every 2 seconds\n        );\n      }\n    };\n\n    initialize();\n\n    // Cleanup function\n    return () => {\n      mounted = false;\n      mountedRef.current = false;\n      if (pollingIntervalRef.current) {\n        jarvisApi.stopPolling(pollingIntervalRef.current);\n      }\n    };\n  }, [checkServerConnection]);\n\n  /**\n   * Retry connection\n   */\n  const retryConnection = useCallback(async () => {\n    setError(null);\n    return await checkServerConnection();\n  }, [checkServerConnection]);\n\n  return {\n    // State\n    state,\n    isProcessing,\n    isServerConnected,\n    lastResponse,\n    conversationHistory,\n    error,\n\n    // Actions\n    updateState,\n    sendMessage,\n    startSpeechRecognition,\n    speakText,\n    clearHistory,\n    retryConnection,\n  };\n};\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAChE,OAAOC,SAAS,MAAM,uBAAuB;AAE7C,OAAO,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,SAAS,CAAC;EAC7C,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACW,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACe,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMmB,kBAAkB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMiB,UAAU,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAE/B;AACF;AACA;EACE,MAAMkB,qBAAqB,GAAGnB,WAAW,CAAC,YAAY;IACpD,IAAI;MACF,MAAMoB,SAAS,GAAG,MAAMlB,SAAS,CAACmB,eAAe,CAAC,CAAC;MACnD,IAAIH,UAAU,CAACI,OAAO,EAAE;QACtBZ,oBAAoB,CAACU,SAAS,CAAC;QAC/B,IAAIA,SAAS,EAAE;UACbJ,QAAQ,CAAC,IAAI,CAAC;QAChB;MACF;MACA,OAAOI,SAAS;IAClB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBZ,oBAAoB,CAAC,KAAK,CAAC;QAC3BM,QAAQ,CAAC,qCAAqC,CAAC;MACjD;MACA,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMQ,WAAW,GAAGxB,WAAW,CAAC,MAAOyB,QAAQ,IAAK;IAClD,IAAI;MACF,MAAMvB,SAAS,CAACI,QAAQ,CAACmB,QAAQ,CAAC;MAClC,IAAIP,UAAU,CAACI,OAAO,EAAE;QACtBhB,QAAQ,CAACmB,QAAQ,CAAC;QAClBT,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZG,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEQ,GAAG,CAAC;MAC7C,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBN,QAAQ,CAAC,2BAA2BO,GAAG,CAACI,OAAO,EAAE,CAAC;MACpD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMC,WAAW,GAAG5B,WAAW,CAAC,MAAO2B,OAAO,IAAK;IACjD,IAAI,CAAClB,iBAAiB,EAAE;MACtB,MAAM,IAAIoB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,IAAI;MACFrB,eAAe,CAAC,IAAI,CAAC;MACrBQ,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMc,QAAQ,GAAG,MAAM5B,SAAS,CAAC0B,WAAW,CAACD,OAAO,CAAC;MAErD,IAAIT,UAAU,CAACI,OAAO,EAAE;QACtBV,eAAe,CAACkB,QAAQ,CAACA,QAAQ,CAAC;QAClCxB,QAAQ,CAACwB,QAAQ,CAACC,aAAa,IAAI,MAAM,CAAC;;QAE1C;QACAjB,sBAAsB,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UACvCC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,IAAI,EAAET,OAAO;UACbU,MAAM,EAAEP,QAAQ,CAACA,QAAQ;UACzBQ,SAAS,EAAER,QAAQ,CAACQ;QACtB,CAAC,CAAC,CAAC;;QAEH;QACA,IAAIR,QAAQ,CAACA,QAAQ,EAAE;UACrB,IAAI;YACF,MAAM5B,SAAS,CAACqC,YAAY,CAACT,QAAQ,CAACA,QAAQ,CAAC;UACjD,CAAC,CAAC,OAAOU,WAAW,EAAE;YACpBd,OAAO,CAACe,IAAI,CAAC,wBAAwB,EAAED,WAAW,CAAC;YACnD;UACF;QACF;MACF;MAEA,OAAOV,QAAQ;IACjB,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZG,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEQ,GAAG,CAAC;MAC7C,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBN,QAAQ,CAAC,2BAA2BO,GAAG,CAACI,OAAO,EAAE,CAAC;MACpD;MACA,MAAMJ,GAAG;IACX,CAAC,SAAS;MACR,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBd,eAAe,CAAC,KAAK,CAAC;MACxB;IACF;EACF,CAAC,EAAE,CAACC,iBAAiB,CAAC,CAAC;;EAEvB;AACF;AACA;EACE,MAAMiC,sBAAsB,GAAG1C,WAAW,CAAC,YAAY;IACrD,IAAI,CAACS,iBAAiB,EAAE;MACtB,MAAM,IAAIoB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,IAAI;MACFrB,eAAe,CAAC,IAAI,CAAC;MACrBQ,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMc,QAAQ,GAAG,MAAM5B,SAAS,CAACwC,sBAAsB,CAAC,CAAC;MAEzD,IAAIxB,UAAU,CAACI,OAAO,IAAIQ,QAAQ,CAACa,IAAI,EAAE;QACvC;QACA;QACA,OAAO,MAAMf,WAAW,CAACE,QAAQ,CAACa,IAAI,CAAC;MACzC;MAEA,OAAOb,QAAQ;IACjB,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZG,OAAO,CAACX,KAAK,CAAC,4BAA4B,EAAEQ,GAAG,CAAC;MAChD,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBN,QAAQ,CAAC,8BAA8BO,GAAG,CAACI,OAAO,EAAE,CAAC;MACvD;MACA,MAAMJ,GAAG;IACX,CAAC,SAAS;MACR,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBd,eAAe,CAAC,KAAK,CAAC;MACxB;IACF;EACF,CAAC,EAAE,CAACC,iBAAiB,EAAEmB,WAAW,CAAC,CAAC;;EAEpC;AACF;AACA;EACE,MAAMgB,SAAS,GAAG5C,WAAW,CAAC,MAAO2C,IAAI,IAAK;IAC5C,IAAI,CAAClC,iBAAiB,EAAE;MACtB,MAAM,IAAIoB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,IAAI;MACFb,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMc,QAAQ,GAAG,MAAM5B,SAAS,CAACqC,YAAY,CAACI,IAAI,CAAC;MACnD,OAAOb,QAAQ;IACjB,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZG,OAAO,CAACX,KAAK,CAAC,wBAAwB,EAAEQ,GAAG,CAAC;MAC5C,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBN,QAAQ,CAAC,0BAA0BO,GAAG,CAACI,OAAO,EAAE,CAAC;MACnD;MACA,MAAMJ,GAAG;IACX;EACF,CAAC,EAAE,CAACd,iBAAiB,CAAC,CAAC;;EAEvB;AACF;AACA;EACE,MAAMoC,YAAY,GAAG7C,WAAW,CAAC,YAAY;IAC3C,IAAI;MACF,MAAME,SAAS,CAAC4C,wBAAwB,CAAC,CAAC;MAC1C,IAAI5B,UAAU,CAACI,OAAO,EAAE;QACtBR,sBAAsB,CAAC,EAAE,CAAC;QAC1BE,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZG,OAAO,CAACX,KAAK,CAAC,0BAA0B,EAAEQ,GAAG,CAAC;MAC9C,IAAIL,UAAU,CAACI,OAAO,EAAE;QACtBN,QAAQ,CAAC,4BAA4BO,GAAG,CAACI,OAAO,EAAE,CAAC;MACrD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE5B,SAAS,CAAC,MAAM;IACd,IAAIgD,OAAO,GAAG,IAAI;IAClB7B,UAAU,CAACI,OAAO,GAAG,IAAI;IAEzB,MAAM0B,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B;MACA,MAAM7B,qBAAqB,CAAC,CAAC;;MAE7B;MACA,IAAI4B,OAAO,EAAE;QACX9B,kBAAkB,CAACK,OAAO,GAAG,MAAMpB,SAAS,CAAC+C,SAAS,CACnDC,SAAS,IAAK;UACb,IAAIhC,UAAU,CAACI,OAAO,EAAE;YACtBhB,QAAQ,CAAC4C,SAAS,CAACnB,aAAa,CAAC;YACjCvB,eAAe,CAAC0C,SAAS,CAACC,aAAa,CAAC;YACxCvC,eAAe,CAACsC,SAAS,CAACE,aAAa,CAAC;UAC1C;QACF,CAAC,EACD,IAAI,CAAC;QACP,CAAC;MACH;IACF,CAAC;IAEDJ,UAAU,CAAC,CAAC;;IAEZ;IACA,OAAO,MAAM;MACXD,OAAO,GAAG,KAAK;MACf7B,UAAU,CAACI,OAAO,GAAG,KAAK;MAC1B,IAAIL,kBAAkB,CAACK,OAAO,EAAE;QAC9BpB,SAAS,CAACmD,WAAW,CAACpC,kBAAkB,CAACK,OAAO,CAAC;MACnD;IACF,CAAC;EACH,CAAC,EAAE,CAACH,qBAAqB,CAAC,CAAC;;EAE3B;AACF;AACA;EACE,MAAMmC,eAAe,GAAGtD,WAAW,CAAC,YAAY;IAC9CgB,QAAQ,CAAC,IAAI,CAAC;IACd,OAAO,MAAMG,qBAAqB,CAAC,CAAC;EACtC,CAAC,EAAE,CAACA,qBAAqB,CAAC,CAAC;EAE3B,OAAO;IACL;IACAd,KAAK;IACLE,YAAY;IACZE,iBAAiB;IACjBE,YAAY;IACZE,mBAAmB;IACnBE,KAAK;IAEL;IACAS,WAAW;IACXI,WAAW;IACXc,sBAAsB;IACtBE,SAAS;IACTC,YAAY;IACZS;EACF,CAAC;AACH,CAAC;AAAClD,EAAA,CAhPWD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}