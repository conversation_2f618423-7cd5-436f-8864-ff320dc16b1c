{"ast": null, "code": "import { warnOnce } from '../../utils/warn-once.mjs';\nimport { useScroll } from '../use-scroll.mjs';\n\n/**\n * @deprecated useViewportScroll is deprecated. Convert to useScroll()\n */\nfunction useViewportScroll() {\n  if (process.env.NODE_ENV !== \"production\") {\n    warnOnce(false, \"useViewportScroll is deprecated. Convert to useScroll().\");\n  }\n  return useScroll();\n}\nexport { useViewportScroll };", "map": {"version": 3, "names": ["warnOnce", "useScroll", "useViewportScroll", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/value/scroll/use-viewport-scroll.mjs"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once.mjs';\nimport { useScroll } from '../use-scroll.mjs';\n\n/**\n * @deprecated useViewportScroll is deprecated. Convert to useScroll()\n */\nfunction useViewportScroll() {\n    if (process.env.NODE_ENV !== \"production\") {\n        warnOnce(false, \"useViewportScroll is deprecated. Convert to useScroll().\");\n    }\n    return useScroll();\n}\n\nexport { useViewportScroll };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,SAAS,QAAQ,mBAAmB;;AAE7C;AACA;AACA;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACvCL,QAAQ,CAAC,KAAK,EAAE,0DAA0D,CAAC;EAC/E;EACA,OAAOC,SAAS,CAAC,CAAC;AACtB;AAEA,SAASC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}