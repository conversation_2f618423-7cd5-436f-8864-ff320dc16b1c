import React, { useState, useEffect } from 'react';

const MenuIcon = ({ onMenuClick, position = 'top-left', size = 'small' }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-12 h-12',
    large: 'w-16 h-16'
  };

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  // Calculate rotation based on mouse position
  const iconCenter = { x: 50, y: 50 }; // Approximate center when positioned
  const angle = Math.atan2(
    mousePosition.y - iconCenter.y,
    mousePosition.x - iconCenter.x
  ) * (180 / Math.PI);

  return (
    <button
      onClick={onMenuClick}
      className={`fixed ${positionClasses[position]} ${sizeClasses[size]} 
                 bg-gradient-to-r from-blue-500 to-purple-600 
                 rounded-full shadow-lg hover:shadow-xl 
                 transition-all duration-300 hover:scale-110
                 flex items-center justify-center z-50`}
      style={{
        transform: `rotate(${angle}deg)`,
        transition: 'transform 0.1s ease-out'
      }}
    >
      <div className="text-white text-xs">🎯</div>
    </button>
  );
};

export default MenuIcon;
