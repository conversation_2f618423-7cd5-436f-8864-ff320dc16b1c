import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Footer } from '../layout';
import { AnimatedButton, CuteMenuIcon, StatusIndicator } from '../ui';

/**
 * ModularHomeScreen - Example of how to use separate screen elements together
 * This demonstrates the modular approach where each UI element is a separate component
 */
const ModularHomeScreen = ({ onEnterJarvis }) => {
  // State management
  const [isEntering, setIsEntering] = useState(false);
  const [showExitAnimation, setShowExitAnimation] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [currentStatus, setCurrentStatus] = useState('idle');
  
  // Refs for animations
  const wallpaperRef = useRef(null);
  const exitAnimationRef = useRef(null);

  // Event handlers
  const handleEnterJarvis = () => {
    setIsEntering(true);
    setCurrentStatus('thinking');
    setShowExitAnimation(true);
    
    // Simulate activation process
    setTimeout(() => {
      setCurrentStatus('speaking');
    }, 1000);
    
    setTimeout(() => {
      onEnterJarvis();
    }, 2000);
  };

  const handleMenuToggle = (isOpen) => {
    setMenuOpen(isOpen);
  };

  const handleSettingsClick = () => {
    console.log('Settings clicked');
    setCurrentStatus('thinking');
    setTimeout(() => setCurrentStatus('idle'), 2000);
  };

  const handleHelpClick = () => {
    console.log('Help clicked');
    setCurrentStatus('speaking');
    setTimeout(() => setCurrentStatus('idle'), 2000);
  };

  const handleVoiceCommandsClick = () => {
    console.log('Voice Commands clicked');
    setCurrentStatus('listening');
    setTimeout(() => setCurrentStatus('idle'), 3000);
  };

  // Effects
  useEffect(() => {
    const wallpaper = wallpaperRef.current;
    if (wallpaper) {
      wallpaper.style.mixBlendMode = 'screen';
      wallpaper.style.filter = 'brightness(0.8) contrast(1.2)';
    }
  }, []);

  return (
    <div className="homescreen-container">
      {/* Header with integrated menu and status */}
      <Header
        title="JARVIS"
        status={currentStatus}
        onMenuClick={handleMenuToggle}
        showMenu={true}
        showStatus={true}
      />

      {/* Cute Menu Icon - positioned separately for custom behavior */}
      <CuteMenuIcon onMenuToggle={handleMenuToggle} />

      {/* Background Elements */}
      <div className="wallpaper-container">
        <img
          ref={wallpaperRef}
          src="/assets/HomeSettingsscreen.gif"
          alt="Animated Wallpaper"
          className="wallpaper-background"
          onError={(e) => {
            e.target.style.display = 'none';
            e.target.parentElement.style.background = 
              'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)';
          }}
        />
        <div className="wallpaper-overlay"></div>
      </div>

      {/* Exit Animation */}
      {showExitAnimation && (
        <div className="exit-animation-overlay">
          <img
            ref={exitAnimationRef}
            src="/assets/screenexit.gif"
            alt="Screen Exit Animation"
            className="exit-animation"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.parentElement.innerHTML = '<div class="fallback-exit-animation"></div>';
            }}
          />
        </div>
      )}

      {/* Main Content Area */}
      <div className={`homescreen-content ${isEntering ? 'entering' : ''}`}>
        {/* Status Indicator */}
        <div className="flex justify-center mb-8">
          <StatusIndicator 
            status={currentStatus} 
            size="large" 
            showLabel={true} 
          />
        </div>

        {/* Central Action Area */}
        <div className="jarvis-icon-container">
          <div className="jarvis-icon">
            <div className="icon-glow"></div>
            <div className="icon-core">
              <svg viewBox="0 0 100 100" className="jarvis-svg">
                <circle cx="50" cy="50" r="30" className="outer-ring" />
                <circle cx="50" cy="50" r="20" className="middle-ring" />
                <circle cx="50" cy="50" r="10" className="inner-core" />
                <path d="M30 50 L70 50 M50 30 L50 70" className="cross-lines" />
              </svg>
            </div>
          </div>
          
          <AnimatedButton
            onClick={handleEnterJarvis}
            variant="primary"
            size="large"
            loading={isEntering}
            disabled={isEntering}
            className="mt-6"
          >
            {isEntering ? 'ACTIVATING JARVIS...' : '⚡ ACTIVATE JARVIS'}
          </AnimatedButton>
        </div>

        {/* Action Buttons Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-12 max-w-2xl mx-auto">
          <AnimatedButton
            onClick={handleSettingsClick}
            variant="secondary"
            size="medium"
            className="flex flex-col items-center p-4"
          >
            <span className="text-2xl mb-2">⚙️</span>
            <span className="text-sm">Settings</span>
          </AnimatedButton>
          
          <AnimatedButton
            onClick={handleVoiceCommandsClick}
            variant="secondary"
            size="medium"
            className="flex flex-col items-center p-4"
          >
            <span className="text-2xl mb-2">🎤</span>
            <span className="text-sm">Voice</span>
          </AnimatedButton>
          
          <AnimatedButton
            onClick={handleHelpClick}
            variant="secondary"
            size="medium"
            className="flex flex-col items-center p-4"
          >
            <span className="text-2xl mb-2">❓</span>
            <span className="text-sm">Help</span>
          </AnimatedButton>
          
          <AnimatedButton
            onClick={() => console.log('About clicked')}
            variant="secondary"
            size="medium"
            className="flex flex-col items-center p-4"
          >
            <span className="text-2xl mb-2">ℹ️</span>
            <span className="text-sm">About</span>
          </AnimatedButton>
        </div>
      </div>

      {/* Footer Navigation */}
      <Footer
        onHomeClick={() => console.log('Home clicked')}
        onSettingsClick={handleSettingsClick}
        onHelpClick={handleHelpClick}
        showNavigation={!menuOpen}
      />
    </div>
  );
};

export default ModularHomeScreen;
