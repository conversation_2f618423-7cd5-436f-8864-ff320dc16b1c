{"ast": null, "code": "function resolveVariantFromProps(props, definition, custom) {\n  let currentValues = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  let currentVelocity = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {};\n  /**\n   * If the variant definition is a function, resolve.\n   */\n  if (typeof definition === \"function\") {\n    definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n  }\n  /**\n   * If the variant definition is a variant label, or\n   * the function returned a variant label, resolve.\n   */\n  if (typeof definition === \"string\") {\n    definition = props.variants && props.variants[definition];\n  }\n  /**\n   * At this point we've resolved both functions and variant labels,\n   * but the resolved variant label might itself have been a function.\n   * If so, resolve. This can only have returned a valid target object.\n   */\n  if (typeof definition === \"function\") {\n    definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n  }\n  return definition;\n}\nexport { resolveVariantFromProps };", "map": {"version": 3, "names": ["resolveVariantFromProps", "props", "definition", "custom", "currentV<PERSON>ues", "arguments", "length", "undefined", "currentVelocity", "variants"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs"], "sourcesContent": ["function resolveVariantFromProps(props, definition, custom, currentValues = {}, currentVelocity = {}) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        definition = definition(custom !== undefined ? custom : props.custom, currentValues, currentVelocity);\n    }\n    return definition;\n}\n\nexport { resolveVariantFromProps };\n"], "mappings": "AAAA,SAASA,uBAAuBA,CAACC,KAAK,EAAEC,UAAU,EAAEC,MAAM,EAA4C;EAAA,IAA1CC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,eAAe,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAChG;AACJ;AACA;EACI,IAAI,OAAOH,UAAU,KAAK,UAAU,EAAE;IAClCA,UAAU,GAAGA,UAAU,CAACC,MAAM,KAAKI,SAAS,GAAGJ,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEC,aAAa,EAAEI,eAAe,CAAC;EACzG;EACA;AACJ;AACA;AACA;EACI,IAAI,OAAON,UAAU,KAAK,QAAQ,EAAE;IAChCA,UAAU,GAAGD,KAAK,CAACQ,QAAQ,IAAIR,KAAK,CAACQ,QAAQ,CAACP,UAAU,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IAClCA,UAAU,GAAGA,UAAU,CAACC,MAAM,KAAKI,SAAS,GAAGJ,MAAM,GAAGF,KAAK,CAACE,MAAM,EAAEC,aAAa,EAAEI,eAAe,CAAC;EACzG;EACA,OAAON,UAAU;AACrB;AAEA,SAASF,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}