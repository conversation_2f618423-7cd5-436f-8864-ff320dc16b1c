{"ast": null, "code": "import { isPrimaryPointer } from './utils/is-primary-pointer.mjs';\nfunction extractEventInfo(event) {\n  let pointType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"page\";\n  return {\n    point: {\n      x: event[pointType + \"X\"],\n      y: event[pointType + \"Y\"]\n    }\n  };\n}\nconst addPointerInfo = handler => {\n  return event => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\nexport { addPointerInfo, extractEventInfo };", "map": {"version": 3, "names": ["isPrimaryPointer", "extractEventInfo", "event", "pointType", "arguments", "length", "undefined", "point", "x", "y", "addPointerInfo", "handler"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/events/event-info.mjs"], "sourcesContent": ["import { isPrimaryPointer } from './utils/is-primary-pointer.mjs';\n\nfunction extractEventInfo(event, pointType = \"page\") {\n    return {\n        point: {\n            x: event[pointType + \"X\"],\n            y: event[pointType + \"Y\"],\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\n\nexport { addPointerInfo, extractEventInfo };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,gCAAgC;AAEjE,SAASC,gBAAgBA,CAACC,KAAK,EAAsB;EAAA,IAApBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAM;EAC/C,OAAO;IACHG,KAAK,EAAE;MACHC,CAAC,EAAEN,KAAK,CAACC,SAAS,GAAG,GAAG,CAAC;MACzBM,CAAC,EAAEP,KAAK,CAACC,SAAS,GAAG,GAAG;IAC5B;EACJ,CAAC;AACL;AACA,MAAMO,cAAc,GAAIC,OAAO,IAAK;EAChC,OAAQT,KAAK,IAAKF,gBAAgB,CAACE,KAAK,CAAC,IAAIS,OAAO,CAACT,KAAK,EAAED,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACxF,CAAC;AAED,SAASQ,cAAc,EAAET,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}