{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\components\\\\ui\\\\ExpandableSidebar.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpandableSidebar = () => {\n  return /*#__PURE__*/_jsxDEV(\"ul\", {\n    className: \"w-64 flex flex-col gap-1 border-l border-cyan-500/30 pl-1\",\n    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n      className: \"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-purple-300 transition-all active:scale-95 hover:text-purple-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-lg border-2 border-purple-400/50 bg-purple-900/50 p-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"size-6\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 10,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-semibold text-cyan-100\",\n          children: \"Notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"divide-y divide-gray-600/50 p-4 pt-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"py-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 20,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-400\",\n                  children: \"2m ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 23,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: \"from Wanye Enterprises\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"py-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",\n                  children: \"Request\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-400\",\n                  children: \"14m ago\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: \"from Acme Corporation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n      className: \"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-blue-300 transition-all active:scale-95 hover:text-blue-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-lg border-2 border-blue-400/50 bg-blue-900/50 p-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"size-6\",\n            stroke: \"currentColor\",\n            strokeWidth: \"1.5\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\",\n              strokeLinejoin: \"round\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\",\n              strokeLinejoin: \"round\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-semibold text-cyan-100\",\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"divide-y divide-gray-600/50 p-4 pt-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"py-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",\n                  children: \"System Preferences\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-400 transition-all peer-hover:translate-x-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 60,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 58,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: \"Default Settings / Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"py-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group/title flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",\n                  children: \"Theme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-400 transition-all peer-hover:translate-x-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 73,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: \"Light / Dark Mode\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n      className: \"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-green-300 transition-all active:scale-95 hover:text-green-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-lg border-2 border-green-400/50 bg-green-900/50 p-1\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"size-6\",\n            stroke: \"currentColor\",\n            strokeWidth: \"1.5\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\",\n              strokeLinejoin: \"round\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-semibold text-cyan-100\",\n          children: \"Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"border-t border-gray-600/50 p-4 pt-0.5\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex flex-col items-end\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right text-xs text-gray-400\",\n                children: \"8:34 AM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-40 rounded-lg bg-cyan-600/70 px-2 py-1 text-right text-sm text-white\",\n                children: \"Hey JARVIS, what's your status?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex flex-col items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right text-xs text-gray-400\",\n                children: \"8:37 AM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-40 rounded-lg bg-gray-700/80 px-2 py-1 text-sm text-cyan-100\",\n                children: \"All systems operational.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              className: \"h-8 w-full rounded-b-lg border border-gray-600/50 bg-gray-800/80 pl-2 text-sm text-cyan-100 placeholder-gray-400 focus:border-cyan-400/50 focus:outline-none focus:ring-1 focus:ring-cyan-400/30\",\n              placeholder: \"Reply\",\n              type: \"text\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute bottom-0 right-2 top-0 my-auto size-fit cursor-pointer text-cyan-400 hover:text-cyan-300\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"size-5\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1.5\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\",\n                  strokeLinejoin: \"round\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = ExpandableSidebar;\nexport default ExpandableSidebar;\nvar _c;\n$RefreshReg$(_c, \"ExpandableSidebar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ExpandableSidebar", "className", "children", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ui/ExpandableSidebar.js"], "sourcesContent": ["import React from 'react';\n\nconst ExpandableSidebar = () => {\n  return (\n    <ul className=\"w-64 flex flex-col gap-1 border-l border-cyan-500/30 pl-1\">\n      <li className=\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\">\n        <button className=\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-purple-300 transition-all active:scale-95 hover:text-purple-200\">\n          <div className=\"rounded-lg border-2 border-purple-400/50 bg-purple-900/50 p-1\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"size-6\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\" />\n            </svg>\n          </div>\n          <div className=\"font-semibold text-cyan-100\">Notifications</div>\n        </button>\n        <div className=\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\">\n          <div className=\"overflow-hidden\">\n            <ul className=\"divide-y divide-gray-600/50 p-4 pt-0\">\n              <li className=\"py-2\">\n                <div className=\"flex items-center justify-between\">\n                  <button className=\"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    Email\n                  </button>\n                  <div className=\"text-sm text-gray-400\">2m ago</div>\n                </div>\n                <div className=\"text-xs text-gray-500\">from Wanye Enterprises</div>\n              </li>\n              <li className=\"py-1\">\n                <div className=\"flex items-center justify-between\">\n                  <button className=\"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    Request\n                  </button>\n                  <div className=\"text-sm text-gray-400\">14m ago</div>\n                </div>\n                <div className=\"text-xs text-gray-500\">from Acme Corporation</div>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </li>\n      <li className=\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\">\n        <button className=\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-blue-300 transition-all active:scale-95 hover:text-blue-200\">\n          <div className=\"rounded-lg border-2 border-blue-400/50 bg-blue-900/50 p-1\">\n            <svg className=\"size-6\" stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n              <path d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n            </svg>\n          </div>\n          <div className=\"font-semibold text-cyan-100\">Settings</div>\n        </button>\n        <div className=\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\">\n          <div className=\"overflow-hidden\">\n            <ul className=\"divide-y divide-gray-600/50 p-4 pt-0\">\n              <li className=\"py-2\">\n                <div className=\"flex items-center justify-between\">\n                  <button className=\"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    System Preferences\n                  </button>\n                  <div className=\"text-sm text-gray-400 transition-all peer-hover:translate-x-1\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"size-4\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m8.25 4.5 7.5 7.5-7.5 7.5\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"text-xs text-gray-500\">Default Settings / Profile</div>\n              </li>\n              <li className=\"py-1\">\n                <div className=\"group/title flex items-center justify-between\">\n                  <button className=\"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    Theme\n                  </button>\n                  <div className=\"text-sm text-gray-400 transition-all peer-hover:translate-x-1\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"size-4\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m8.25 4.5 7.5 7.5-7.5 7.5\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"text-xs text-gray-500\">Light / Dark Mode</div>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </li>\n      <li className=\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\">\n        <button className=\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-green-300 transition-all active:scale-95 hover:text-green-200\">\n          <div className=\"rounded-lg border-2 border-green-400/50 bg-green-900/50 p-1\">\n            <svg className=\"size-6\" stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n            </svg>\n          </div>\n          <div className=\"font-semibold text-cyan-100\">Chat</div>\n        </button>\n        <div className=\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\">\n          <div className=\"overflow-hidden\">\n            <ul className=\"border-t border-gray-600/50 p-4 pt-0.5\">\n              <li className=\"flex flex-col items-end\">\n                <div className=\"text-right text-xs text-gray-400\">8:34 AM</div>\n                <div className=\"w-40 rounded-lg bg-cyan-600/70 px-2 py-1 text-right text-sm text-white\">\n                  Hey JARVIS, what's your status?\n                </div>\n              </li>\n              <li className=\"flex flex-col items-start\">\n                <div className=\"text-right text-xs text-gray-400\">8:37 AM</div>\n                <div className=\"w-40 rounded-lg bg-gray-700/80 px-2 py-1 text-sm text-cyan-100\">\n                  All systems operational.\n                </div>\n              </li>\n            </ul>\n            <div className=\"relative\">\n              <input className=\"h-8 w-full rounded-b-lg border border-gray-600/50 bg-gray-800/80 pl-2 text-sm text-cyan-100 placeholder-gray-400 focus:border-cyan-400/50 focus:outline-none focus:ring-1 focus:ring-cyan-400/30\" placeholder=\"Reply\" type=\"text\" />\n              <button className=\"absolute bottom-0 right-2 top-0 my-auto size-fit cursor-pointer text-cyan-400 hover:text-cyan-300\">\n                <svg className=\"size-5\" stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </li>\n    </ul>\n  );\n}\n\nexport default ExpandableSidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,oBACED,OAAA;IAAIE,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBACvEH,OAAA;MAAIE,SAAS,EAAC,qRAAqR;MAAAC,QAAA,gBACjSH,OAAA;QAAQE,SAAS,EAAC,+IAA+I;QAAAC,QAAA,gBAC/JH,OAAA;UAAKE,SAAS,EAAC,+DAA+D;UAAAC,QAAA,eAC5EH,OAAA;YAAKI,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,WAAW,EAAC,KAAK;YAACC,MAAM,EAAC,cAAc;YAACN,SAAS,EAAC,QAAQ;YAAAC,QAAA,eAChIH,OAAA;cAAMS,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAwN;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7Q;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAa;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACTf,OAAA;QAAKE,SAAS,EAAC,6FAA6F;QAAAC,QAAA,eAC1GH,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BH,OAAA;YAAIE,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAClDH,OAAA;cAAIE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAClBH,OAAA;gBAAKE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDH,OAAA;kBAAQE,SAAS,EAAC,gEAAgE;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTf,OAAA;kBAAKE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACNf,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAsB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACLf,OAAA;cAAIE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAClBH,OAAA;gBAAKE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDH,OAAA;kBAAQE,SAAS,EAAC,gEAAgE;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTf,OAAA;kBAAKE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNf,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACLf,OAAA;MAAIE,SAAS,EAAC,qRAAqR;MAAAC,QAAA,gBACjSH,OAAA;QAAQE,SAAS,EAAC,2IAA2I;QAAAC,QAAA,gBAC3JH,OAAA;UAAKE,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACxEH,OAAA;YAAKE,SAAS,EAAC,QAAQ;YAACM,MAAM,EAAC,cAAc;YAACD,WAAW,EAAC,KAAK;YAACD,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,MAAM;YAACD,KAAK,EAAC,4BAA4B;YAAAD,QAAA,gBAChIH,OAAA;cAAMW,CAAC,EAAC,y+BAAy+B;cAACD,cAAc,EAAC,OAAO;cAACD,aAAa,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjiCf,OAAA;cAAMW,CAAC,EAAC,qCAAqC;cAACD,cAAc,EAAC,OAAO;cAACD,aAAa,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAQ;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACTf,OAAA;QAAKE,SAAS,EAAC,6FAA6F;QAAAC,QAAA,eAC1GH,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BH,OAAA;YAAIE,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAClDH,OAAA;cAAIE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAClBH,OAAA;gBAAKE,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDH,OAAA;kBAAQE,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,EAAC;gBAExF;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTf,OAAA;kBAAKE,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,eAC5EH,OAAA;oBAAKI,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAACN,SAAS,EAAC,QAAQ;oBAAAC,QAAA,eAChIH,OAAA;sBAAMS,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA2B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNf,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA0B;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACLf,OAAA;cAAIE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAClBH,OAAA;gBAAKE,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAC5DH,OAAA;kBAAQE,SAAS,EAAC,qEAAqE;kBAAAC,QAAA,EAAC;gBAExF;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTf,OAAA;kBAAKE,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,eAC5EH,OAAA;oBAAKI,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACC,WAAW,EAAC,KAAK;oBAACC,MAAM,EAAC,cAAc;oBAACN,SAAS,EAAC,QAAQ;oBAAAC,QAAA,eAChIH,OAAA;sBAAMS,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA2B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNf,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACLf,OAAA;MAAIE,SAAS,EAAC,qRAAqR;MAAAC,QAAA,gBACjSH,OAAA;QAAQE,SAAS,EAAC,6IAA6I;QAAAC,QAAA,gBAC7JH,OAAA;UAAKE,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1EH,OAAA;YAAKE,SAAS,EAAC,QAAQ;YAACM,MAAM,EAAC,cAAc;YAACD,WAAW,EAAC,KAAK;YAACD,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,MAAM;YAACD,KAAK,EAAC,4BAA4B;YAAAD,QAAA,eAChIH,OAAA;cAAMW,CAAC,EAAC,4eAA4e;cAACD,cAAc,EAAC,OAAO;cAACD,aAAa,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjiB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACTf,OAAA;QAAKE,SAAS,EAAC,6FAA6F;QAAAC,QAAA,eAC1GH,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BH,OAAA;YAAIE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACpDH,OAAA;cAAIE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACrCH,OAAA;gBAAKE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/Df,OAAA;gBAAKE,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,EAAC;cAExF;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLf,OAAA;cAAIE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACvCH,OAAA;gBAAKE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/Df,OAAA;gBAAKE,SAAS,EAAC,gEAAgE;gBAAAC,QAAA,EAAC;cAEhF;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACLf,OAAA;YAAKE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBH,OAAA;cAAOE,SAAS,EAAC,kMAAkM;cAACc,WAAW,EAAC,OAAO;cAACC,IAAI,EAAC;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtPf,OAAA;cAAQE,SAAS,EAAC,mGAAmG;cAAAC,QAAA,eACnHH,OAAA;gBAAKE,SAAS,EAAC,QAAQ;gBAACM,MAAM,EAAC,cAAc;gBAACD,WAAW,EAAC,KAAK;gBAACD,OAAO,EAAC,WAAW;gBAACD,IAAI,EAAC,MAAM;gBAACD,KAAK,EAAC,4BAA4B;gBAAAD,QAAA,eAChIH,OAAA;kBAAMW,CAAC,EAAC,oEAAoE;kBAACD,cAAc,EAAC,OAAO;kBAACD,aAAa,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAET,CAAC;AAAAG,EAAA,GAtHKjB,iBAAiB;AAwHvB,eAAeA,iBAAiB;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}