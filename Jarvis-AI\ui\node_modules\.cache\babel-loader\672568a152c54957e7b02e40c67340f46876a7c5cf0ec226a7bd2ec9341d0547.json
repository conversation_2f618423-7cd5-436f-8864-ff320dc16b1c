{"ast": null, "code": "import { isCustomValue } from '../../utils/resolve-value.mjs';\nimport { isMotionValue } from './is-motion-value.mjs';\n\n/**\n * If the provided value is a MotionValue, this returns the actual value, otherwise just the value itself\n *\n * TODO: Remove and move to library\n */\nfunction resolveMotionValue(value) {\n  const unwrappedValue = isMotionValue(value) ? value.get() : value;\n  return isCustomValue(unwrappedValue) ? unwrappedValue.toValue() : unwrappedValue;\n}\nexport { resolveMotionValue };", "map": {"version": 3, "names": ["isCustomValue", "isMotionValue", "resolveMotionValue", "value", "unwrappedValue", "get", "toValue"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs"], "sourcesContent": ["import { isCustomValue } from '../../utils/resolve-value.mjs';\nimport { isMotionValue } from './is-motion-value.mjs';\n\n/**\n * If the provided value is a MotionValue, this returns the actual value, otherwise just the value itself\n *\n * TODO: Remove and move to library\n */\nfunction resolveMotionValue(value) {\n    const unwrappedValue = isMotionValue(value) ? value.get() : value;\n    return isCustomValue(unwrappedValue)\n        ? unwrappedValue.toValue()\n        : unwrappedValue;\n}\n\nexport { resolveMotionValue };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,aAAa,QAAQ,uBAAuB;;AAErD;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EAC/B,MAAMC,cAAc,GAAGH,aAAa,CAACE,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,CAAC,CAAC,GAAGF,KAAK;EACjE,OAAOH,aAAa,CAACI,cAAc,CAAC,GAC9BA,cAAc,CAACE,OAAO,CAAC,CAAC,GACxBF,cAAc;AACxB;AAEA,SAASF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}