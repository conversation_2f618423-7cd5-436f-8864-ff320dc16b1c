@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: 'Segoe UI', 'Roboto', 'Oxygen', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
  background: transparent;
}

.glass {
  background: rgba(10, 14, 41, 0.7);
  backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(0, 238, 255, 0.1);
}

/* Sci-Fi Terminal Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 30, 60, 0.4);
}

::-webkit-scrollbar-thumb {
  background: #00eeff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ff00aa;
}

/* Smooth GIF rendering and transitions */
img, video {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* Ensure smooth GIF loops */
img[src$=".gif"] {
  image-rendering: auto;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  animation-fill-mode: both;
}

/* Hardware acceleration for smooth transitions */
.motion-div {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Custom Mic Button Styles */
.container {
  --UnChacked-color: hsl(0, 0%, 10%);
  --chacked-color: hsl(216, 100%, 60%);
  --font-color: white;
  --chacked-font-color: var(--font-color);
  --icon-size: 1.5em;
  --anim-time: 0.2s;
  --anim-scale: 0.1;
  --base-radius: 0.8em;
}

.container {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  font-size: 20px;
  user-select: none;
  fill: var(--font-color);
  color: var(--font-color);
}

/* Hide the default checkbox */
.container input {
  display: none;
}

/* Base custom checkbox */
.checkmark {
  background: var(--UnChacked-color);
  border-radius: var(--base-radius);
  display: flex;
  padding: 0.5em;
}

.icon {
  width: var(--icon-size);
  height: auto;
  filter: drop-shadow(0px 2px var(--base-radius) rgba(0, 0, 0, 0.25));
}

.name {
  margin: 0 0.25em;
}

.Yes {
  width: 0;
}

.name.Yes {
  display: none;
}

/* action custom checkbox */
.container:hover .checkmark,
.container:hover .icon,
.container:hover .name {
  transform: scale(calc(1 + var(--anim-scale)));
}

.container:active .checkmark,
.container:active .icon,
.container:active .name {
  transform: scale(calc(1 - var(--anim-scale) / 2));
  border-radius: calc(var(--base-radius) * 2);
}

.checkmark::before {
  content: "";
  opacity: 0.5;
  transform: scale(1);
  border-radius: var(--base-radius);
  position: absolute;
  box-sizing: border-box;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.checkmark:hover:before {
  background-color: hsla(0, 0%, 50%, 0.2);
}

.container input:checked + .checkmark:before {
  animation: boon calc(var(--anim-time)) ease;
  animation-delay: calc(var(--anim-time) / 2);
}

/* When the checkbox is checked*/
.container input:checked + .checkmark {
  --UnChacked-color: var(--chacked-color);
  fill: var(--chacked-font-color);
  color: var(--chacked-font-color);
}

.container input:checked ~ .checkmark .No {
  width: 0;
}

.container input:checked ~ .checkmark .name.No {
  display: none;
}

.container input:checked ~ .checkmark .Yes {
  width: var(--icon-size);
}

.container input:checked ~ .checkmark .name.Yes {
  width: auto;
  display: unset;
}

/*Animation*/
.container,
.checkmark,
.checkmark:after,
.icon,
.checkmark .name {
  transition: all var(--anim-time);
}

/*Unuse*/
@keyframes icon-rot {
  50% {
    transform: rotateZ(180deg) scale(calc(1 - var(--anim-scale)));
    border-radius: 1em;
  }
  to {
    transform: rotate(360deg);
    border-radius: var(--base-radius);
  }
}

/*Unuse*/
@keyframes boo {
  80% {
    transform: scale(1.4);
  }
  99% {
    transform: scale(1.7);
    border: 2px solid var(--UnChacked-color);
  }
  to {
    transform: scale(0);
  }
}

/* Menu Styles */
.menu {
  padding: 0.3rem;
  background: linear-gradient(135deg, rgba(10, 14, 41, 0.9), rgba(0, 30, 60, 0.8));
  backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(0, 238, 255, 0.2);
  position: relative;
  display: flex;
  justify-content: center;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 238, 255, 0.1), 0 4px 16px rgba(0, 0, 0, 0.3);
}

.link {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 45px;
  height: 35px;
  border-radius: 6px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  transform-origin: center left;
  transition: width 0.2s ease-in;
  text-decoration: none;
  color: #00eeff;
}

.link:before {
  position: absolute;
  z-index: -1;
  content: "";
  display: block;
  border-radius: 6px;
  width: 100%;
  height: 100%;
  top: 0;
  transform: translateX(100%);
  transition: transform 0.2s ease-in;
  transform-origin: center right;
  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));
}

.link:hover,
.link:focus {
  outline: 0;
  width: 100px;
}

.link:hover:before,
.link:hover .link-title,
.link:focus:before,
.link:focus .link-title {
  transform: translateX(0);
  opacity: 1;
}

.link-icon {
  width: 20px;
  height: 20px;
  display: block;
  flex-shrink: 0;
  left: 12px;
  position: absolute;
}

.link-icon svg {
  width: 20px;
  height: 20px;
}

.link-title {
  transform: translateX(100%);
  transition: transform 0.2s ease-in;
  transform-origin: center right;
  display: block;
  text-align: center;
  text-indent: 20px;
  width: 100%;
  color: #00eeff;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Cute Menu Icon - Exact Reference Implementation */
.cute-menu-wrapper {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 12px;
}

.cute-menu-icon-container {
  width: 80px;
  height: 80px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cute-menu-icon-container:hover {
  transform: scale(1.05);
}

.cute-icon-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    #00f5ff 0%,
    #00d4ff 25%,
    #0099ff 50%,
    #6b73ff 75%,
    #9b59b6 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 32px rgba(0, 245, 255, 0.3),
    0 4px 16px rgba(155, 89, 182, 0.2);
  position: relative;
  overflow: hidden;
}

.cute-icon-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 20px;
}

.cute-eyes {
  display: flex;
  gap: 12px;
  z-index: 2;
  position: relative;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.cute-eye {
  width: 12px;
  height: 24px;
  background: white;
  border-radius: 12px;
  transition: transform 0.1s ease-out, opacity 0.3s ease;
  animation: eyeBlink 4s infinite;
}

@keyframes eyeBlink {
  0%, 90%, 100% {
    height: 24px;
  }
  95% {
    height: 4px;
  }
}

.cute-eye.hidden {
  opacity: 0;
}

.cute-happy-eyes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.cute-happy-eyes.visible {
  opacity: 1;
}

.happy-eye {
  width: 20px;
  height: 20px;
}

.click-me-label {
  color: #00eeff;
  font-size: 0.85rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(0, 238, 255, 0.5);
  animation: textPulse 2s ease-in-out infinite;
  white-space: nowrap;
}

.cute-dropdown-menu {
  position: absolute;
  top: 90px;
  left: 0;
  background: linear-gradient(135deg, rgba(10, 14, 41, 0.95), rgba(0, 30, 60, 0.9));
  backdrop-filter: blur(12px);
  border-radius: 12px;
  padding: 12px;
  min-width: 180px;
  box-shadow: 0 8px 32px rgba(0, 238, 255, 0.2);
  border: 1px solid rgba(0, 238, 255, 0.3);
  animation: menuSlideIn 0.3s ease-out;
}

.menu-option {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ffffff;
  font-size: 0.85rem;
}

.menu-option:hover {
  background: rgba(0, 238, 255, 0.1);
  transform: translateX(3px);
}

.menu-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.notifications-icon {
  background: linear-gradient(135deg, #9147ff, #b347ff);
}

.settings-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.chat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.menu-icon svg {
  width: 16px;
  height: 16px;
  color: white;
}

@keyframes textPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes menuSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* HomeScreen Styles */
.homescreen-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wallpaper-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  animation: backgroundShift 8s ease-in-out infinite;
}

.wallpaper-background {
  width: 100%;
  height: 100%;
  object-fit: cover;
  mix-blend-mode: screen;
  filter: brightness(0.8) contrast(1.2);
  animation: wallpaperLoop 0.5s ease-in-out infinite alternate;
}

/* Animated background particles effect */
.wallpaper-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 238, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 170, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 238, 255, 0.05) 0%, transparent 50%);
  animation: particleFloat 12s ease-in-out infinite;
}

.wallpaper-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(45deg, transparent 30%, rgba(0, 238, 255, 0.03) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 0, 170, 0.03) 50%, transparent 70%);
  animation: scanLines 6s linear infinite;
}

.wallpaper-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(10, 14, 41, 0.4) 50%,
    rgba(0, 0, 0, 0.3) 100%
  );
  backdrop-filter: blur(1px);
}

.exit-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  animation: fadeIn 0.3s ease-in-out;
}

.exit-animation {
  width: 100%;
  height: 100%;
  object-fit: cover;
  mix-blend-mode: screen;
}

.fallback-exit-animation {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(circle, #00eeff 0%, transparent 70%);
  animation: pulseExit 2s ease-in-out;
}

.homescreen-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100vh;
  padding: 2rem;
  transition: all 0.5s ease-in-out;
}

.homescreen-content.entering {
  opacity: 0;
  transform: scale(0.9);
}

/* Title Section Styles */
.title-section {
  text-align: center;
  margin-top: 2rem;
}

.marvel-logo {
  font-size: 1.2rem;
  font-weight: bold;
  color: #ff0000;
  background: linear-gradient(45deg, #ff0000, #ff6b6b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.2em;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.main-title {
  font-size: 4rem;
  font-weight: bold;
  color: #ffffff;
  text-shadow:
    0 0 20px rgba(0, 238, 255, 0.5),
    0 0 40px rgba(0, 238, 255, 0.3),
    0 0 60px rgba(0, 238, 255, 0.2);
  letter-spacing: 0.1em;
  margin: 0;
  animation: titleGlow 2s ease-in-out infinite alternate;
}

.subtitle {
  font-size: 1rem;
  color: #00eeff;
  margin-top: 1rem;
  opacity: 0.8;
  animation: subtitlePulse 1.5s ease-in-out infinite;
}

/* Jarvis Icon Styles */
.jarvis-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.jarvis-icon-container:hover {
  transform: scale(1.05);
}

.jarvis-icon {
  position: relative;
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 238, 255, 0.3) 0%, transparent 70%);
  animation: iconGlow 2s ease-in-out infinite alternate;
}

.icon-core {
  position: relative;
  z-index: 2;
  width: 80%;
  height: 80%;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));
  border: 2px solid #00eeff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}

.jarvis-icon-container:hover .icon-core {
  border-color: #ff00aa;
  box-shadow: 0 0 30px rgba(0, 238, 255, 0.5);
}

.jarvis-svg {
  width: 60%;
  height: 60%;
  fill: none;
  stroke: #00eeff;
  stroke-width: 2;
  transition: all 0.3s ease-in-out;
}

.outer-ring {
  stroke-dasharray: 188;
  stroke-dashoffset: 188;
  animation: drawRing 3s ease-in-out infinite;
}

.middle-ring {
  stroke-dasharray: 125;
  stroke-dashoffset: 125;
  animation: drawRing 3s ease-in-out infinite 0.5s;
}

.inner-core {
  fill: #00eeff;
  opacity: 0.8;
  animation: coreGlow 2s ease-in-out infinite alternate;
}

.cross-lines {
  stroke-dasharray: 40;
  stroke-dashoffset: 40;
  animation: drawRing 3s ease-in-out infinite 1s;
}

.icon-label {
  font-size: 0.9rem;
  color: #00eeff;
  font-weight: 500;
  letter-spacing: 0.1em;
  opacity: 0.9;
}

/* Bottom Menu Styles */
.bottom-menu {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.menu-item {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(10, 14, 41, 0.9), rgba(0, 30, 60, 0.8));
  backdrop-filter: blur(12px) saturate(180%);
  border: 1px solid rgba(0, 238, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 16px rgba(0, 238, 255, 0.1);
}

.menu-item:hover {
  transform: translateY(-5px) scale(1.05);
  border-color: #00eeff;
  box-shadow: 0 8px 32px rgba(0, 238, 255, 0.3);
  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));
}

.menu-item:hover::before {
  opacity: 1;
  transform: scale(1);
}

.menu-item::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%) scale(0.8);
  background: rgba(0, 0, 0, 0.9);
  color: #00eeff;
  padding: 0.5rem 0.8rem;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  pointer-events: none;
  border: 1px solid rgba(0, 238, 255, 0.3);
}

.menu-letter {
  font-size: 1.2rem;
  font-weight: bold;
  color: #00eeff;
  transition: all 0.3s ease-in-out;
}

.menu-item:hover .menu-letter {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 238, 255, 0.8);
}

/* Animations */
@keyframes wallpaperLoop {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulseExit {
  0% { transform: scale(0); opacity: 0; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(2); opacity: 0; }
}

@keyframes titleGlow {
  0% { text-shadow: 0 0 20px rgba(0, 238, 255, 0.5), 0 0 40px rgba(0, 238, 255, 0.3); }
  100% { text-shadow: 0 0 30px rgba(0, 238, 255, 0.8), 0 0 60px rgba(0, 238, 255, 0.5); }
}

@keyframes subtitlePulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes iconGlow {
  0% { transform: scale(1); opacity: 0.3; }
  100% { transform: scale(1.1); opacity: 0.6; }
}

@keyframes drawRing {
  0% { stroke-dashoffset: 188; }
  50% { stroke-dashoffset: 0; }
  100% { stroke-dashoffset: -188; }
}

@keyframes coreGlow {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

/* Enhanced animations for MinimizedJarvis */
@keyframes iconGlow {
  0% { transform: scale(1); opacity: 0.3; }
  100% { transform: scale(1.1); opacity: 0.6; }
}

@keyframes drawRing {
  0% { stroke-dashoffset: 220; }
  50% { stroke-dashoffset: 0; }
  100% { stroke-dashoffset: -220; }
}

@keyframes backgroundShift {
  0% {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  }
  25% {
    background: linear-gradient(225deg, #16213e 0%, #0f3460 50%, #1a1a2e 100%);
  }
  50% {
    background: linear-gradient(315deg, #0f3460 0%, #1a1a2e 50%, #16213e 100%);
  }
  75% {
    background: linear-gradient(45deg, #1a1a2e 0%, #0f3460 50%, #16213e 100%);
  }
  100% {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
    opacity: 0.6;
  }
  66% {
    transform: translateY(10px) rotate(240deg);
    opacity: 0.4;
  }
}

@keyframes scanLines {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* AI Assistant Component Styles */
.container-ai-input {
  --perspective: 1000px;
  --translateY: 45px;
  position: absolute;
  left: 0;
  right: 0;
  top: -2.5rem;
  bottom: -2.5rem;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  transform-style: preserve-3d;
}

.container-wrap {
  display: flex;
  align-items: center;
  justify-items: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 9;
  transform-style: preserve-3d;
  cursor: pointer;
  padding: 4px;
  transition: all 0.3s ease;
}

.container-wrap:hover {
  padding: 0;
}

.container-wrap:active {
  transform: translateX(-50%) translateY(-50%) scale(0.95);
}

.container-wrap:after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-55%);
  width: 12rem;
  height: 11rem;
  background-color: #dedfe0;
  border-radius: 3.2rem;
  transition: all 0.3s ease;
}

.container-wrap:hover:after {
  transform: translateX(-50%) translateY(-50%);
  height: 12rem;
}

.container-wrap input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.container-wrap input:checked + .card .eyes {
  opacity: 0;
}

.container-wrap input:checked + .card .content-card {
  width: 260px;
  height: 160px;
}

.container-wrap input:checked + .card .background-blur-balls {
  border-radius: 20px;
}

.container-wrap input:checked + .card .container-ai-chat {
  opacity: 1;
  visibility: visible;
  z-index: 99999;
  pointer-events: visible;
}

.card {
  width: 100%;
  height: 100%;
  /* background-color: #fff; */
  transform-style: preserve-3d;
  will-change: transform;
  transition: all 0.6s ease;
  border-radius: 3rem;
  display: flex;
  align-items: center;
  transform: translateZ(50px);
  justify-content: center;
}

.card:hover {
  box-shadow:
    0 10px 40px rgba(0, 0, 60, 0.25),
    inset 0 0 10px rgba(255, 255, 255, 0.5);
}

.background-blur-balls {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  width: 100%;
  height: 100%;
  z-index: -10;
  border-radius: 3rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

.balls {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  animation: rotate-background-balls 10s linear infinite;
}

.container-wrap:hover .balls {
  animation-play-state: paused;
}

.background-blur-balls .ball {
  width: 6rem;
  height: 6rem;
  position: absolute;
  border-radius: 50%;
  filter: blur(30px);
}

.background-blur-balls .ball.violet {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background-color: #9147ff;
}

.background-blur-balls .ball.green {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background-color: #34d399;
}

.background-blur-balls .ball.rosa {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  background-color: #ec4899;
}

.background-blur-balls .ball.cyan {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background-color: #05e0f5;
}

.content-card {
  width: 12rem;
  height: 12rem;
  display: flex;
  border-radius: 3rem;
  transition: all 0.3s ease;
  overflow: hidden;
}

.background-blur-card {
  width: 100%;
  height: 100%;
  backdrop-filter: blur(50px);
}

.eyes {
  position: absolute;
  left: 50%;
  bottom: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 26px;
  gap: 1rem;
  transition: all 0.3s ease;
}

.eyes .eye {
  width: 13px;
  height: 26px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(0, 238, 255, 0.3));
  border-radius: 8px;
  animation: animate-eyes 10s infinite linear;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 238, 255, 0.4);
}

.eyes.happy {
  display: none;
  color: #fff;
  gap: 0;
}

.eyes.happy svg {
  width: 60px;
}

.container-wrap:hover .eyes .eye {
  display: none;
}

.container-wrap:hover .eyes.happy {
  display: flex;
}

.container-ai-chat {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 6px;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  transition: all 0.3s ease;
}

.container-wrap .card .chat {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  border-radius: 15px;
  width: 100%;
  height: 100%;
  padding: 4px;
  overflow: hidden;
  background-color: #ffffff;
}

.options-menu {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  width: 100%;
  height: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(0, 238, 255, 0.1), rgba(255, 0, 170, 0.1));
  border: 1px solid rgba(0, 238, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.option-item:hover {
  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));
  border-color: #00eeff;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 238, 255, 0.3);
}

.option-item span {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.click-me-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: #00eeff;
  text-shadow: 0 0 8px rgba(0, 238, 255, 0.5);
  animation: clickMePulse 2s ease-in-out infinite;
  white-space: nowrap;
  pointer-events: none;
}

@keyframes clickMePulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes rotate-background-balls {
  from {
    transform: translateX(-50%) translateY(-50%) rotate(360deg);
  }
  to {
    transform: translateX(-50%) translateY(-50%) rotate(0);
  }
}

@keyframes animate-eyes {
  46% {
    height: 52px;
  }
  48% {
    height: 20px;
  }
  50% {
    height: 52px;
  }
  96% {
    height: 52px;
  }
  98% {
    height: 20px;
  }
  100% {
    height: 52px;
  }
}

/* 3D Hover Effects for AI Assistant */
.area:nth-child(15):hover ~ .container-wrap .card,
.area:nth-child(15):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(10deg)
    translateZ(var(--translateY)) scale3d(1.05, 1.05, 1.05);
}
.area:nth-child(14):hover ~ .container-wrap .card,
.area:nth-child(14):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(5deg)
    translateZ(var(--translateY)) scale3d(1.02, 1.02, 1.02);
}
.area:nth-child(13):hover ~ .container-wrap .card,
.area:nth-child(13):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(0)
    translateZ(var(--translateY)) scale3d(1.02, 1.02, 1.02);
}
.area:nth-child(12):hover ~ .container-wrap .card,
.area:nth-child(12):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(-5deg)
    translateZ(var(--translateY)) scale3d(1.02, 1.02, 1.02);
}
.area:nth-child(11):hover ~ .container-wrap .card,
.area:nth-child(11):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(-10deg)
    translateZ(var(--translateY)) scale3d(1.05, 1.05, 1.05);
}

.area:nth-child(10):hover ~ .container-wrap .card,
.area:nth-child(10):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(0) rotateY(15deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(9):hover ~ .container-wrap .card,
.area:nth-child(9):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(0) rotateY(7deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(8):hover ~ .container-wrap .card,
.area:nth-child(8):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(0) rotateY(0)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(7):hover ~ .container-wrap .card,
.area:nth-child(7):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(0) rotateY(-7deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(6):hover ~ .container-wrap .card,
.area:nth-child(6):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(0) rotateY(-15deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}

.area:nth-child(5):hover ~ .container-wrap .card,
.area:nth-child(5):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(15deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(4):hover ~ .container-wrap .card,
.area:nth-child(4):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(7deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(3):hover ~ .container-wrap .card,
.area:nth-child(3):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(0)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(2):hover ~ .container-wrap .card,
.area:nth-child(2):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(-7deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}
.area:nth-child(1):hover ~ .container-wrap .card,
.area:nth-child(1):hover ~ .container-wrap .eyes .eye {
  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(-15deg)
    translateZ(var(--translateY)) scale3d(1, 1, 1);
}

/* AI Assistant Menu Styles */
.ai-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: linear-gradient(135deg, rgba(10, 14, 41, 0.95), rgba(0, 30, 60, 0.9));
  backdrop-filter: blur(15px);
  border-radius: 12px;
  padding: 0.5rem;
  min-width: 120px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 238, 255, 0.3);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-20px) scale(0.9);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  max-height: 0;
  overflow: hidden;
}

.ai-menu.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  max-height: 200px;
}

.ai-menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
  white-space: nowrap;
  border: 1px solid transparent;
}

.ai-menu-item:hover {
  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(0, 123, 255, 0.1));
  color: rgba(0, 238, 255, 1);
  border-color: rgba(0, 238, 255, 0.4);
  transform: translateX(2px);
}

.ai-menu-item svg {
  width: 12px;
  height: 12px;
}

/* Text Input Styles for JARVIS */
.input {
  color: #fff;
  font-size: 0.9rem;
  background-color: transparent;
  width: 100%;
  box-sizing: border-box;
  padding-inline: 0.5em;
  padding-block: 0.7em;
  border: none;
  border-bottom: var(--border-height) solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

/* Chat Screen Specific Input Styles */
.input:focus {
  box-shadow: 0 0 10px rgba(0, 238, 255, 0.3);
  transform: scale(1.02);
}

.input-border {
  position: absolute;
  background: var(--border-after-color);
  width: 0%;
  height: 2px;
  bottom: 6px;
  left: 8px;
  border-radius: 0 0 2px 2px;
  transition: width 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045);
  z-index: 1;
  max-width: calc(100% - 16px);
}

.input:focus {
  outline: none;
}

.input:focus + .input-border {
  width: calc(100% - 16px);
}

.form-control {
  position: relative;
  --width-of-input: 300px;
  --width-of-input-expanded: 450px;
  --border-height: 2px;
  --border-before-color: rgba(221, 221, 221, 0.39);
  --border-after-color: linear-gradient(90deg, #FF6464 0%, #FFBF59 50%, #47C9FF 100%);
  width: var(--width-of-input);
  margin-top: 2rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  padding: 4px;
  overflow: hidden;

  /* Glow effect */
  box-shadow:
    0 0 20px rgba(0, 238, 255, 0.3),
    0 0 40px rgba(0, 238, 255, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.2);

  /* Subtle background glow */
  background: linear-gradient(135deg,
    rgba(0, 238, 255, 0.05) 0%,
    rgba(255, 0, 170, 0.05) 50%,
    rgba(71, 201, 255, 0.05) 100%);

  /* Animated border glow */
  border: 1px solid rgba(0, 238, 255, 0.3);
}

.form-control:hover {
  box-shadow:
    0 0 30px rgba(0, 238, 255, 0.4),
    0 0 60px rgba(0, 238, 255, 0.2),
    0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 238, 255, 0.5);
}

.form-control.focused {
  width: var(--width-of-input-expanded);
  box-shadow:
    0 0 40px rgba(0, 238, 255, 0.6),
    0 0 80px rgba(0, 238, 255, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 238, 255, 0.8);
  transform: scale(1.02);
}

.form-control::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    #FF6464, #FFBF59, #47C9FF, #00eeff, #ff00aa);
  background-size: 400% 400%;
  border-radius: 10px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: gradientShift 3s ease infinite;
}

.form-control.focused::before {
  opacity: 0.3;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes blinkingCircle {
  0% {
    opacity: 0.6;
    transform: scale(1);
    box-shadow: 0 0 20px currentColor;
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
    box-shadow: 0 0 30px currentColor;
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
    box-shadow: 0 0 20px currentColor;
  }
}

.input-alt {
  font-size: 1.2rem;
  padding-inline: 1em;
  padding-block: 0.8em;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.input-border-alt {
  height: 3px;
  background: linear-gradient(90deg, #FF6464 0%, #FFBF59 50%, #47C9FF 100%);
  transition: width 0.4s cubic-bezier(0.42, 0, 0.58, 1.00);
}

.input-alt:focus + .input-border-alt {
  width: 100%;
}