{"ast": null, "code": "import { frame, cancelFrame } from '../../../frameloop/frame.mjs';\nfunction observeTimeline(update, timeline) {\n  let prevProgress;\n  const onFrame = () => {\n    const {\n      currentTime\n    } = timeline;\n    const percentage = currentTime === null ? 0 : currentTime.value;\n    const progress = percentage / 100;\n    if (prevProgress !== progress) {\n      update(progress);\n    }\n    prevProgress = progress;\n  };\n  frame.update(onFrame, true);\n  return () => cancelFrame(onFrame);\n}\nexport { observeTimeline };", "map": {"version": 3, "names": ["frame", "cancelFrame", "observeTimeline", "update", "timeline", "prevProgress", "onFrame", "currentTime", "percentage", "value", "progress"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs"], "sourcesContent": ["import { frame, cancelFrame } from '../../../frameloop/frame.mjs';\n\nfunction observeTimeline(update, timeline) {\n    let prevProgress;\n    const onFrame = () => {\n        const { currentTime } = timeline;\n        const percentage = currentTime === null ? 0 : currentTime.value;\n        const progress = percentage / 100;\n        if (prevProgress !== progress) {\n            update(progress);\n        }\n        prevProgress = progress;\n    };\n    frame.update(onFrame, true);\n    return () => cancelFrame(onFrame);\n}\n\nexport { observeTimeline };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,8BAA8B;AAEjE,SAASC,eAAeA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACvC,IAAIC,YAAY;EAChB,MAAMC,OAAO,GAAGA,CAAA,KAAM;IAClB,MAAM;MAAEC;IAAY,CAAC,GAAGH,QAAQ;IAChC,MAAMI,UAAU,GAAGD,WAAW,KAAK,IAAI,GAAG,CAAC,GAAGA,WAAW,CAACE,KAAK;IAC/D,MAAMC,QAAQ,GAAGF,UAAU,GAAG,GAAG;IACjC,IAAIH,YAAY,KAAKK,QAAQ,EAAE;MAC3BP,MAAM,CAACO,QAAQ,CAAC;IACpB;IACAL,YAAY,GAAGK,QAAQ;EAC3B,CAAC;EACDV,KAAK,CAACG,MAAM,CAACG,OAAO,EAAE,IAAI,CAAC;EAC3B,OAAO,MAAML,WAAW,CAACK,OAAO,CAAC;AACrC;AAEA,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}