const { app, BrowserWindow, ipcMain, desktopCapturer } = require('electron')
const path = require('path')
const { spawn } = require('child_process')
const fs = require('fs')

let mainWindow = null;
let backendProcess = null;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    transparent: true,
    frame: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false // Allow access to system audio
    },
    backgroundColor: '#00000000',
    icon: path.join(__dirname, 'assets/icon.png'), // Add app icon if available
    show: false // Don't show until ready
  })

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  // Always load the built React app for Electron
  const startUrl = `file://${path.join(__dirname, '../build/index.html')}`
  console.log('Loading URL:', startUrl)
  console.log('Build path exists:', require('fs').existsSync(path.join(__dirname, '../build/index.html')))

  mainWindow.loadURL(startUrl)

  // Always open dev tools to debug issues
  mainWindow.webContents.openDevTools()

  // Add error handling for loading
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', errorCode, errorDescription, validatedURL)
  })

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page loaded successfully')
  })

  // Handle system audio capture requests
  setupSystemAudioCapture();

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
    stopBackendProcess()
  })
}

// System Audio Capture Setup
function setupSystemAudioCapture() {
  // Handle requests for system audio sources
  ipcMain.handle('get-system-audio-sources', async () => {
    try {
      const sources = await desktopCapturer.getSources({
        types: ['screen', 'window'],
        fetchWindowIcons: false
      });

      // Return audio-capable sources
      return sources.map(source => ({
        id: source.id,
        name: source.name,
        display_id: source.display_id
      }));
    } catch (error) {
      console.error('Failed to get system audio sources:', error);
      return [];
    }
  });

  // Handle system audio stream requests
  ipcMain.handle('get-system-audio-stream', async (event, sourceId) => {
    try {
      // This will be handled by the renderer process
      // We just need to provide the source ID
      return { success: true, sourceId };
    } catch (error) {
      console.error('Failed to setup system audio stream:', error);
      return { success: false, error: error.message };
    }
  });
}

// Backend Process Management
function startBackendProcess() {
  const backendPath = path.join(__dirname, '../../Backend')
  const pythonScript = path.join(backendPath, 'api_server.py')

  // Check if backend script exists
  if (!fs.existsSync(pythonScript)) {
    console.error('Backend script not found:', pythonScript)
    return false
  }

  console.log('Starting Jarvis backend process...')

  try {
    backendProcess = spawn('python', [pythonScript], {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe']
    })

    backendProcess.stdout.on('data', (data) => {
      console.log('Backend:', data.toString())
    })

    backendProcess.stderr.on('data', (data) => {
      console.error('Backend Error:', data.toString())
    })

    backendProcess.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`)
      backendProcess = null
    })

    backendProcess.on('error', (error) => {
      console.error('Failed to start backend process:', error)
      backendProcess = null
    })

    return true
  } catch (error) {
    console.error('Error starting backend:', error)
    return false
  }
}

function stopBackendProcess() {
  if (backendProcess) {
    console.log('Stopping backend process...')
    backendProcess.kill('SIGTERM')
    backendProcess = null
  }
}

// App Event Handlers
app.whenReady().then(() => {
  // Start backend process first
  const backendStarted = startBackendProcess()

  if (backendStarted) {
    // Wait a moment for backend to start, then create window
    setTimeout(createWindow, 3000)
  } else {
    // Create window anyway, but backend won't be available
    createWindow()
  }
})

app.on('window-all-closed', () => {
  stopBackendProcess()
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.on('before-quit', () => {
  stopBackendProcess()
})