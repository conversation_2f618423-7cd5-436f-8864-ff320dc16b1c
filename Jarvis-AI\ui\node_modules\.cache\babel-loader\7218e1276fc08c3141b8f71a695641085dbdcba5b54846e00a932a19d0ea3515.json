{"ast": null, "code": "import { resolveElements } from '../utils/resolve-element.mjs';\nconst thresholds = {\n  some: 0,\n  all: 1\n};\nfunction inView(elementOrSelector, onStart, {\n  root,\n  margin: rootMargin,\n  amount = \"some\"\n} = {}) {\n  const elements = resolveElements(elementOrSelector);\n  const activeIntersections = new WeakMap();\n  const onIntersectionChange = entries => {\n    entries.forEach(entry => {\n      const onEnd = activeIntersections.get(entry.target);\n      /**\n       * If there's no change to the intersection, we don't need to\n       * do anything here.\n       */\n      if (entry.isIntersecting === Boolean(onEnd)) return;\n      if (entry.isIntersecting) {\n        const newOnEnd = onStart(entry);\n        if (typeof newOnEnd === \"function\") {\n          activeIntersections.set(entry.target, newOnEnd);\n        } else {\n          observer.unobserve(entry.target);\n        }\n      } else if (onEnd) {\n        onEnd(entry);\n        activeIntersections.delete(entry.target);\n      }\n    });\n  };\n  const observer = new IntersectionObserver(onIntersectionChange, {\n    root,\n    rootMargin,\n    threshold: typeof amount === \"number\" ? amount : thresholds[amount]\n  });\n  elements.forEach(element => observer.observe(element));\n  return () => observer.disconnect();\n}\nexport { inView };", "map": {"version": 3, "names": ["resolveElements", "thresholds", "some", "all", "inView", "elementOrSelector", "onStart", "root", "margin", "rootMargin", "amount", "elements", "activeIntersections", "WeakMap", "onIntersectionChange", "entries", "for<PERSON>ach", "entry", "onEnd", "get", "target", "isIntersecting", "Boolean", "newOnEnd", "set", "observer", "unobserve", "delete", "IntersectionObserver", "threshold", "element", "observe", "disconnect"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs"], "sourcesContent": ["import { resolveElements } from '../utils/resolve-element.mjs';\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = resolveElements(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (onEnd) {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\nexport { inView };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,8BAA8B;AAE9D,MAAMC,UAAU,GAAG;EACfC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE;AACT,CAAC;AACD,SAASC,MAAMA,CAACC,iBAAiB,EAAEC,OAAO,EAAE;EAAEC,IAAI;EAAEC,MAAM,EAAEC,UAAU;EAAEC,MAAM,GAAG;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5F,MAAMC,QAAQ,GAAGX,eAAe,CAACK,iBAAiB,CAAC;EACnD,MAAMO,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;EACzC,MAAMC,oBAAoB,GAAIC,OAAO,IAAK;IACtCA,OAAO,CAACC,OAAO,CAAEC,KAAK,IAAK;MACvB,MAAMC,KAAK,GAAGN,mBAAmB,CAACO,GAAG,CAACF,KAAK,CAACG,MAAM,CAAC;MACnD;AACZ;AACA;AACA;MACY,IAAIH,KAAK,CAACI,cAAc,KAAKC,OAAO,CAACJ,KAAK,CAAC,EACvC;MACJ,IAAID,KAAK,CAACI,cAAc,EAAE;QACtB,MAAME,QAAQ,GAAGjB,OAAO,CAACW,KAAK,CAAC;QAC/B,IAAI,OAAOM,QAAQ,KAAK,UAAU,EAAE;UAChCX,mBAAmB,CAACY,GAAG,CAACP,KAAK,CAACG,MAAM,EAAEG,QAAQ,CAAC;QACnD,CAAC,MACI;UACDE,QAAQ,CAACC,SAAS,CAACT,KAAK,CAACG,MAAM,CAAC;QACpC;MACJ,CAAC,MACI,IAAIF,KAAK,EAAE;QACZA,KAAK,CAACD,KAAK,CAAC;QACZL,mBAAmB,CAACe,MAAM,CAACV,KAAK,CAACG,MAAM,CAAC;MAC5C;IACJ,CAAC,CAAC;EACN,CAAC;EACD,MAAMK,QAAQ,GAAG,IAAIG,oBAAoB,CAACd,oBAAoB,EAAE;IAC5DP,IAAI;IACJE,UAAU;IACVoB,SAAS,EAAE,OAAOnB,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGT,UAAU,CAACS,MAAM;EACtE,CAAC,CAAC;EACFC,QAAQ,CAACK,OAAO,CAAEc,OAAO,IAAKL,QAAQ,CAACM,OAAO,CAACD,OAAO,CAAC,CAAC;EACxD,OAAO,MAAML,QAAQ,CAACO,UAAU,CAAC,CAAC;AACtC;AAEA,SAAS5B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}