{"ast": null, "code": "import{useState,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import ExpandableSidebar from'./ui/ExpandableSidebar';import{useJarvis}from'../hooks/useJarvis';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Jarvis=_ref=>{let{onExitToHome,onEnterChat}=_ref;// Local UI state\nconst[micActive,setMicActive]=useState(false);const[inputText,setInputText]=useState('');const[isInputFocused,setIsInputFocused]=useState(false);// Jarvis API integration\nconst{state,isProcessing,isServerConnected,error,updateState,sendMessage,startSpeechRecognition,speakText,retryConnection}=useJarvis();// Handle mic toggle with state switching and speech recognition\nconst handleMicToggle=async checked=>{setMicActive(checked);if(checked){// Turn on mic -> start speech recognition\ntry{await updateState('listening');const response=await startSpeechRecognition();// If speech was recognized and processed, navigate to chat\nif(response&&response.response){onEnterChat(response.response);}}catch(error){console.error('Speech recognition failed:',error);setMicActive(false);await updateState('rest');}}else{// Turn off mic -> return to rest mode\nif(state==='listening'){await updateState('rest');}}};// Animation variants\nconst containerVariants={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.1}}};const itemVariants={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:0.5}}};// State content configuration\nconst stateConfig={startup:{media:'startup.gif',title:'SYSTEM INITIALIZATION',description:'J.A.R.V.I.S. ONLINE',color:'#00eeff',loop:true},rest:{media:'rest.mp4',title:'STANDBY MODE',description:'AWAITING COMMAND',color:'#00eeff',loop:true},listening:{media:'listening.gif',title:'LISTENING',description:'PROCESSING AUDIO INPUT',color:'#ff00aa',loop:true},thinking:{media:'thinking.gif',title:'PROCESSING',description:'ANALYZING REQUEST',color:'#ff9900',loop:true},speaking:{media:'speaking.gif',title:'RESPONDING',description:'OUTPUT GENERATION',color:'#00ff88',loop:true}};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-4 right-4 z-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 px-3 py-2 rounded-lg \".concat(isServerConnected?'bg-green-900/50 border border-green-500/30':'bg-red-900/50 border border-red-500/30'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 rounded-full \".concat(isServerConnected?'bg-green-400':'bg-red-400')}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs \".concat(isServerConnected?'text-green-300':'text-red-300'),children:isServerConnected?'Backend Connected':'Backend Disconnected'}),!isServerConnected&&/*#__PURE__*/_jsx(\"button\",{onClick:retryConnection,className:\"text-xs text-red-300 hover:text-red-100 underline ml-2\",children:\"Retry\"})]})}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-16 right-4 z-20 max-w-sm\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-900/80 border border-red-500/50 rounded-lg p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-2\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-red-400 flex-shrink-0 mt-0.5\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",clipRule:\"evenodd\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-red-300\",children:error})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",children:/*#__PURE__*/_jsx(ExpandableSidebar,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center space-y-8\",children:[/*#__PURE__*/_jsxs(motion.div,{className:\"relative w-96 h-96 rounded-full overflow-hidden\",children:[/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:1.05},transition:{duration:0.6,ease:[0.4,0,0.2,1],opacity:{duration:0.4}},className:\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\",children:stateConfig[state].media.endsWith('.mp4')?/*#__PURE__*/_jsx(\"video\",{src:\"/assets/\".concat(stateConfig[state].media),autoPlay:true,muted:true,loop:true,playsInline:true,className:\"w-full h-full object-cover rounded-full\",style:{clipPath:'circle(50% at 50% 50%)'}}):/*#__PURE__*/_jsx(\"img\",{src:\"/assets/\".concat(stateConfig[state].media),alt:state,className:\"w-full h-full object-cover rounded-full \".concat(state==='thinking'?'transform translate-x-1 translate-y-1':''),style:{clipPath:'circle(50% at 50% 50%)',imageRendering:'auto',filter:'contrast(1.1) brightness(1.05)',transition:'all 0.3s ease-in-out'}})},state)}),/*#__PURE__*/_jsx(motion.div,{className:\"absolute inset-0 rounded-full border-4 pointer-events-none\",style:{borderColor:stateConfig[state].color},animate:{opacity:[0.3,0.8,0.3],scale:[1,1.1,1],boxShadow:\"0 0 20px \".concat(stateConfig[state].color,\"60\")},transition:{duration:2,repeat:Infinity}})]}),/*#__PURE__*/_jsxs(motion.div,{className:\"text-center space-y-4\",variants:containerVariants,initial:\"hidden\",animate:\"visible\",children:[/*#__PURE__*/_jsx(motion.h1,{className:\"text-4xl font-bold tracking-tighter\",style:{color:stateConfig[state].color},variants:itemVariants,children:stateConfig[state].title}),/*#__PURE__*/_jsx(motion.p,{className:\"text-xl text-cyan-200 font-light\",variants:itemVariants,children:stateConfig[state].description})]}),/*#__PURE__*/_jsx(motion.div,{className:\"mt-12\",variants:itemVariants,initial:\"hidden\",animate:\"visible\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:micActive,onChange:e=>handleMicToggle(e.target.checked)}),/*#__PURE__*/_jsxs(\"div\",{className:\"checkmark\",children:[/*#__PURE__*/_jsxs(\"svg\",{className:\"icon No\",viewBox:\"0 0 24 24\",fill:\"#dc6b6b\",style:{color:'#dc6b6b'},children:[/*#__PURE__*/_jsx(\"path\",{d:\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"6\",y1:\"4\",x2:\"18\",y2:\"20\",stroke:\"#dc6b6b\",strokeWidth:\"2.5\",strokeLinecap:\"round\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"name No\",children:\"Mic Off\"}),/*#__PURE__*/_jsxs(\"svg\",{className:\"icon Yes\",viewBox:\"0 0 24 24\",fill:\"currentColor\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"}),/*#__PURE__*/_jsxs(\"circle\",{cx:\"12\",cy:\"8\",r:\"1\",opacity:\"0.6\",children:[/*#__PURE__*/_jsx(\"animate\",{attributeName:\"r\",values:\"1;2;1\",dur:\"1s\",repeatCount:\"indefinite\"}),/*#__PURE__*/_jsx(\"animate\",{attributeName:\"opacity\",values:\"0.6;0.2;0.6\",dur:\"1s\",repeatCount:\"indefinite\"})]}),/*#__PURE__*/_jsxs(\"circle\",{cx:\"12\",cy:\"8\",r:\"3\",opacity:\"0.3\",children:[/*#__PURE__*/_jsx(\"animate\",{attributeName:\"r\",values:\"3;4;3\",dur:\"1.5s\",repeatCount:\"indefinite\"}),/*#__PURE__*/_jsx(\"animate\",{attributeName:\"opacity\",values:\"0.3;0.1;0.3\",dur:\"1.5s\",repeatCount:\"indefinite\"})]})]}),/*#__PURE__*/_jsx(\"span\",{className:\"name Yes\",children:\"Listening\"})]})]})}),/*#__PURE__*/_jsx(motion.div,{className:\"flex justify-center\",variants:itemVariants,initial:\"hidden\",animate:\"visible\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-control \".concat(isInputFocused?'focused':''),children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"input\",placeholder:\"Type something intelligent (Press Enter to chat)\",value:inputText,onChange:e=>setInputText(e.target.value),onFocus:()=>setIsInputFocused(true),onBlur:()=>setIsInputFocused(false),onKeyDown:async e=>{if(e.key==='Enter'&&inputText.trim()){// Handle text input submission - send to backend and open chat screen\nconst message=inputText.trim();setInputText('');try{const response=await sendMessage(message);onEnterChat(message);}catch(error){console.error('Failed to send message:',error);// Still navigate to chat even if backend fails\nonEnterChat(message);}}}}),/*#__PURE__*/_jsx(\"div\",{className:\"input-border\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-6 left-6\",children:/*#__PURE__*/_jsxs(motion.div,{className:\"menu\",variants:containerVariants,initial:\"hidden\",animate:\"visible\",children:[Object.keys(stateConfig).map(s=>/*#__PURE__*/_jsxs(motion.button,{className:\"link \".concat(state===s?'active':''),style:{backgroundColor:state===s?stateConfig[s].color+'20':'transparent',border:state===s?\"1px solid \".concat(stateConfig[s].color,\"70\"):'1px solid transparent'},whileHover:{scale:1.05,boxShadow:\"0 0 12px \".concat(stateConfig[s].color,\"50\")},whileTap:{scale:0.95},onClick:()=>updateState(s),variants:itemVariants,title:s.toUpperCase(),children:[/*#__PURE__*/_jsx(\"div\",{className:\"link-icon\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",style:{backgroundColor:state===s?stateConfig[s].color:'rgba(0, 238, 255, 0.6)',color:'white',fontSize:'10px'},children:s.charAt(0).toUpperCase()})}),/*#__PURE__*/_jsx(\"span\",{className:\"link-title\",children:s.charAt(0).toUpperCase()+s.slice(1)})]},s)),onExitToHome&&/*#__PURE__*/_jsxs(motion.button,{className:\"link\",style:{backgroundColor:'transparent',border:'1px solid rgba(255, 255, 255, 0.3)'},whileHover:{scale:1.05,boxShadow:'0 0 12px rgba(255, 255, 255, 0.5)'},whileTap:{scale:0.95},onClick:onExitToHome,variants:itemVariants,title:\"HOME\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"link-icon\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",style:{backgroundColor:'rgba(255, 255, 255, 0.6)',color:'black',fontSize:'10px'},children:\"H\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"link-title\",children:\"Home\"})]})]})})]});};export default Jarvis;", "map": {"version": 3, "names": ["useState", "useEffect", "motion", "AnimatePresence", "ExpandableSidebar", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON>", "_ref", "onExitToHome", "onEnterChat", "micActive", "setMicActive", "inputText", "setInputText", "isInputFocused", "setIsInputFocused", "state", "isProcessing", "isServerConnected", "error", "updateState", "sendMessage", "startSpeechRecognition", "speakText", "retryConnection", "handleMicToggle", "checked", "response", "console", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "stateConfig", "startup", "media", "title", "description", "color", "loop", "rest", "listening", "thinking", "speaking", "className", "children", "concat", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "div", "mode", "initial", "scale", "animate", "exit", "ease", "endsWith", "src", "autoPlay", "muted", "playsInline", "style", "clipPath", "alt", "imageRendering", "filter", "borderColor", "boxShadow", "repeat", "Infinity", "variants", "h1", "p", "type", "onChange", "e", "target", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "strokeLinecap", "cx", "cy", "r", "attributeName", "values", "dur", "repeatCount", "placeholder", "value", "onFocus", "onBlur", "onKeyDown", "key", "trim", "message", "Object", "keys", "map", "s", "button", "backgroundColor", "border", "whileHover", "whileTap", "toUpperCase", "fontSize", "char<PERSON>t", "slice"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/Jarvis.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport { useJarvis } from '../hooks/useJarvis';\n\nconst Jarvis = ({ onExitToHome, onEnterChat }) => {\n  // Local UI state\n  const [micActive, setMicActive] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n\n  // Jarvis API integration\n  const {\n    state,\n    isProcessing,\n    isServerConnected,\n    error,\n    updateState,\n    sendMessage,\n    startSpeechRecognition,\n    speakText,\n    retryConnection\n  } = useJarvis();\n\n\n  // Handle mic toggle with state switching and speech recognition\n  const handleMicToggle = async (checked) => {\n    setMicActive(checked);\n\n    if (checked) {\n      // Turn on mic -> start speech recognition\n      try {\n        await updateState('listening');\n        const response = await startSpeechRecognition();\n\n        // If speech was recognized and processed, navigate to chat\n        if (response && response.response) {\n          onEnterChat(response.response);\n        }\n      } catch (error) {\n        console.error('Speech recognition failed:', error);\n        setMicActive(false);\n        await updateState('rest');\n      }\n    } else {\n      // Turn off mic -> return to rest mode\n      if (state === 'listening') {\n        await updateState('rest');\n      }\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: { staggerChildren: 0.1 }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  // State content configuration\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      loop: true\n    },\n    rest: {\n      media: 'rest.mp4',\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      loop: true\n    },\n    listening: {\n      media: 'listening.gif',\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      loop: true\n    },\n    thinking: {\n      media: 'thinking.gif',\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      loop: true\n    },\n    speaking: {\n      media: 'speaking.gif',\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      loop: true\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\">\n\n      {/* Connection Status Indicator */}\n      <div className=\"fixed top-4 right-4 z-20\">\n        <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${isServerConnected\n            ? 'bg-green-900/50 border border-green-500/30'\n            : 'bg-red-900/50 border border-red-500/30'\n          }`}>\n          <div className={`w-2 h-2 rounded-full ${isServerConnected ? 'bg-green-400' : 'bg-red-400'\n            }`} />\n          <span className={`text-xs ${isServerConnected ? 'text-green-300' : 'text-red-300'\n            }`}>\n            {isServerConnected ? 'Backend Connected' : 'Backend Disconnected'}\n          </span>\n          {!isServerConnected && (\n            <button\n              onClick={retryConnection}\n              className=\"text-xs text-red-300 hover:text-red-100 underline ml-2\"\n            >\n              Retry\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"fixed top-16 right-4 z-20 max-w-sm\">\n          <div className=\"bg-red-900/80 border border-red-500/50 rounded-lg p-3\">\n            <div className=\"flex items-start space-x-2\">\n              <svg className=\"w-5 h-5 text-red-400 flex-shrink-0 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n              <div>\n                <p className=\"text-sm text-red-300\">{error}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Centered Media Display */}\n      <div className=\"flex flex-col items-center justify-center space-y-8\">\n        <motion.div\n          className=\"relative w-96 h-96 rounded-full overflow-hidden\"\n        >\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={state}\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 1.05 }}\n              transition={{\n                duration: 0.6,\n                ease: [0.4, 0, 0.2, 1],\n                opacity: { duration: 0.4 }\n              }}\n              className=\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\"\n            >\n              {stateConfig[state].media.endsWith('.mp4') ? (\n                <video\n                  src={`/assets/${stateConfig[state].media}`}\n                  autoPlay\n                  muted\n                  loop\n                  playsInline\n                  className=\"w-full h-full object-cover rounded-full\"\n                  style={{ clipPath: 'circle(50% at 50% 50%)' }}\n                />\n              ) : (\n                <img\n                  src={`/assets/${stateConfig[state].media}`}\n                  alt={state}\n                  className={`w-full h-full object-cover rounded-full ${state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''\n                    }`}\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    imageRendering: 'auto',\n                    filter: 'contrast(1.1) brightness(1.05)',\n                    transition: 'all 0.3s ease-in-out'\n                  }}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Pulsing Halo */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full border-4 pointer-events-none\"\n            style={{ borderColor: stateConfig[state].color }}\n            animate={{\n              opacity: [0.3, 0.8, 0.3],\n              scale: [1, 1.1, 1],\n              boxShadow: `0 0 20px ${stateConfig[state].color}60`\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity\n            }}\n          />\n        </motion.div>\n\n        {/* Status Text */}\n        <motion.div\n          className=\"text-center space-y-4\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <motion.h1\n            className=\"text-4xl font-bold tracking-tighter\"\n            style={{\n              color: stateConfig[state].color\n            }}\n            variants={itemVariants}\n          >\n            {stateConfig[state].title}\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl text-cyan-200 font-light\"\n            variants={itemVariants}\n          >\n            {stateConfig[state].description}\n          </motion.p>\n        </motion.div>\n\n        {/* Mic Button - Center Bottom */}\n        <motion.div\n          className=\"mt-12\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <label className=\"container\">\n            <input\n              type=\"checkbox\"\n              checked={micActive}\n              onChange={(e) => handleMicToggle(e.target.checked)}\n            />\n            <div className=\"checkmark\">\n              {/* Mic Off Icon - with vertical cross line */}\n              <svg className=\"icon No\" viewBox=\"0 0 24 24\" fill=\"#dc6b6b\" style={{ color: '#dc6b6b' }}>\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\" />\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\" />\n                {/* Tilted cross line towards right */}\n                <line\n                  x1=\"6\" y1=\"4\"\n                  x2=\"18\" y2=\"20\"\n                  stroke=\"#dc6b6b\"\n                  strokeWidth=\"2.5\"\n                  strokeLinecap=\"round\"\n                />\n              </svg>\n              <span className=\"name No\">Mic Off</span>\n\n              {/* Mic On Icon - active listening */}\n              <svg className=\"icon Yes\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\" />\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\" />\n                {/* Sound waves indicator */}\n                <circle cx=\"12\" cy=\"8\" r=\"1\" opacity=\"0.6\">\n                  <animate attributeName=\"r\" values=\"1;2;1\" dur=\"1s\" repeatCount=\"indefinite\" />\n                  <animate attributeName=\"opacity\" values=\"0.6;0.2;0.6\" dur=\"1s\" repeatCount=\"indefinite\" />\n                </circle>\n                <circle cx=\"12\" cy=\"8\" r=\"3\" opacity=\"0.3\">\n                  <animate attributeName=\"r\" values=\"3;4;3\" dur=\"1.5s\" repeatCount=\"indefinite\" />\n                  <animate attributeName=\"opacity\" values=\"0.3;0.1;0.3\" dur=\"1.5s\" repeatCount=\"indefinite\" />\n                </circle>\n              </svg>\n              <span className=\"name Yes\">Listening</span>\n            </div>\n          </label>\n        </motion.div>\n\n        {/* Text Input - Below Mic Button */}\n        <motion.div\n          className=\"flex justify-center\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>\n            <input\n              type=\"text\"\n              className=\"input\"\n              placeholder=\"Type something intelligent (Press Enter to chat)\"\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onFocus={() => setIsInputFocused(true)}\n              onBlur={() => setIsInputFocused(false)}\n              onKeyDown={async (e) => {\n                if (e.key === 'Enter' && inputText.trim()) {\n                  // Handle text input submission - send to backend and open chat screen\n                  const message = inputText.trim();\n                  setInputText('');\n\n                  try {\n                    const response = await sendMessage(message);\n                    onEnterChat(message);\n                  } catch (error) {\n                    console.error('Failed to send message:', error);\n                    // Still navigate to chat even if backend fails\n                    onEnterChat(message);\n                  }\n                }\n              }}\n            />\n            <div className=\"input-border\"></div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* State Control Menu - Bottom Left */}\n      <div className=\"absolute bottom-6 left-6\">\n        <motion.div\n          className=\"menu\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {Object.keys(stateConfig).map((s) => (\n            <motion.button\n              key={s}\n              className={`link ${state === s ? 'active' : ''}`}\n              style={{\n                backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',\n                border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: `0 0 12px ${stateConfig[s].color}50`\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => updateState(s)}\n              variants={itemVariants}\n              title={s.toUpperCase()}\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',\n                    color: 'white',\n                    fontSize: '10px'\n                  }}\n                >\n                  {s.charAt(0).toUpperCase()}\n                </div>\n              </div>\n              <span className=\"link-title\">\n                {s.charAt(0).toUpperCase() + s.slice(1)}\n              </span>\n            </motion.button>\n          ))}\n\n          {/* Home Button */}\n          {onExitToHome && (\n            <motion.button\n              className=\"link\"\n              style={{\n                backgroundColor: 'transparent',\n                border: '1px solid rgba(255, 255, 255, 0.3)'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={onExitToHome}\n              variants={itemVariants}\n              title=\"HOME\"\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                    color: 'black',\n                    fontSize: '10px'\n                  }}\n                >\n                  H\n                </div>\n              </div>\n              <span className=\"link-title\">Home</span>\n            </motion.button>\n          )}\n        </motion.div>\n      </div>\n\n\n\n    </div>\n  );\n};\n\nexport default Jarvis;"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,MAAO,CAAAC,iBAAiB,KAAM,wBAAwB,CACtD,OAASC,SAAS,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAAmC,IAAlC,CAAEC,YAAY,CAAEC,WAAY,CAAC,CAAAF,IAAA,CAC3C;AACA,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkB,cAAc,CAAEC,iBAAiB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CACJoB,KAAK,CACLC,YAAY,CACZC,iBAAiB,CACjBC,KAAK,CACLC,WAAW,CACXC,WAAW,CACXC,sBAAsB,CACtBC,SAAS,CACTC,eACF,CAAC,CAAGvB,SAAS,CAAC,CAAC,CAGf;AACA,KAAM,CAAAwB,eAAe,CAAG,KAAO,CAAAC,OAAO,EAAK,CACzCf,YAAY,CAACe,OAAO,CAAC,CAErB,GAAIA,OAAO,CAAE,CACX;AACA,GAAI,CACF,KAAM,CAAAN,WAAW,CAAC,WAAW,CAAC,CAC9B,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAL,sBAAsB,CAAC,CAAC,CAE/C;AACA,GAAIK,QAAQ,EAAIA,QAAQ,CAACA,QAAQ,CAAE,CACjClB,WAAW,CAACkB,QAAQ,CAACA,QAAQ,CAAC,CAChC,CACF,CAAE,MAAOR,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDR,YAAY,CAAC,KAAK,CAAC,CACnB,KAAM,CAAAS,WAAW,CAAC,MAAM,CAAC,CAC3B,CACF,CAAC,IAAM,CACL;AACA,GAAIJ,KAAK,GAAK,WAAW,CAAE,CACzB,KAAM,CAAAI,WAAW,CAAC,MAAM,CAAC,CAC3B,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAS,iBAAiB,CAAG,CACxBC,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtBC,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CAAEC,eAAe,CAAE,GAAI,CACrC,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBL,MAAM,CAAE,CAAEM,CAAC,CAAE,EAAE,CAAEL,OAAO,CAAE,CAAE,CAAC,CAC7BC,OAAO,CAAE,CACPI,CAAC,CAAE,CAAC,CACJL,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CAAEI,QAAQ,CAAE,GAAI,CAC9B,CACF,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAG,CAClBC,OAAO,CAAE,CACPC,KAAK,CAAE,aAAa,CACpBC,KAAK,CAAE,uBAAuB,CAC9BC,WAAW,CAAE,qBAAqB,CAClCC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IACR,CAAC,CACDC,IAAI,CAAE,CACJL,KAAK,CAAE,UAAU,CACjBC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,kBAAkB,CAC/BC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IACR,CAAC,CACDE,SAAS,CAAE,CACTN,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAE,WAAW,CAClBC,WAAW,CAAE,wBAAwB,CACrCC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IACR,CAAC,CACDG,QAAQ,CAAE,CACRP,KAAK,CAAE,cAAc,CACrBC,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,mBAAmB,CAChCC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IACR,CAAC,CACDI,QAAQ,CAAE,CACRR,KAAK,CAAE,cAAc,CACrBC,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,mBAAmB,CAChCC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IACR,CACF,CAAC,CAED,mBACEvC,KAAA,QAAK4C,SAAS,CAAC,8EAA8E,CAAAC,QAAA,eAG3F/C,IAAA,QAAK8C,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC7C,KAAA,QAAK4C,SAAS,qDAAAE,MAAA,CAAsDjC,iBAAiB,CAC/E,4CAA4C,CAC5C,wCAAwC,CACzC,CAAAgC,QAAA,eACH/C,IAAA,QAAK8C,SAAS,yBAAAE,MAAA,CAA0BjC,iBAAiB,CAAG,cAAc,CAAG,YAAY,CACpF,CAAE,CAAC,cACRf,IAAA,SAAM8C,SAAS,YAAAE,MAAA,CAAajC,iBAAiB,CAAG,gBAAgB,CAAG,cAAc,CAC5E,CAAAgC,QAAA,CACFhC,iBAAiB,CAAG,mBAAmB,CAAG,sBAAsB,CAC7D,CAAC,CACN,CAACA,iBAAiB,eACjBf,IAAA,WACEiD,OAAO,CAAE5B,eAAgB,CACzByB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,OAED,CAAQ,CACT,EACE,CAAC,CACH,CAAC,CAGL/B,KAAK,eACJhB,IAAA,QAAK8C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD/C,IAAA,QAAK8C,SAAS,CAAC,uDAAuD,CAAAC,QAAA,cACpE7C,KAAA,QAAK4C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC/C,IAAA,QAAK8C,SAAS,CAAC,2CAA2C,CAACI,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAJ,QAAA,cAChG/C,IAAA,SAAMoD,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,yNAAyN,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CACvQ,CAAC,cACNtD,IAAA,QAAA+C,QAAA,cACE/C,IAAA,MAAG8C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAE/B,KAAK,CAAI,CAAC,CAC5C,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,cAGDhB,IAAA,QAAK8C,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnE/C,IAAA,CAACH,iBAAiB,GAAE,CAAC,CAClB,CAAC,cAGNK,KAAA,QAAK4C,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClE7C,KAAA,CAACP,MAAM,CAAC4D,GAAG,EACTT,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAE3D/C,IAAA,CAACJ,eAAe,EAAC4D,IAAI,CAAC,MAAM,CAAAT,QAAA,cAC1B/C,IAAA,CAACL,MAAM,CAAC4D,GAAG,EAETE,OAAO,CAAE,CAAE7B,OAAO,CAAE,CAAC,CAAE8B,KAAK,CAAE,IAAK,CAAE,CACrCC,OAAO,CAAE,CAAE/B,OAAO,CAAE,CAAC,CAAE8B,KAAK,CAAE,CAAE,CAAE,CAClCE,IAAI,CAAE,CAAEhC,OAAO,CAAE,CAAC,CAAE8B,KAAK,CAAE,IAAK,CAAE,CAClC5B,UAAU,CAAE,CACVI,QAAQ,CAAE,GAAG,CACb2B,IAAI,CAAE,CAAC,GAAG,CAAE,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACtBjC,OAAO,CAAE,CAAEM,QAAQ,CAAE,GAAI,CAC3B,CAAE,CACFY,SAAS,CAAC,2FAA2F,CAAAC,QAAA,CAEpGZ,WAAW,CAACtB,KAAK,CAAC,CAACwB,KAAK,CAACyB,QAAQ,CAAC,MAAM,CAAC,cACxC9D,IAAA,UACE+D,GAAG,YAAAf,MAAA,CAAab,WAAW,CAACtB,KAAK,CAAC,CAACwB,KAAK,CAAG,CAC3C2B,QAAQ,MACRC,KAAK,MACLxB,IAAI,MACJyB,WAAW,MACXpB,SAAS,CAAC,yCAAyC,CACnDqB,KAAK,CAAE,CAAEC,QAAQ,CAAE,wBAAyB,CAAE,CAC/C,CAAC,cAEFpE,IAAA,QACE+D,GAAG,YAAAf,MAAA,CAAab,WAAW,CAACtB,KAAK,CAAC,CAACwB,KAAK,CAAG,CAC3CgC,GAAG,CAAExD,KAAM,CACXiC,SAAS,4CAAAE,MAAA,CAA6CnC,KAAK,GAAK,UAAU,CAAG,uCAAuC,CAAG,EAAE,CACpH,CACLsD,KAAK,CAAE,CACLC,QAAQ,CAAE,wBAAwB,CAClCE,cAAc,CAAE,MAAM,CACtBC,MAAM,CAAE,gCAAgC,CACxCzC,UAAU,CAAE,sBACd,CAAE,CACH,CACF,EAlCIjB,KAmCK,CAAC,CACE,CAAC,cAGlBb,IAAA,CAACL,MAAM,CAAC4D,GAAG,EACTT,SAAS,CAAC,4DAA4D,CACtEqB,KAAK,CAAE,CAAEK,WAAW,CAAErC,WAAW,CAACtB,KAAK,CAAC,CAAC2B,KAAM,CAAE,CACjDmB,OAAO,CAAE,CACP/B,OAAO,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACxB8B,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAClBe,SAAS,aAAAzB,MAAA,CAAcb,WAAW,CAACtB,KAAK,CAAC,CAAC2B,KAAK,MACjD,CAAE,CACFV,UAAU,CAAE,CACVI,QAAQ,CAAE,CAAC,CACXwC,MAAM,CAAEC,QACV,CAAE,CACH,CAAC,EACQ,CAAC,cAGbzE,KAAA,CAACP,MAAM,CAAC4D,GAAG,EACTT,SAAS,CAAC,uBAAuB,CACjC8B,QAAQ,CAAElD,iBAAkB,CAC5B+B,OAAO,CAAC,QAAQ,CAChBE,OAAO,CAAC,SAAS,CAAAZ,QAAA,eAEjB/C,IAAA,CAACL,MAAM,CAACkF,EAAE,EACR/B,SAAS,CAAC,qCAAqC,CAC/CqB,KAAK,CAAE,CACL3B,KAAK,CAAEL,WAAW,CAACtB,KAAK,CAAC,CAAC2B,KAC5B,CAAE,CACFoC,QAAQ,CAAE5C,YAAa,CAAAe,QAAA,CAEtBZ,WAAW,CAACtB,KAAK,CAAC,CAACyB,KAAK,CAChB,CAAC,cAEZtC,IAAA,CAACL,MAAM,CAACmF,CAAC,EACPhC,SAAS,CAAC,kCAAkC,CAC5C8B,QAAQ,CAAE5C,YAAa,CAAAe,QAAA,CAEtBZ,WAAW,CAACtB,KAAK,CAAC,CAAC0B,WAAW,CACvB,CAAC,EACD,CAAC,cAGbvC,IAAA,CAACL,MAAM,CAAC4D,GAAG,EACTT,SAAS,CAAC,OAAO,CACjB8B,QAAQ,CAAE5C,YAAa,CACvByB,OAAO,CAAC,QAAQ,CAChBE,OAAO,CAAC,SAAS,CAAAZ,QAAA,cAEjB7C,KAAA,UAAO4C,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC1B/C,IAAA,UACE+E,IAAI,CAAC,UAAU,CACfxD,OAAO,CAAEhB,SAAU,CACnByE,QAAQ,CAAGC,CAAC,EAAK3D,eAAe,CAAC2D,CAAC,CAACC,MAAM,CAAC3D,OAAO,CAAE,CACpD,CAAC,cACFrB,KAAA,QAAK4C,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB7C,KAAA,QAAK4C,SAAS,CAAC,SAAS,CAACK,OAAO,CAAC,WAAW,CAACD,IAAI,CAAC,SAAS,CAACiB,KAAK,CAAE,CAAE3B,KAAK,CAAE,SAAU,CAAE,CAAAO,QAAA,eACtF/C,IAAA,SAAMqD,CAAC,CAAC,8EAA8E,CAAE,CAAC,cACzFrD,IAAA,SAAMqD,CAAC,CAAC,sGAAsG,CAAE,CAAC,cAEjHrD,IAAA,SACEmF,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CACbC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CACfC,MAAM,CAAC,SAAS,CAChBC,WAAW,CAAC,KAAK,CACjBC,aAAa,CAAC,OAAO,CACtB,CAAC,EACC,CAAC,cACNzF,IAAA,SAAM8C,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cAGxC7C,KAAA,QAAK4C,SAAS,CAAC,UAAU,CAACK,OAAO,CAAC,WAAW,CAACD,IAAI,CAAC,cAAc,CAAAH,QAAA,eAC/D/C,IAAA,SAAMqD,CAAC,CAAC,8EAA8E,CAAE,CAAC,cACzFrD,IAAA,SAAMqD,CAAC,CAAC,sGAAsG,CAAE,CAAC,cAEjHnD,KAAA,WAAQwF,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAChE,OAAO,CAAC,KAAK,CAAAmB,QAAA,eACxC/C,IAAA,YAAS6F,aAAa,CAAC,GAAG,CAACC,MAAM,CAAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC,YAAY,CAAE,CAAC,cAC9EhG,IAAA,YAAS6F,aAAa,CAAC,SAAS,CAACC,MAAM,CAAC,aAAa,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC,YAAY,CAAE,CAAC,EACpF,CAAC,cACT9F,KAAA,WAAQwF,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAAChE,OAAO,CAAC,KAAK,CAAAmB,QAAA,eACxC/C,IAAA,YAAS6F,aAAa,CAAC,GAAG,CAACC,MAAM,CAAC,OAAO,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAE,CAAC,cAChFhG,IAAA,YAAS6F,aAAa,CAAC,SAAS,CAACC,MAAM,CAAC,aAAa,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAE,CAAC,EACtF,CAAC,EACN,CAAC,cACNhG,IAAA,SAAM8C,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACxC,CAAC,EACD,CAAC,CACE,CAAC,cAGb/C,IAAA,CAACL,MAAM,CAAC4D,GAAG,EACTT,SAAS,CAAC,qBAAqB,CAC/B8B,QAAQ,CAAE5C,YAAa,CACvByB,OAAO,CAAC,QAAQ,CAChBE,OAAO,CAAC,SAAS,CAAAZ,QAAA,cAEjB7C,KAAA,QAAK4C,SAAS,iBAAAE,MAAA,CAAkBrC,cAAc,CAAG,SAAS,CAAG,EAAE,CAAG,CAAAoC,QAAA,eAChE/C,IAAA,UACE+E,IAAI,CAAC,MAAM,CACXjC,SAAS,CAAC,OAAO,CACjBmD,WAAW,CAAC,kDAAkD,CAC9DC,KAAK,CAAEzF,SAAU,CACjBuE,QAAQ,CAAGC,CAAC,EAAKvE,YAAY,CAACuE,CAAC,CAACC,MAAM,CAACgB,KAAK,CAAE,CAC9CC,OAAO,CAAEA,CAAA,GAAMvF,iBAAiB,CAAC,IAAI,CAAE,CACvCwF,MAAM,CAAEA,CAAA,GAAMxF,iBAAiB,CAAC,KAAK,CAAE,CACvCyF,SAAS,CAAE,KAAO,CAAApB,CAAC,EAAK,CACtB,GAAIA,CAAC,CAACqB,GAAG,GAAK,OAAO,EAAI7F,SAAS,CAAC8F,IAAI,CAAC,CAAC,CAAE,CACzC;AACA,KAAM,CAAAC,OAAO,CAAG/F,SAAS,CAAC8F,IAAI,CAAC,CAAC,CAChC7F,YAAY,CAAC,EAAE,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAN,WAAW,CAACsF,OAAO,CAAC,CAC3ClG,WAAW,CAACkG,OAAO,CAAC,CACtB,CAAE,MAAOxF,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C;AACAV,WAAW,CAACkG,OAAO,CAAC,CACtB,CACF,CACF,CAAE,CACH,CAAC,cACFxG,IAAA,QAAK8C,SAAS,CAAC,cAAc,CAAM,CAAC,EACjC,CAAC,CACI,CAAC,EACV,CAAC,cAGN9C,IAAA,QAAK8C,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC7C,KAAA,CAACP,MAAM,CAAC4D,GAAG,EACTT,SAAS,CAAC,MAAM,CAChB8B,QAAQ,CAAElD,iBAAkB,CAC5B+B,OAAO,CAAC,QAAQ,CAChBE,OAAO,CAAC,SAAS,CAAAZ,QAAA,EAEhB0D,MAAM,CAACC,IAAI,CAACvE,WAAW,CAAC,CAACwE,GAAG,CAAEC,CAAC,eAC9B1G,KAAA,CAACP,MAAM,CAACkH,MAAM,EAEZ/D,SAAS,SAAAE,MAAA,CAAUnC,KAAK,GAAK+F,CAAC,CAAG,QAAQ,CAAG,EAAE,CAAG,CACjDzC,KAAK,CAAE,CACL2C,eAAe,CAAEjG,KAAK,GAAK+F,CAAC,CAAGzE,WAAW,CAACyE,CAAC,CAAC,CAACpE,KAAK,CAAG,IAAI,CAAG,aAAa,CAC1EuE,MAAM,CAAElG,KAAK,GAAK+F,CAAC,cAAA5D,MAAA,CAAgBb,WAAW,CAACyE,CAAC,CAAC,CAACpE,KAAK,OAAO,uBAChE,CAAE,CACFwE,UAAU,CAAE,CACVtD,KAAK,CAAE,IAAI,CACXe,SAAS,aAAAzB,MAAA,CAAcb,WAAW,CAACyE,CAAC,CAAC,CAACpE,KAAK,MAC7C,CAAE,CACFyE,QAAQ,CAAE,CAAEvD,KAAK,CAAE,IAAK,CAAE,CAC1BT,OAAO,CAAEA,CAAA,GAAMhC,WAAW,CAAC2F,CAAC,CAAE,CAC9BhC,QAAQ,CAAE5C,YAAa,CACvBM,KAAK,CAAEsE,CAAC,CAACM,WAAW,CAAC,CAAE,CAAAnE,QAAA,eAEvB/C,IAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB/C,IAAA,QACE8C,SAAS,CAAC,yEAAyE,CACnFqB,KAAK,CAAE,CACL2C,eAAe,CAAEjG,KAAK,GAAK+F,CAAC,CAAGzE,WAAW,CAACyE,CAAC,CAAC,CAACpE,KAAK,CAAG,wBAAwB,CAC9EA,KAAK,CAAE,OAAO,CACd2E,QAAQ,CAAE,MACZ,CAAE,CAAApE,QAAA,CAED6D,CAAC,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,CACvB,CAAC,CACH,CAAC,cACNlH,IAAA,SAAM8C,SAAS,CAAC,YAAY,CAAAC,QAAA,CACzB6D,CAAC,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,CAAGN,CAAC,CAACS,KAAK,CAAC,CAAC,CAAC,CACnC,CAAC,GA7BFT,CA8BQ,CAChB,CAAC,CAGDvG,YAAY,eACXH,KAAA,CAACP,MAAM,CAACkH,MAAM,EACZ/D,SAAS,CAAC,MAAM,CAChBqB,KAAK,CAAE,CACL2C,eAAe,CAAE,aAAa,CAC9BC,MAAM,CAAE,oCACV,CAAE,CACFC,UAAU,CAAE,CACVtD,KAAK,CAAE,IAAI,CACXe,SAAS,CAAE,mCACb,CAAE,CACFwC,QAAQ,CAAE,CAAEvD,KAAK,CAAE,IAAK,CAAE,CAC1BT,OAAO,CAAE5C,YAAa,CACtBuE,QAAQ,CAAE5C,YAAa,CACvBM,KAAK,CAAC,MAAM,CAAAS,QAAA,eAEZ/C,IAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB/C,IAAA,QACE8C,SAAS,CAAC,yEAAyE,CACnFqB,KAAK,CAAE,CACL2C,eAAe,CAAE,0BAA0B,CAC3CtE,KAAK,CAAE,OAAO,CACd2E,QAAQ,CAAE,MACZ,CAAE,CAAApE,QAAA,CACH,GAED,CAAK,CAAC,CACH,CAAC,cACN/C,IAAA,SAAM8C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,EAC3B,CAChB,EACS,CAAC,CACV,CAAC,EAIH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5C,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}