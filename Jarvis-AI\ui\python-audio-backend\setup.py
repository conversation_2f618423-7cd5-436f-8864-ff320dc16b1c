#!/usr/bin/env python3
"""
Setup script for JARVIS Audio Backend
"""

import subprocess
import sys
import os

def install_requirements():
    """Install Python requirements"""
    print("🐍 Installing Python requirements...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Python requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_system_requirements():
    """Check system requirements for audio capture"""
    print("🔍 Checking system requirements...")
    
    # Check if we're on Windows (PyAudio works best on Windows for system audio)
    if sys.platform == "win32":
        print("✅ Windows detected - good for system audio capture")
    else:
        print("⚠️  Non-Windows system - may need additional setup for system audio")
    
    # Check Python version
    if sys.version_info >= (3, 7):
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is supported")
    else:
        print("❌ Python 3.7+ required")
        return False
    
    return True

def main():
    """Main setup function"""
    print("🎵 JARVIS Audio Backend Setup")
    print("=" * 40)
    
    if not check_system_requirements():
        print("❌ System requirements not met")
        return False
    
    if not install_requirements():
        print("❌ Failed to install requirements")
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Enable 'Stereo Mix' in Windows Sound settings (for system audio)")
    print("2. Run: python audio_capture.py")
    print("3. Connect your frontend to ws://localhost:8765")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
