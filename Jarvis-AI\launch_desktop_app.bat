@echo off
echo ========================================
echo    Starting Jarvis AI Desktop App
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js 16 or higher
    pause
    exit /b 1
)

echo ✅ Python and Node.js found
echo.

REM Navigate to UI directory
cd /d "%~dp0ui"

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo ✅ Dependencies ready
echo.

REM Build the React app if build folder doesn't exist
if not exist "build" (
    echo Building React app...
    npm run build
    if errorlevel 1 (
        echo Error: Failed to build React app
        pause
        exit /b 1
    )
)

echo ✅ React app built
echo.

echo 🚀 Launching Jarvis AI Desktop Application...
echo.

REM Start Electron app
npm run electron

pause
