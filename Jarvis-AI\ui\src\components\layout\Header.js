import React from 'react';
import MenuIcon from '../ui/MenuIcon';
import StatusIndicator from '../ui/StatusIndicator';

const Header = ({ 
  title = "JARVIS", 
  status = "idle", 
  onMenuClick,
  showMenu = true,
  showStatus = true 
}) => {
  return (
    <header className="relative w-full p-4 bg-black/20 backdrop-blur-sm">
      {showMenu && (
        <MenuIcon 
          onMenuClick={onMenuClick}
          position="top-left"
          size="small"
        />
      )}
      
      <div className="text-center">
        <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          {title}
        </h1>
        {showStatus && (
          <div className="mt-2 flex justify-center">
            <StatusIndicator status={status} size="medium" />
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
