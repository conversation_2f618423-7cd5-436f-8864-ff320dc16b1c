{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\components\\\\ChatScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport MinimizedJarvis from './ui/MinimizedJarvis';\nimport { useJarvis } from '../hooks/useJarvis';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatScreen = ({\n  onExitToHome,\n  initialMessage = ''\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [showScrollButton, setShowScrollButton] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Jarvis API integration\n  const {\n    state,\n    isProcessing,\n    isServerConnected,\n    error,\n    sendMessage,\n    speakText,\n    conversationHistory,\n    clearHistory\n  } = useJarvis();\n\n  // Add initial message if provided and send to backend\n  useEffect(() => {\n    if (initialMessage.trim()) {\n      const userMessage = {\n        id: Date.now(),\n        text: initialMessage,\n        sender: 'user',\n        timestamp: new Date()\n      };\n      setMessages([userMessage]);\n\n      // Send to backend and get real response\n      const processInitialMessage = async () => {\n        try {\n          setIsTyping(true);\n          const response = await sendMessage(initialMessage);\n          setMessages(prev => [...prev, {\n            id: Date.now() + 1,\n            text: response.response,\n            sender: 'jarvis',\n            timestamp: new Date(response.timestamp)\n          }]);\n        } catch (error) {\n          console.error('Failed to process initial message:', error);\n          setMessages(prev => [...prev, {\n            id: Date.now() + 1,\n            text: \"I'm sorry, I'm having trouble connecting to my backend systems. Please try again.\",\n            sender: 'jarvis',\n            timestamp: new Date()\n          }]);\n        } finally {\n          setIsTyping(false);\n        }\n      };\n      processInitialMessage();\n    }\n  }, [initialMessage, sendMessage]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages, isTyping]);\n\n  // Focus input on mount\n  useEffect(() => {\n    var _inputRef$current;\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();\n  }, []);\n  const handleSendMessage = async () => {\n    if (!inputText.trim() || !isServerConnected) return;\n    const messageText = inputText.trim();\n    const newMessage = {\n      id: Date.now(),\n      text: messageText,\n      sender: 'user',\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, newMessage]);\n    setInputText('');\n\n    // Send to backend\n    try {\n      setIsTyping(true);\n      const response = await sendMessage(messageText);\n      setMessages(prev => [...prev, {\n        id: Date.now() + 1,\n        text: response.response,\n        sender: 'jarvis',\n        timestamp: new Date(response.timestamp)\n      }]);\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      setMessages(prev => [...prev, {\n        id: Date.now() + 1,\n        text: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        sender: 'jarvis',\n        timestamp: new Date()\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Scroll functionality\n  const handleScroll = e => {\n    const {\n      scrollTop,\n      scrollHeight,\n      clientHeight\n    } = e.target;\n    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;\n    setShowScrollButton(!isNearBottom && messages.length > 0);\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre2;\n    (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n\n  // Animation variants\n  const messageVariants = {\n    hidden: {\n      opacity: 0,\n      y: 10,\n      scale: 0.95\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.3\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen bg-gradient-to-br from-gray-900 to-black text-white relative\",\n    children: [!isServerConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900/80 border border-red-500/50 rounded-lg px-4 py-2\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-red-300\",\n          children: \"Backend Disconnected - Chat functionality limited\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",\n      children: /*#__PURE__*/_jsxDEV(ExpandableSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MinimizedJarvis, {\n      onExpand: onExitToHome,\n      isProcessing: isProcessing\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-4 py-3 border-b border-cyan-500/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowDropdown(!showDropdown),\n            className: \"flex items-center space-x-2 text-white hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold\",\n              children: \"JARVIS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M19 9l-7 7-7-7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), showDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-full left-0 mt-1 w-48 bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-filter backdrop-blur-12 rounded-lg shadow-lg border border-cyan-500/30 z-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"py-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\",\n                children: \"New Chat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\",\n                children: \"Chat History\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\",\n                children: \"Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center space-x-2 bg-gradient-to-r from-cyan-600/20 to-blue-600/20 hover:from-cyan-500/30 hover:to-blue-500/30 border border-cyan-500/30 px-3 py-2 rounded-lg transition-all duration-300 text-cyan-100 hover:text-cyan-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Share\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onExitToHome,\n          className: \"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\",\n          title: \"Return to JARVIS\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto relative\",\n      onScroll: handleScroll,\n      ref: messagesContainerRef,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-3xl mx-auto\",\n        children: [messages.length === 0 ?\n        /*#__PURE__*/\n        /* Welcome Message */\n        _jsxDEV(\"div\", {\n          className: \"flex flex-col items-center justify-center h-full text-center py-20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-8 h-8 rounded-full bg-cyan-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-semibold text-gray-300 mb-2\",\n              children: \"How can I help you today?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500\",\n              children: \"I'm JARVIS, your AI assistant. Ask me anything!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-8\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: messages.map(message => /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: messageVariants,\n              initial: \"hidden\",\n              animate: \"visible\",\n              className: `group mb-8 ${message.sender === 'user' ? 'ml-auto' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4 px-4\",\n                children: [message.sender === 'jarvis' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-4 h-4 rounded-full bg-cyan-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex-1 ${message.sender === 'user' ? 'max-w-2xl ml-auto' : 'max-w-2xl'}`,\n                  children: [message.sender === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right mb-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-400\",\n                      children: \"You\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${message.sender === 'user' ? 'bg-gray-700 text-white rounded-2xl px-4 py-3 ml-auto inline-block max-w-fit' : 'text-gray-100'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                      children: message.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `mt-2 ${message.sender === 'user' ? 'text-right' : 'text-left'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: message.timestamp.toLocaleTimeString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this), message.sender === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium\",\n                      children: \"U\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this)\n            }, message.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 10\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-8 px-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-4 h-4 rounded-full bg-cyan-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 max-w-2xl\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-1 py-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.1s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), showScrollButton && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-4 right-4\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: scrollToBottom,\n          className: \"bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow-lg transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `form-control ${isInputFocused ? 'focused' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          ref: inputRef,\n          type: \"text\",\n          className: \"input\",\n          placeholder: \"Type something intelligent (Press Enter to chat)\",\n          value: inputText,\n          onChange: e => setInputText(e.target.value),\n          onFocus: () => setIsInputFocused(true),\n          onBlur: () => setIsInputFocused(false),\n          onKeyDown: handleKeyPress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-border\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatScreen, \"pzok0fnGK0Bu8P17nhamRv7X+G4=\", false, function () {\n  return [useJarvis];\n});\n_c = ChatScreen;\nexport default ChatScreen;\nvar _c;\n$RefreshReg$(_c, \"ChatScreen\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "ExpandableSidebar", "Mini<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ChatScreen", "onExitToHome", "initialMessage", "_s", "messages", "setMessages", "inputText", "setInputText", "isInputFocused", "setIsInputFocused", "isTyping", "setIsTyping", "showDropdown", "setShowDropdown", "showScrollButton", "setShowScrollButton", "messagesEndRef", "inputRef", "messagesContainerRef", "state", "isProcessing", "isServerConnected", "error", "sendMessage", "speakText", "conversationHistory", "clearHistory", "trim", "userMessage", "id", "Date", "now", "text", "sender", "timestamp", "processInitialMessage", "response", "prev", "console", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "_inputRef$current", "focus", "handleSendMessage", "messageText", "newMessage", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "handleScroll", "scrollTop", "scrollHeight", "clientHeight", "target", "isNearBottom", "length", "scrollToBottom", "_messagesEndRef$curre2", "messageVariants", "hidden", "opacity", "y", "scale", "visible", "transition", "duration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onExpand", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "onScroll", "ref", "map", "message", "div", "variants", "initial", "animate", "toLocaleTimeString", "style", "animationDelay", "type", "placeholder", "value", "onChange", "onFocus", "onBlur", "onKeyDown", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ChatScreen.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport MinimizedJarvis from './ui/MinimizedJarvis';\nimport { useJarvis } from '../hooks/useJarvis';\n\nconst ChatScreen = ({ onExitToHome, initialMessage = '' }) => {\n  const [messages, setMessages] = useState([]);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [showScrollButton, setShowScrollButton] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Jarvis API integration\n  const {\n    state,\n    isProcessing,\n    isServerConnected,\n    error,\n    sendMessage,\n    speakText,\n    conversationHistory,\n    clearHistory\n  } = useJarvis();\n\n  // Add initial message if provided and send to backend\n  useEffect(() => {\n    if (initialMessage.trim()) {\n      const userMessage = {\n        id: Date.now(),\n        text: initialMessage,\n        sender: 'user',\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n\n      // Send to backend and get real response\n      const processInitialMessage = async () => {\n        try {\n          setIsTyping(true);\n          const response = await sendMessage(initialMessage);\n\n          setMessages(prev => [...prev, {\n            id: Date.now() + 1,\n            text: response.response,\n            sender: 'jarvis',\n            timestamp: new Date(response.timestamp)\n          }]);\n        } catch (error) {\n          console.error('Failed to process initial message:', error);\n          setMessages(prev => [...prev, {\n            id: Date.now() + 1,\n            text: \"I'm sorry, I'm having trouble connecting to my backend systems. Please try again.\",\n            sender: 'jarvis',\n            timestamp: new Date()\n          }]);\n        } finally {\n          setIsTyping(false);\n        }\n      };\n\n      processInitialMessage();\n    }\n  }, [initialMessage, sendMessage]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, isTyping]);\n\n  // Focus input on mount\n  useEffect(() => {\n    inputRef.current?.focus();\n  }, []);\n\n  const handleSendMessage = async () => {\n    if (!inputText.trim() || !isServerConnected) return;\n\n    const messageText = inputText.trim();\n    const newMessage = {\n      id: Date.now(),\n      text: messageText,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    setInputText('');\n\n    // Send to backend\n    try {\n      setIsTyping(true);\n      const response = await sendMessage(messageText);\n\n      setMessages(prev => [...prev, {\n        id: Date.now() + 1,\n        text: response.response,\n        sender: 'jarvis',\n        timestamp: new Date(response.timestamp)\n      }]);\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      setMessages(prev => [...prev, {\n        id: Date.now() + 1,\n        text: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        sender: 'jarvis',\n        timestamp: new Date()\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Scroll functionality\n  const handleScroll = (e) => {\n    const { scrollTop, scrollHeight, clientHeight } = e.target;\n    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;\n    setShowScrollButton(!isNearBottom && messages.length > 0);\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  // Animation variants\n  const messageVariants = {\n    hidden: { opacity: 0, y: 10, scale: 0.95 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-screen bg-gradient-to-br from-gray-900 to-black text-white relative\">\n      {/* Connection Status Indicator */}\n      {!isServerConnected && (\n        <div className=\"fixed top-4 left-1/2 transform -translate-x-1/2 z-20\">\n          <div className=\"bg-red-900/80 border border-red-500/50 rounded-lg px-4 py-2\">\n            <span className=\"text-sm text-red-300\">Backend Disconnected - Chat functionality limited</span>\n          </div>\n        </div>\n      )}\n\n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Minimized JARVIS - Top Right */}\n      <MinimizedJarvis\n        onExpand={onExitToHome}\n        isProcessing={isProcessing}\n      />\n\n      {/* Header - JARVIS Style */}\n      <div className=\"flex items-center justify-between px-4 py-3 border-b border-cyan-500/20\">\n        {/* Left side - Title with dropdown */}\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowDropdown(!showDropdown)}\n              className=\"flex items-center space-x-2 text-white hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors\"\n            >\n              <span className=\"font-semibold\">JARVIS</span>\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </button>\n\n            {/* Dropdown Menu */}\n            {showDropdown && (\n              <div className=\"absolute top-full left-0 mt-1 w-48 bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-filter backdrop-blur-12 rounded-lg shadow-lg border border-cyan-500/30 z-50\">\n                <div className=\"py-1\">\n                  <button className=\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\">\n                    New Chat\n                  </button>\n                  <button className=\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\">\n                    Chat History\n                  </button>\n                  <button className=\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\">\n                    Settings\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Right side - Share button and menu */}\n        <div className=\"flex items-center space-x-2\">\n          <button className=\"flex items-center space-x-2 bg-gradient-to-r from-cyan-600/20 to-blue-600/20 hover:from-cyan-500/30 hover:to-blue-500/30 border border-cyan-500/30 px-3 py-2 rounded-lg transition-all duration-300 text-cyan-100 hover:text-cyan-400\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\" />\n            </svg>\n            <span className=\"text-sm\">Share</span>\n          </button>\n\n          <button\n            onClick={onExitToHome}\n            className=\"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\"\n            title=\"Return to JARVIS\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n            </svg>\n          </button>\n\n          <button className=\"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\">\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      {/* Messages Container - ChatGPT Style */}\n      <div className=\"flex-1 overflow-y-auto relative\" onScroll={handleScroll} ref={messagesContainerRef}>\n        <div className=\"max-w-3xl mx-auto\">\n          {messages.length === 0 ? (\n            /* Welcome Message */\n            <div className=\"flex flex-col items-center justify-center h-full text-center py-20\">\n              <div className=\"mb-8\">\n                <div className=\"w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center mx-auto mb-4\">\n                  <div className=\"w-8 h-8 rounded-full bg-cyan-400\"></div>\n                </div>\n                <h2 className=\"text-2xl font-semibold text-gray-300 mb-2\">How can I help you today?</h2>\n                <p className=\"text-gray-500\">I'm JARVIS, your AI assistant. Ask me anything!</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"py-8\">\n              <AnimatePresence>\n                {messages.map((message) => (\n                  <motion.div\n                    key={message.id}\n                    variants={messageVariants}\n                    initial=\"hidden\"\n                    animate=\"visible\"\n                    className={`group mb-8 ${message.sender === 'user' ? 'ml-auto' : ''}`}\n                  >\n                    <div className=\"flex items-start space-x-4 px-4\">\n                      {message.sender === 'jarvis' && (\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\">\n                            <div className=\"w-4 h-4 rounded-full bg-cyan-400\"></div>\n                          </div>\n                        </div>\n                      )}\n\n                      <div className={`flex-1 ${message.sender === 'user' ? 'max-w-2xl ml-auto' : 'max-w-2xl'}`}>\n                        {message.sender === 'user' && (\n                          <div className=\"text-right mb-2\">\n                            <span className=\"text-sm text-gray-400\">You</span>\n                          </div>\n                        )}\n\n                        <div className={`${message.sender === 'user'\n                          ? 'bg-gray-700 text-white rounded-2xl px-4 py-3 ml-auto inline-block max-w-fit'\n                          : 'text-gray-100'\n                          }`}>\n                          <p className=\"text-sm leading-relaxed whitespace-pre-wrap\">{message.text}</p>\n                        </div>\n\n                        <div className={`mt-2 ${message.sender === 'user' ? 'text-right' : 'text-left'}`}>\n                          <span className=\"text-xs text-gray-500\">\n                            {message.timestamp.toLocaleTimeString()}\n                          </span>\n                        </div>\n                      </div>\n\n                      {message.sender === 'user' && (\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center\">\n                            <span className=\"text-sm font-medium\">U</span>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n\n              {/* Typing indicator */}\n              {isTyping && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"mb-8 px-4\"\n                >\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\">\n                        <div className=\"w-4 h-4 rounded-full bg-cyan-400\"></div>\n                      </div>\n                    </div>\n                    <div className=\"flex-1 max-w-2xl\">\n                      <div className=\"flex space-x-1 py-2\">\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          )}\n\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Scroll to bottom button */}\n        {showScrollButton && (\n          <div className=\"absolute bottom-4 right-4\">\n            <button\n              onClick={scrollToBottom}\n              className=\"bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow-lg transition-colors\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n              </svg>\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Input Area - JARVIS Style */}\n      <div className=\"flex justify-center p-6\">\n        <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>\n          <input\n            ref={inputRef}\n            type=\"text\"\n            className=\"input\"\n            placeholder=\"Type something intelligent (Press Enter to chat)\"\n            value={inputText}\n            onChange={(e) => setInputText(e.target.value)}\n            onFocus={() => setIsInputFocused(true)}\n            onBlur={() => setIsInputFocused(false)}\n            onKeyDown={handleKeyPress}\n          />\n          <div className=\"input-border\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,SAASC,SAAS,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,UAAU,GAAGA,CAAC;EAAEC,YAAY;EAAEC,cAAc,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC5D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM0B,cAAc,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM0B,QAAQ,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM2B,oBAAoB,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACA,MAAM;IACJ4B,KAAK;IACLC,YAAY;IACZC,iBAAiB;IACjBC,KAAK;IACLC,WAAW;IACXC,SAAS;IACTC,mBAAmB;IACnBC;EACF,CAAC,GAAG7B,SAAS,CAAC,CAAC;;EAEf;EACAL,SAAS,CAAC,MAAM;IACd,IAAIU,cAAc,CAACyB,IAAI,CAAC,CAAC,EAAE;MACzB,MAAMC,WAAW,GAAG;QAClBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE9B,cAAc;QACpB+B,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,IAAIJ,IAAI,CAAC;MACtB,CAAC;MAEDzB,WAAW,CAAC,CAACuB,WAAW,CAAC,CAAC;;MAE1B;MACA,MAAMO,qBAAqB,GAAG,MAAAA,CAAA,KAAY;QACxC,IAAI;UACFxB,WAAW,CAAC,IAAI,CAAC;UACjB,MAAMyB,QAAQ,GAAG,MAAMb,WAAW,CAACrB,cAAc,CAAC;UAElDG,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;YAC5BR,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;YAClBC,IAAI,EAAEI,QAAQ,CAACA,QAAQ;YACvBH,MAAM,EAAE,QAAQ;YAChBC,SAAS,EAAE,IAAIJ,IAAI,CAACM,QAAQ,CAACF,SAAS;UACxC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,OAAOZ,KAAK,EAAE;UACdgB,OAAO,CAAChB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1DjB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;YAC5BR,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;YAClBC,IAAI,EAAE,mFAAmF;YACzFC,MAAM,EAAE,QAAQ;YAChBC,SAAS,EAAE,IAAIJ,IAAI,CAAC;UACtB,CAAC,CAAC,CAAC;QACL,CAAC,SAAS;UACRnB,WAAW,CAAC,KAAK,CAAC;QACpB;MACF,CAAC;MAEDwB,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACjC,cAAc,EAAEqB,WAAW,CAAC,CAAC;;EAEjC;EACA/B,SAAS,CAAC,MAAM;IAAA,IAAA+C,qBAAA;IACd,CAAAA,qBAAA,GAAAvB,cAAc,CAACwB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACtC,QAAQ,EAAEM,QAAQ,CAAC,CAAC;;EAExB;EACAlB,SAAS,CAAC,MAAM;IAAA,IAAAmD,iBAAA;IACd,CAAAA,iBAAA,GAAA1B,QAAQ,CAACuB,OAAO,cAAAG,iBAAA,uBAAhBA,iBAAA,CAAkBC,KAAK,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACvC,SAAS,CAACqB,IAAI,CAAC,CAAC,IAAI,CAACN,iBAAiB,EAAE;IAE7C,MAAMyB,WAAW,GAAGxC,SAAS,CAACqB,IAAI,CAAC,CAAC;IACpC,MAAMoB,UAAU,GAAG;MACjBlB,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAEc,WAAW;MACjBb,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,IAAIJ,IAAI,CAAC;IACtB,CAAC;IAEDzB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEU,UAAU,CAAC,CAAC;IAC1CxC,YAAY,CAAC,EAAE,CAAC;;IAEhB;IACA,IAAI;MACFI,WAAW,CAAC,IAAI,CAAC;MACjB,MAAMyB,QAAQ,GAAG,MAAMb,WAAW,CAACuB,WAAW,CAAC;MAE/CzC,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5BR,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAEI,QAAQ,CAACA,QAAQ;QACvBH,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,IAAIJ,IAAI,CAACM,QAAQ,CAACF,SAAS;MACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdgB,OAAO,CAAChB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CjB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5BR,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,8EAA8E;QACpFC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,IAAIJ,IAAI,CAAC;MACtB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRnB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMqC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBP,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAIJ,CAAC,IAAK;IAC1B,MAAM;MAAEK,SAAS;MAAEC,YAAY;MAAEC;IAAa,CAAC,GAAGP,CAAC,CAACQ,MAAM;IAC1D,MAAMC,YAAY,GAAGH,YAAY,GAAGD,SAAS,GAAGE,YAAY,GAAG,GAAG;IAClEzC,mBAAmB,CAAC,CAAC2C,YAAY,IAAItD,QAAQ,CAACuD,MAAM,GAAG,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA;IAC3B,CAAAA,sBAAA,GAAA7C,cAAc,CAACwB,OAAO,cAAAqB,sBAAA,uBAAtBA,sBAAA,CAAwBpB,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMoB,eAAe,GAAG;IACtBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAC;IAC1CC,OAAO,EAAE;MACPH,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE,CAAC;MACRE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,oBACEtE,OAAA;IAAKuE,SAAS,EAAC,qFAAqF;IAAAC,QAAA,GAEjG,CAAClD,iBAAiB,iBACjBtB,OAAA;MAAKuE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnExE,OAAA;QAAKuE,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1ExE,OAAA;UAAMuE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5E,OAAA;MAAKuE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnExE,OAAA,CAACJ,iBAAiB;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGN5E,OAAA,CAACH,eAAe;MACdgF,QAAQ,EAAE3E,YAAa;MACvBmB,YAAY,EAAEA;IAAa;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGF5E,OAAA;MAAKuE,SAAS,EAAC,yEAAyE;MAAAC,QAAA,gBAEtFxE,OAAA;QAAKuE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CxE,OAAA;UAAKuE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBxE,OAAA;YACE8E,OAAO,EAAEA,CAAA,KAAMhE,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9C0D,SAAS,EAAC,iGAAiG;YAAAC,QAAA,gBAE3GxE,OAAA;cAAMuE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C5E,OAAA;cAAKuE,SAAS,EAAC,SAAS;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAT,QAAA,eAC5ExE,OAAA;gBAAMkF,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAGR/D,YAAY,iBACXb,OAAA;YAAKuE,SAAS,EAAC,wKAAwK;YAAAC,QAAA,eACrLxE,OAAA;cAAKuE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBxE,OAAA;gBAAQuE,SAAS,EAAC,6GAA6G;gBAAAC,QAAA,EAAC;cAEhI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5E,OAAA;gBAAQuE,SAAS,EAAC,6GAA6G;gBAAAC,QAAA,EAAC;cAEhI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5E,OAAA;gBAAQuE,SAAS,EAAC,6GAA6G;gBAAAC,QAAA,EAAC;cAEhI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5E,OAAA;QAAKuE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CxE,OAAA;UAAQuE,SAAS,EAAC,uOAAuO;UAAAC,QAAA,gBACvPxE,OAAA;YAAKuE,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAT,QAAA,eAC5ExE,OAAA;cAAMkF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAuO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5S,CAAC,eACN5E,OAAA;YAAMuE,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAET5E,OAAA;UACE8E,OAAO,EAAE5E,YAAa;UACtBqE,SAAS,EAAC,yFAAyF;UACnGe,KAAK,EAAC,kBAAkB;UAAAd,QAAA,eAExBxE,OAAA;YAAKuE,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAT,QAAA,eAC5ExE,OAAA;cAAMkF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA6B;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAET5E,OAAA;UAAQuE,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eACzGxE,OAAA;YAAKuE,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAT,QAAA,eAC5ExE,OAAA;cAAMkF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAuH;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5L;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5E,OAAA;MAAKuE,SAAS,EAAC,iCAAiC;MAACgB,QAAQ,EAAEjC,YAAa;MAACkC,GAAG,EAAErE,oBAAqB;MAAAqD,QAAA,gBACjGxE,OAAA;QAAKuE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/BnE,QAAQ,CAACuD,MAAM,KAAK,CAAC;QAAA;QACpB;QACA5D,OAAA;UAAKuE,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjFxE,OAAA;YAAKuE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxE,OAAA;cAAKuE,SAAS,EAAC,iHAAiH;cAAAC,QAAA,eAC9HxE,OAAA;gBAAKuE,SAAS,EAAC;cAAkC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN5E,OAAA;cAAIuE,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxF5E,OAAA;cAAGuE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN5E,OAAA;UAAKuE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxE,OAAA,CAACL,eAAe;YAAA6E,QAAA,EACbnE,QAAQ,CAACoF,GAAG,CAAEC,OAAO,iBACpB1F,OAAA,CAACN,MAAM,CAACiG,GAAG;cAETC,QAAQ,EAAE7B,eAAgB;cAC1B8B,OAAO,EAAC,QAAQ;cAChBC,OAAO,EAAC,SAAS;cACjBvB,SAAS,EAAE,cAAcmB,OAAO,CAACxD,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG,EAAE,EAAG;cAAAsC,QAAA,eAEtExE,OAAA;gBAAKuE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,GAC7CkB,OAAO,CAACxD,MAAM,KAAK,QAAQ,iBAC1BlC,OAAA;kBAAKuE,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BxE,OAAA;oBAAKuE,SAAS,EAAC,kGAAkG;oBAAAC,QAAA,eAC/GxE,OAAA;sBAAKuE,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAED5E,OAAA;kBAAKuE,SAAS,EAAE,UAAUmB,OAAO,CAACxD,MAAM,KAAK,MAAM,GAAG,mBAAmB,GAAG,WAAW,EAAG;kBAAAsC,QAAA,GACvFkB,OAAO,CAACxD,MAAM,KAAK,MAAM,iBACxBlC,OAAA;oBAAKuE,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9BxE,OAAA;sBAAMuE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CACN,eAED5E,OAAA;oBAAKuE,SAAS,EAAE,GAAGmB,OAAO,CAACxD,MAAM,KAAK,MAAM,GACxC,6EAA6E,GAC7E,eAAe,EACd;oBAAAsC,QAAA,eACHxE,OAAA;sBAAGuE,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,EAAEkB,OAAO,CAACzD;oBAAI;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eAEN5E,OAAA;oBAAKuE,SAAS,EAAE,QAAQmB,OAAO,CAACxD,MAAM,KAAK,MAAM,GAAG,YAAY,GAAG,WAAW,EAAG;oBAAAsC,QAAA,eAC/ExE,OAAA;sBAAMuE,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCkB,OAAO,CAACvD,SAAS,CAAC4D,kBAAkB,CAAC;oBAAC;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELc,OAAO,CAACxD,MAAM,KAAK,MAAM,iBACxBlC,OAAA;kBAAKuE,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BxE,OAAA;oBAAKuE,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFxE,OAAA;sBAAMuE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GA3CDc,OAAO,CAAC5D,EAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4CL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,EAGjBjE,QAAQ,iBACPX,OAAA,CAACN,MAAM,CAACiG,GAAG;YACTE,OAAO,EAAE;cAAE5B,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/B4B,OAAO,EAAE;cAAE7B,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BK,SAAS,EAAC,WAAW;YAAAC,QAAA,eAErBxE,OAAA;cAAKuE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCxE,OAAA;gBAAKuE,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BxE,OAAA;kBAAKuE,SAAS,EAAC,kGAAkG;kBAAAC,QAAA,eAC/GxE,OAAA;oBAAKuE,SAAS,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5E,OAAA;gBAAKuE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BxE,OAAA;kBAAKuE,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCxE,OAAA;oBAAKuE,SAAS,EAAC;kBAAiD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvE5E,OAAA;oBAAKuE,SAAS,EAAC,iDAAiD;oBAACyB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1G5E,OAAA;oBAAKuE,SAAS,EAAC,iDAAiD;oBAACyB,KAAK,EAAE;sBAAEC,cAAc,EAAE;oBAAO;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAED5E,OAAA;UAAKwF,GAAG,EAAEvE;QAAe;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,EAGL7D,gBAAgB,iBACff,OAAA;QAAKuE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACxCxE,OAAA;UACE8E,OAAO,EAAEjB,cAAe;UACxBU,SAAS,EAAC,uFAAuF;UAAAC,QAAA,eAEjGxE,OAAA;YAAKuE,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAT,QAAA,eAC5ExE,OAAA;cAAMkF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA4B;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5E,OAAA;MAAKuE,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCxE,OAAA;QAAKuE,SAAS,EAAE,gBAAgB9D,cAAc,GAAG,SAAS,GAAG,EAAE,EAAG;QAAA+D,QAAA,gBAChExE,OAAA;UACEwF,GAAG,EAAEtE,QAAS;UACdgF,IAAI,EAAC,MAAM;UACX3B,SAAS,EAAC,OAAO;UACjB4B,WAAW,EAAC,kDAAkD;UAC9DC,KAAK,EAAE7F,SAAU;UACjB8F,QAAQ,EAAGnD,CAAC,IAAK1C,YAAY,CAAC0C,CAAC,CAACQ,MAAM,CAAC0C,KAAK,CAAE;UAC9CE,OAAO,EAAEA,CAAA,KAAM5F,iBAAiB,CAAC,IAAI,CAAE;UACvC6F,MAAM,EAAEA,CAAA,KAAM7F,iBAAiB,CAAC,KAAK,CAAE;UACvC8F,SAAS,EAAEvD;QAAe;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACF5E,OAAA;UAAKuE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxE,EAAA,CAlWIH,UAAU;EAAA,QAqBVH,SAAS;AAAA;AAAA2G,EAAA,GArBTxG,UAAU;AAoWhB,eAAeA,UAAU;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}