{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { featureDefinitions } from './definitions.mjs';\nfunction loadFeatures(features) {\n  for (const key in features) {\n    featureDefinitions[key] = _objectSpread(_objectSpread({}, featureDefinitions[key]), features[key]);\n  }\n}\nexport { loadFeatures };", "map": {"version": 3, "names": ["featureDefinitions", "loadFeatures", "features", "key", "_objectSpread"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/motion/features/load-features.mjs"], "sourcesContent": ["import { featureDefinitions } from './definitions.mjs';\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        featureDefinitions[key] = {\n            ...featureDefinitions[key],\n            ...features[key],\n        };\n    }\n}\n\nexport { loadFeatures };\n"], "mappings": ";AAAA,SAASA,kBAAkB,QAAQ,mBAAmB;AAEtD,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC5B,KAAK,MAAMC,GAAG,IAAID,QAAQ,EAAE;IACxBF,kBAAkB,CAACG,GAAG,CAAC,GAAAC,aAAA,CAAAA,aAAA,KAChBJ,kBAAkB,CAACG,GAAG,CAAC,GACvBD,QAAQ,CAACC,GAAG,CAAC,CACnB;EACL;AACJ;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}