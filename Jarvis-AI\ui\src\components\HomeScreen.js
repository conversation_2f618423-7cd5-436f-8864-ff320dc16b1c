import React, { useState, useEffect, useRef } from 'react';
import Footer from './layout/Footer';
import AnimatedButton from './ui/AnimatedButton';

// Import the existing cute menu icon (keeping your original implementation)
import CuteMenuIcon from './ui/CuteMenuIcon';
import ExpandableSidebar from './ui/ExpandableSidebar';

const HomeScreen = ({ onEnterJarvis }) => {
  const [isEntering, setIsEntering] = useState(false);
  const [showExitAnimation, setShowExitAnimation] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const wallpaperRef = useRef(null);
  const exitAnimationRef = useRef(null);

  // Handle entering Jarvis mode
  const handleEnterJarvis = () => {
    setIsEntering(true);
    setShowExitAnimation(true);

    // Wait for exit animation to complete before transitioning
    setTimeout(() => {
      onEnterJarvis();
    }, 2000); // Adjust timing based on screenexit.gif duration
  };

  // Handle menu actions
  const handleMenuToggle = (isOpen) => {
    setMenuOpen(isOpen);
  };

  const handleSettingsClick = () => {
    console.log('Settings clicked');
  };

  const handleHelpClick = () => {
    console.log('Help clicked');
  };

  const handleVoiceCommandsClick = () => {
    console.log('Voice Commands clicked');
  };

  const handleAboutClick = () => {
    console.log('About clicked');
  };

  // Additive dissolve effect for wallpaper looping
  useEffect(() => {
    const wallpaper = wallpaperRef.current;
    if (wallpaper) {
      wallpaper.style.mixBlendMode = 'screen';
      wallpaper.style.filter = 'brightness(0.8) contrast(1.2)';
    }
  }, []);

  return (
    <div className="homescreen-container">
      {/* Cute Menu Icon in Top Left */}
      <CuteMenuIcon onMenuToggle={handleMenuToggle} />
      
      {/* Expandable Sidebar - Left Side */}
      <div className="fixed left-0 top-1/2 transform -translate-y-1/2 z-10">
        <ExpandableSidebar />
      </div>

      {/* Animated Wallpaper Background */}
      <div className="wallpaper-container">
        <img
          ref={wallpaperRef}
          src="/assets/HomeSettingsscreen.gif"
          alt="Animated Wallpaper"
          className="wallpaper-background"
          onError={(e) => {
            // Fallback to a gradient if gif not found
            e.target.style.display = 'none';
            e.target.parentElement.style.background =
              'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)';
          }}
        />

        {/* Overlay for better text readability */}
        <div className="wallpaper-overlay"></div>
      </div>

      {/* Exit Animation Overlay */}
      {showExitAnimation && (
        <div className="exit-animation-overlay">
          <img
            ref={exitAnimationRef}
            src="/assets/screenexit.gif"
            alt="Screen Exit Animation"
            className="exit-animation"
            onError={(e) => {
              // Fallback animation if gif not found
              e.target.style.display = 'none';
              e.target.parentElement.innerHTML = '<div class="fallback-exit-animation"></div>';
            }}
          />
        </div>
      )}

      {/* Main Content */}
      <div className={`homescreen-content ${isEntering ? 'entering' : ''}`}>
        {/* Spider-Man Style Title */}
        <div className="title-section">
          <div className="marvel-logo">MARVEL</div>
          <h1 className="main-title">JARVIS</h1>
          <div className="subtitle">PRESS ⚡ TO START</div>
        </div>

        {/* Central Jarvis Icon/Button - Now using AnimatedButton */}
        <div className="jarvis-icon-container">
          <div className="jarvis-icon">
            <div className="icon-glow"></div>
            <div className="icon-core">
              <svg viewBox="0 0 100 100" className="jarvis-svg">
                <circle cx="50" cy="50" r="30" className="outer-ring" />
                <circle cx="50" cy="50" r="20" className="middle-ring" />
                <circle cx="50" cy="50" r="10" className="inner-core" />
                <path d="M30 50 L70 50 M50 30 L50 70" className="cross-lines" />
              </svg>
            </div>
          </div>
          <AnimatedButton
            onClick={handleEnterJarvis}
            variant="primary"
            size="large"
            loading={isEntering}
            className="mt-4"
          >
            {isEntering ? 'ACTIVATING...' : 'ACTIVATE JARVIS'}
          </AnimatedButton>
        </div>

        {/* Bottom Menu - Now using AnimatedButtons */}
        <div className="bottom-menu">
          <AnimatedButton
            onClick={handleSettingsClick}
            variant="secondary"
            size="small"
            className="menu-button"
          >
            ⚙️ Settings
          </AnimatedButton>

          <AnimatedButton
            onClick={handleVoiceCommandsClick}
            variant="secondary"
            size="small"
            className="menu-button"
          >
            🎤 Voice
          </AnimatedButton>

          <AnimatedButton
            onClick={handleHelpClick}
            variant="secondary"
            size="small"
            className="menu-button"
          >
            ❓ Help
          </AnimatedButton>

          <AnimatedButton
            onClick={handleAboutClick}
            variant="secondary"
            size="small"
            className="menu-button"
          >
            ℹ️ About
          </AnimatedButton>
        </div>
      </div>

      {/* Footer with additional navigation */}
      <Footer
        onHomeClick={() => console.log('Home clicked')}
        onSettingsClick={handleSettingsClick}
        onHelpClick={handleHelpClick}
        showNavigation={!menuOpen} // Hide footer nav when menu is open
      />
    </div>

      


  );
};

export default HomeScreen;
