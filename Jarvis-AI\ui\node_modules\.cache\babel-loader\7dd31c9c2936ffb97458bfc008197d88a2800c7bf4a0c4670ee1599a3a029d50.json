{"ast": null, "code": "import { DragGesture } from '../../gestures/drag/index.mjs';\nimport { PanGesture } from '../../gestures/pan/index.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\nimport { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\nconst drag = {\n  pan: {\n    Feature: PanGesture\n  },\n  drag: {\n    Feature: DragGesture,\n    ProjectionNode: HTMLProjectionNode,\n    MeasureLayout\n  }\n};\nexport { drag };", "map": {"version": 3, "names": ["DragGesture", "PanGesture", "MeasureLayout", "HTMLProjectionNode", "drag", "pan", "Feature", "ProjectionNode"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/motion/features/drag.mjs"], "sourcesContent": ["import { DragGesture } from '../../gestures/drag/index.mjs';\nimport { PanGesture } from '../../gestures/pan/index.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\nimport { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\n\nconst drag = {\n    pan: {\n        Feature: PanGesture,\n    },\n    drag: {\n        Feature: DragGesture,\n        ProjectionNode: HTMLProjectionNode,\n        MeasureLayout,\n    },\n};\n\nexport { drag };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,kBAAkB,QAAQ,8CAA8C;AAEjF,MAAMC,IAAI,GAAG;EACTC,GAAG,EAAE;IACDC,OAAO,EAAEL;EACb,CAAC;EACDG,IAAI,EAAE;IACFE,OAAO,EAAEN,WAAW;IACpBO,cAAc,EAAEJ,kBAAkB;IAClCD;EACJ;AACJ,CAAC;AAED,SAASE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}