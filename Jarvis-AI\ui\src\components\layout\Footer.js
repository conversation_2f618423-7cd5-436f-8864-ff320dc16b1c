import React from 'react';
import AnimatedButton from '../ui/AnimatedButton';

const Footer = ({ 
  onHomeClick, 
  onSettingsClick, 
  onHelpClick,
  showNavigation = true 
}) => {
  return (
    <footer className="fixed bottom-0 left-0 right-0 p-4 bg-black/20 backdrop-blur-sm">
      {showNavigation && (
        <div className="flex justify-center space-x-4">
          <AnimatedButton
            variant="secondary"
            size="small"
            onClick={onHomeClick}
          >
            🏠 Home
          </AnimatedButton>
          
          <AnimatedButton
            variant="secondary"
            size="small"
            onClick={onSettingsClick}
          >
            ⚙️ Settings
          </AnimatedButton>
          
          <AnimatedButton
            variant="secondary"
            size="small"
            onClick={onHelpClick}
          >
            ❓ Help
          </AnimatedButton>
        </div>
      )}
      
      <div className="text-center mt-2">
        <p className="text-xs text-gray-400">
          © 2024 JARVIS UI - Powered by AI
        </p>
      </div>
    </footer>
  );
};

export default Footer;
