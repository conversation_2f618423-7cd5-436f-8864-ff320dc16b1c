import React from 'react';
import CuteMenuIcon from './components/ui/CuteMenuIcon';

function TestCuteMenuIcon() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black text-white flex items-center justify-center">
      <div className="relative w-full h-full">
        <h1 className="text-4xl font-bold text-center mb-8 text-cyan-400">
          JARVIS - Testing CuteMenuIcon
        </h1>
        
        <CuteMenuIcon />
        
        <div className="text-center mt-16">
          <p className="text-lg text-gray-300 mb-4">
            The CuteMenuIcon should appear in the top-left corner with complete frame visible
          </p>
          <p className="text-sm text-gray-400">
            Hover around the icon to see 3D effects and click to open the chat interface
          </p>
        </div>
      </div>
    </div>
  );
}

export default TestCuteMenuIcon;
