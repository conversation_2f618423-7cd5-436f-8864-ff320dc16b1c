{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEqualsRounded, isDeltaZero, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { record } from '../../debug/record.mjs';\nimport { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { animateSingleValue } from '../../animation/interfaces/single-value.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { cancelFrame, frameData, steps, frame } from '../../frameloop/frame.mjs';\nimport { noop } from '../../utils/noop.mjs';\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = {\n  visibility: \"hidden\"\n};\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\n/**\n * Use a mutable data object for debug data so as to not create a new\n * object every frame.\n */\nconst projectionFrameData = {\n  type: \"projectionFrame\",\n  totalNodes: 0,\n  resolvedTargetDeltas: 0,\n  recalculatedProjection: 0\n};\nfunction createProjectionNode(_ref) {\n  let {\n    attachResizeListener,\n    defaultParent,\n    measureScroll,\n    checkIsScrollRoot,\n    resetTransform\n  } = _ref;\n  return class ProjectionNode {\n    constructor() {\n      let latestValues = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      let parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent();\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this node needs\n       * recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Flag to true if the layout *or* transform has changed. This then gets propagated\n       * throughout the projection tree, forcing any element below to recalculate on the next frame.\n       */\n      this.isSharedProjectionDirty = false;\n      /**\n       * Flag transform dirty. This gets propagated throughout the whole tree but is only\n       * respected by shared nodes.\n       */\n      this.isTransformDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      this.hasTreeAnimated = false;\n      // Note: Currently only running on root node\n      this.updateScheduled = false;\n      this.projectionUpdateScheduled = false;\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.projectionUpdateScheduled = false;\n        /**\n         * Reset debug counts. Manually resetting rather than creating a new\n         * object each frame.\n         */\n        projectionFrameData.totalNodes = projectionFrameData.resolvedTargetDeltas = projectionFrameData.recalculatedProjection = 0;\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n        this.nodes.forEach(cleanDirtyNodes);\n        record(projectionFrameData);\n      };\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      subscriptionManager && subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance) {\n      let isLayoutDirty = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.root.hasTreeAnimated;\n      if (this.instance) return;\n      this.isSVG = isSVGElement(instance);\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      this.parent && this.parent.children.add(this);\n      if (isLayoutDirty && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", _ref2 => {\n          let {\n            delta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n            layout: newLayout\n          } = _ref2;\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = this.options.transition || visualElement.getDefaultTransition() || defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const targetChanged = !this.targetLayout || !boxEqualsRounded(this.targetLayout, newLayout) || hasRelativeTargetChanged;\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n          if (this.options.layoutRoot || this.resumeFrom && this.resumeFrom.instance || hasOnlyRelativeTargetChanged || hasLayoutChanged && (targetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n            const animationOptions = _objectSpread(_objectSpread({}, getValueTransition(layoutTransition, \"layout\")), {}, {\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            });\n            if (visualElement.shouldReduceMotion || this.options.layoutRoot) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged) {\n              finishAnimation(this);\n            }\n            if (this.isLead() && this.options.onExitComplete) {\n              this.options.onExitComplete();\n            }\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      const stack = this.getStack();\n      stack && stack.remove(this);\n      this.parent && this.parent.children.delete(this);\n      this.instance = undefined;\n      cancelFrame(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      return this.isAnimationBlocked || this.parent && this.parent.isTreeAnimationBlocked() || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      this.nodes && this.nodes.forEach(resetRotation);\n      this.animationId++;\n    }\n    getTransformTemplate() {\n      const {\n        visualElement\n      } = this.options;\n      return visualElement && visualElement.getProps().transformTemplate;\n    }\n    willUpdate() {\n      let shouldNotifyListeners = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      this.root.hasTreeAnimated = true;\n      if (this.root.isUpdateBlocked()) {\n        this.options.onExitComplete && this.options.onExitComplete();\n        return;\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n        if (node.options.layoutRoot) {\n          node.willUpdate(false);\n        }\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = this.getTransformTemplate();\n      this.prevTransformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    update() {\n      this.updateScheduled = false;\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) {\n        this.nodes.forEach(clearIsLayoutDirty);\n      }\n      this.isUpdating = false;\n      /**\n       * Write\n       */\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      /**\n       * Manually flush any pending updates. Ideally\n       * we could leave this to the following requestAnimationFrame but this seems\n       * to leave a flash of incorrectly styled content.\n       */\n      const now = performance.now();\n      frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n      frameData.timestamp = now;\n      frameData.isProcessing = true;\n      steps.update.process(frameData);\n      steps.preRender.process(frameData);\n      steps.render.process(frameData);\n      frameData.isProcessing = false;\n    }\n    didUpdate() {\n      if (!this.updateScheduled) {\n        this.updateScheduled = true;\n        queueMicrotask(() => this.update());\n      }\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      if (!this.projectionUpdateScheduled) {\n        this.projectionUpdateScheduled = true;\n        frame.preRender(this.updateProjection, false, true);\n      }\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      frame.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n    }\n    updateLayout() {\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      const {\n        visualElement\n      } = this.options;\n      visualElement && visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n    }\n    updateScroll() {\n      let phase = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"measure\";\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement) {\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot: checkIsScrollRoot(this.instance),\n          offset: measureScroll(this.instance)\n        };\n      }\n    }\n    resetTransform() {\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = this.getTransformTemplate();\n      const transformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure() {\n      let removeTransform = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      // Remove viewport scroll to give page-relative coordinates\n      const {\n        scroll\n      } = this.root;\n      if (scroll) {\n        translateAxis(box.x, scroll.offset.x);\n        translateAxis(box.y, scroll.offset.y);\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.isRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n            const {\n              scroll: rootScroll\n            } = this.root;\n            /**\n             * Undo the application of page scroll that was originally added\n             * to the measured bounding box.\n             */\n            if (rootScroll) {\n              translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n              translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n            }\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box) {\n      let transformOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.root.scheduleUpdateProjection();\n      this.isProjectionDirty = true;\n    }\n    setOptions(options) {\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, this.options), options), {}, {\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      });\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    forceRelativeParentToResolveTarget() {\n      if (!this.relativeParent) return;\n      /**\n       * If the parent target isn't up-to-date, force it to update.\n       * This is an unfortunate de-optimisation as it means any updating relative\n       * projection will cause all the relative parents to recalculate back\n       * up the tree.\n       */\n      if (this.relativeParent.resolvedRelativeTargetAt !== frameData.timestamp) {\n        this.relativeParent.resolveTargetDelta(true);\n      }\n    }\n    resolveTargetDelta() {\n      let forceRecalculation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      var _a;\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      const canSkip = !(forceRecalculation || isShared && this.isSharedProjectionDirty || this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty) || this.attemptToResolveRelativeTarget);\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      this.resolvedRelativeTargetAt = frameData.timestamp;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      // TODO If this is unsuccessful this currently happens every frame\n      if (!this.targetDelta && !this.relativeTarget) {\n        // TODO: This is a semi-repetition of further down this function, make DRY\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && this.relativeParent && this.relativeParent.target) {\n        this.forceRelativeParentToResolveTarget();\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * Increase debug counter for resolved target deltas\n       */\n      projectionFrameData.resolvedTargetDeltas++;\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) {\n        return undefined;\n      }\n      if (this.parent.isProjecting()) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    isProjecting() {\n      return Boolean((this.relativeTarget || this.targetDelta || this.options.layoutRoot) && this.layout);\n    }\n    calcProjection() {\n      var _a;\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      /**\n       * If this is a normal layout animation and neither this node nor its nearest projecting\n       * is dirty then we can't skip.\n       */\n      if (this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If this is a shared layout animation and this node's shared projection is dirty then\n       * we can't skip.\n       */\n      if (isShared && (this.isSharedProjectionDirty || this.isTransformDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If we have resolved the target this frame we must recalculate the\n       * projection to ensure it visually represents the internal calculations.\n       */\n      if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n        canSkip = false;\n      }\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(this.parent && this.parent.isTreeAnimating || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Record previous tree scales before updating.\n       */\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      /**\n       * If this layer needs to perform scale correction but doesn't have a target,\n       * use the layout as the target.\n       */\n      if (lead.layout && !lead.target && (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n        lead.target = lead.layout.layoutBox;\n      }\n      const {\n        target\n      } = lead;\n      if (!target) {\n        /**\n         * If we don't have a target to project into, but we were previously\n         * projecting, we want to remove the stored transform and schedule\n         * a render to ensure the elements reflect the removed transform.\n         */\n        if (this.projectionTransform) {\n          this.projectionDelta = createDelta();\n          this.projectionTransform = \"none\";\n          this.scheduleRender();\n        }\n        return;\n      }\n      if (!this.projectionDelta) {\n        this.projectionDelta = createDelta();\n        this.projectionDeltaWithTransform = createDelta();\n      }\n      const prevProjectionTransform = this.projectionTransform;\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n      if (this.projectionTransform !== prevProjectionTransform || this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n      /**\n       * Increase debug counter for recalculated projections\n       */\n      projectionFrameData.recalculatedProjection++;\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender() {\n      let notifyAll = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      this.options.scheduleRender && this.options.scheduleRender();\n      if (notifyAll) {\n        const stack = this.getStack();\n        stack && stack.scheduleRender();\n      }\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    setAnimationOrigin(delta) {\n      let hasOnlyRelativeTargetChanged = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n      const mixedValues = _objectSpread({}, this.latestValues);\n      const targetDelta = createDelta();\n      if (!this.relativeParent || !this.relativeParent.options.layoutRoot) {\n        this.relativeTarget = this.relativeTargetOrigin = undefined;\n      }\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const snapshotSource = snapshot ? snapshot.source : undefined;\n      const layoutSource = this.layout ? this.layout.source : undefined;\n      const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n      const stack = this.getStack();\n      const isOnlyMember = !stack || stack.members.length <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      let prevRelativeTarget;\n      this.mixTargetDelta = latest => {\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && this.relativeParent && this.relativeParent.layout) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n          /**\n           * If this is an unchanged relative target we can consider the\n           * projection not dirty.\n           */\n          if (prevRelativeTarget && boxEquals(this.relativeTarget, prevRelativeTarget)) {\n            this.isProjectionDirty = false;\n          }\n          if (!prevRelativeTarget) prevRelativeTarget = createBox();\n          copyBoxInto(prevRelativeTarget, this.relativeTarget);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n    }\n    startAnimation(options) {\n      this.notifyListeners(\"animationStart\");\n      this.currentAnimation && this.currentAnimation.stop();\n      if (this.resumingFrom && this.resumingFrom.currentAnimation) {\n        this.resumingFrom.currentAnimation.stop();\n      }\n      if (this.pendingAnimation) {\n        cancelFrame(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = frame.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        this.currentAnimation = animateSingleValue(0, animationTarget, _objectSpread(_objectSpread({}, options), {}, {\n          onUpdate: latest => {\n            this.mixTargetDelta(latest);\n            options.onUpdate && options.onUpdate(latest);\n          },\n          onComplete: () => {\n            options.onComplete && options.onComplete();\n            this.completeAnimation();\n          }\n        }));\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      const stack = this.getStack();\n      stack && stack.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      if (this.currentAnimation) {\n        this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      const config = node.options.initialPromotionConfig;\n      node.promote({\n        transition: config ? config.transition : undefined,\n        preserveFollowOpacity: config && config.shouldPreserveFollowOpacity ? config.shouldPreserveFollowOpacity(node) : undefined\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n    }\n    getPrevLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote() {\n      let {\n        needsReset,\n        transition,\n        preserveFollowOpacity\n      } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected rotation values, we can early return without a forced render.\n      let hasRotate = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ) {\n        hasRotate = true;\n      }\n      // If there's no rotation values, we don't need to do any more.\n      if (!hasRotate) return;\n      const resetValues = {};\n      // Check the rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        const key = \"rotate\" + transformAxes[i];\n        // Record the rotation and then temporarily set it to 0\n        if (latestValues[key]) {\n          resetValues[key] = latestValues[key];\n          visualElement.setStaticValue(key, 0);\n        }\n      }\n      // Force a render of this element to apply the transform with all rotations\n      // set to 0.\n      visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles(styleProp) {\n      var _a, _b;\n      if (!this.instance || this.isSVG) return undefined;\n      if (!this.isVisible) {\n        return hiddenVisibility;\n      }\n      const styles = {\n        visibility: \"\"\n      };\n      const transformTemplate = this.getTransformTemplate();\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = \"\".concat(x.origin * 100, \"% \").concat(y.origin * 100, \"% 0\");\n      if (lead.animationValues) {\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? (_b = (_a = valuesToRender.opacity) !== null && _a !== void 0 ? _a : this.latestValues.opacity) !== null && _b !== void 0 ? _b : 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo\n        } = scaleCorrectors[key];\n        /**\n         * Only apply scale correction to the value if we have an\n         * active projection transform. Otherwise these values become\n         * vulnerable to distortion if the element changes size without\n         * a corresponding layout animation.\n         */\n        const corrected = styles.transform === \"none\" ? valuesToRender[key] : correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          styles[key] = corrected;\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => {\n        var _a;\n        return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      });\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  var _a;\n  const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n        /**\n         * Ensure relative target gets resized and rerendererd\n         */\n        if (node.relativeTarget && !node.currentAnimation) {\n          node.isProjectionDirty = true;\n          node.relativeTarget[axis].max = node.relativeTarget[axis].min + length;\n        }\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeTargetChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n            hasRelativeTargetChanged = true;\n          }\n          if (relativeParent.options.layoutRoot) {\n            node.relativeTarget = relativeLayout;\n            node.relativeTargetOrigin = relativeSnapshot;\n            node.relativeParent = relativeParent;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeTargetChanged\n    });\n  } else if (node.isLead()) {\n    const {\n      onExitComplete\n    } = node.options;\n    onExitComplete && onExitComplete();\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Increase debug counter for nodes encountered this frame\n   */\n  projectionFrameData.totalNodes++;\n  if (!node.parent) return;\n  /**\n   * If this node isn't projecting, propagate isProjectionDirty. It will have\n   * no performance impact but it will allow the next child that *is* projecting\n   * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n   * correcting.\n   */\n  if (!node.isProjecting()) {\n    node.isProjectionDirty = node.parent.isProjectionDirty;\n  }\n  /**\n   * Propagate isSharedProjectionDirty and isTransformDirty\n   * throughout the whole tree. A future revision can take another look at\n   * this but for safety we still recalcualte shared nodes.\n   */\n  node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty || node.parent.isProjectionDirty || node.parent.isSharedProjectionDirty));\n  node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n  node.isProjectionDirty = node.isSharedProjectionDirty = node.isTransformDirty = false;\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n  node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n  node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetRotation(node) {\n  node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mix(delta.translate, 0, p);\n  output.scale = mix(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mix(from.min, to.min, p);\n  output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nconst userAgentContains = string => typeof navigator !== \"undefined\" && navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\") ? Math.round : noop;\nfunction roundAxis(axis) {\n  // Round to the nearest .5 pixels to support subpixel layouts\n  axis.min = roundPoint(axis.min);\n  axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": {"version": 3, "names": ["SubscriptionManager", "mixValues", "copyBoxInto", "translateAxis", "transformBox", "applyBoxDelta", "applyTreeDeltas", "calcRelativePosition", "calcRelativeBox", "calcBoxDelta", "calcLength", "isNear", "removeBoxTransforms", "createBox", "create<PERSON><PERSON><PERSON>", "getValueTransition", "boxEqualsRounded", "isDeltaZero", "aspectRatio", "boxEquals", "NodeStack", "scaleCorrectors", "buildProjectionTransform", "eachAxis", "hasTransform", "hasScale", "has2DTranslate", "FlatTree", "resolveMotionValue", "globalProjectionState", "delay", "mix", "record", "isSVGElement", "animateSingleValue", "clamp", "cancelFrame", "frameData", "steps", "frame", "noop", "transformAxes", "hiddenVisibility", "visibility", "animationTarget", "id", "projectionFrameData", "type", "totalNodes", "resolvedTargetDeltas", "recalculatedProjection", "createProjectionNode", "_ref", "attachResizeListener", "defaultParent", "measureScroll", "checkIsScrollRoot", "resetTransform", "ProjectionNode", "constructor", "latestValues", "arguments", "length", "undefined", "parent", "animationId", "children", "Set", "options", "isTreeAnimating", "isAnimationBlocked", "isLayoutDirty", "isProjectionDirty", "isSharedProjectionDirty", "isTransformDirty", "updateManuallyBlocked", "updateBlockedByResize", "isUpdating", "isSVG", "needsReset", "shouldResetTransform", "treeScale", "x", "y", "eventHandlers", "Map", "hasTreeAnimated", "updateScheduled", "projectionUpdateScheduled", "checkUpdateFailed", "clearAllSnapshots", "updateProjection", "nodes", "for<PERSON>ach", "propagateDirtyNodes", "resolveTargetDel<PERSON>", "calcProjection", "cleanDirtyNodes", "hasProjected", "isVisible", "animationProgress", "sharedNodes", "root", "path", "depth", "i", "addEventListener", "name", "handler", "has", "set", "get", "add", "notifyListeners", "subscriptionManager", "_len", "args", "Array", "_key", "notify", "hasListeners", "mount", "instance", "layoutId", "layout", "visualElement", "current", "cancelDelay", "resizeUnblockUpdate", "hasAnimatedSinceResize", "finishAnimation", "registerSharedNode", "animate", "_ref2", "delta", "hasLayoutChanged", "hasRelativeTargetChanged", "newLayout", "isTreeAnimationBlocked", "target", "<PERSON><PERSON><PERSON><PERSON>", "layoutTransition", "transition", "getDefaultTransition", "defaultLayoutTransition", "onLayoutAnimationStart", "onLayoutAnimationComplete", "getProps", "targetChanged", "targetLayout", "hasOnlyRelativeTargetChanged", "layoutRoot", "resumeFrom", "currentAnimation", "resumingFrom", "setAnimationOrigin", "animationOptions", "_objectSpread", "onPlay", "onComplete", "shouldReduceMotion", "startAnimation", "isLead", "onExitComplete", "unmount", "willUpdate", "remove", "stack", "getStack", "delete", "blockUpdate", "unblockUpdate", "isUpdateBlocked", "startUpdate", "resetRotation", "getTransformTemplate", "transformTemplate", "shouldNotifyListeners", "node", "updateScroll", "prevTransformTemplateValue", "updateSnapshot", "update", "updateWasBlocked", "clearMeasurements", "clearIsLayoutDirty", "resetTransformStyle", "updateLayout", "notifyLayoutUpdate", "now", "performance", "timestamp", "isProcessing", "process", "preRender", "render", "didUpdate", "queueMicrotask", "clearSnapshot", "removeLeadSnapshots", "scheduleUpdateProjection", "scheduleCheckAfterUnmount", "postRender", "snapshot", "measure", "alwaysMeasureLayout", "prevLayout", "layoutCorrected", "projectionDel<PERSON>", "layoutBox", "phase", "needsMeasurement", "Boolean", "layoutScroll", "scroll", "isRoot", "offset", "isResetRequested", "hasProjection", "transformTemplateValue", "transformTemplateHasChanged", "scheduleRender", "removeTransform", "pageBox", "measurePageBox", "removeElementScroll", "roundBox", "measuredBox", "source", "box", "measureViewportBox", "boxWithoutScroll", "rootScroll", "applyTransform", "transformOnly", "withTransforms", "boxWithoutTransform", "sourceBox", "nodeBox", "set<PERSON>argetD<PERSON><PERSON>", "targetDel<PERSON>", "setOptions", "crossfade", "forceRelativeParentToResolveTarget", "relativeParent", "resolvedRelativeTargetAt", "forceRecalculation", "_a", "lead", "getLead", "isShared", "canSkip", "attemptToResolveRelativeTarget", "getClosestProjectingParent", "relativeTarget<PERSON><PERSON>in", "targetWithTransforms", "isProjecting", "pendingAnimation", "prevTreeScaleX", "prevTreeScaleY", "projectionTransform", "projectionDeltaWithTransform", "prevProjectionTransform", "hide", "show", "notifyAll", "snapshotLatestValues", "mixedValues", "relativeLayout", "snapshotSource", "layoutSource", "isSharedLayoutAnimation", "isOnlyMember", "members", "shouldCrossfadeOpacity", "some", "hasOpacityCrossfade", "prevRelativeTarget", "mixTargetDelta", "latest", "progress", "mixAxisDelta", "mixBox", "animationValues", "stop", "onUpdate", "completeAnimation", "preserveOpacity", "exitAnimationComplete", "applyTransformsToTarget", "shouldAnimatePositionOnly", "animationType", "xLength", "min", "max", "y<PERSON><PERSON><PERSON>", "config", "initialPromotionConfig", "promote", "preserveFollowOpacity", "shouldPreserveFollowOpacity", "getPrevLead", "prevLead", "relegate", "hasRotate", "rotate", "rotateX", "rotateY", "rotateZ", "resetValues", "key", "setStaticValue", "getProjectionStyles", "styleProp", "_b", "styles", "opacity", "pointerEvents", "transform", "emptyStyles", "valuesToRender", "transform<PERSON><PERSON>in", "concat", "origin", "opacityExit", "correct", "applyTo", "corrected", "num", "resetTree", "clear", "measuredLayout", "axis", "axisSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "visualD<PERSON><PERSON>", "parentSnapshot", "parentLayout", "relativeSnapshot", "onBeforeLayoutMeasure", "removeLeadSnapshot", "output", "p", "translate", "scale", "originPoint", "mixAxis", "from", "to", "duration", "ease", "userAgentContains", "string", "navigator", "userAgent", "toLowerCase", "includes", "roundPoint", "Math", "round", "roundAxis"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEqualsRounded, isDeltaZero, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { record } from '../../debug/record.mjs';\nimport { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { animateSingleValue } from '../../animation/interfaces/single-value.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { cancelFrame, frameData, steps, frame } from '../../frameloop/frame.mjs';\nimport { noop } from '../../utils/noop.mjs';\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = { visibility: \"hidden\" };\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\n/**\n * Use a mutable data object for debug data so as to not create a new\n * object every frame.\n */\nconst projectionFrameData = {\n    type: \"projectionFrame\",\n    totalNodes: 0,\n    resolvedTargetDeltas: 0,\n    recalculatedProjection: 0,\n};\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                projectionFrameData.totalNodes =\n                    projectionFrameData.resolvedTargetDeltas =\n                        projectionFrameData.recalculatedProjection =\n                            0;\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                record(projectionFrameData);\n            };\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance, isLayoutDirty = this.root.hasTreeAnimated) {\n            if (this.instance)\n                return;\n            this.isSVG = isSVGElement(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (isLayoutDirty && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeTargetChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const targetChanged = !this.targetLayout ||\n                        !boxEqualsRounded(this.targetLayout, newLayout) ||\n                        hasRelativeTargetChanged;\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n                    if (this.options.layoutRoot ||\n                        (this.resumeFrom && this.resumeFrom.instance) ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (targetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            cancelFrame(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            this.isUpdating = false;\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = performance.now();\n            frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n            frameData.timestamp = now;\n            frameData.isProcessing = true;\n            steps.update.process(frameData);\n            steps.preRender.process(frameData);\n            steps.render.process(frameData);\n            frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                queueMicrotask(() => this.update());\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement) {\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot: checkIsScrollRoot(this.instance),\n                    offset: measureScroll(this.instance),\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            // Remove viewport scroll to give page-relative coordinates\n            const { scroll } = this.root;\n            if (scroll) {\n                translateAxis(box.x, scroll.offset.x);\n                translateAxis(box.y, scroll.offset.y);\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.isRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                        const { scroll: rootScroll } = this.root;\n                        /**\n                         * Undo the application of page scroll that was originally added\n                         * to the measured bounding box.\n                         */\n                        if (rootScroll) {\n                            translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n                            translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n                        }\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            var _a;\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty) ||\n                this.attemptToResolveRelativeTarget);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            // TODO If this is unsuccessful this currently happens every frame\n            if (!this.targetDelta && !this.relativeTarget) {\n                // TODO: This is a semi-repetition of further down this function, make DRY\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            projectionFrameData.resolvedTargetDeltas++;\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            var _a;\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.projectionTransform) {\n                    this.projectionDelta = createDelta();\n                    this.projectionTransform = \"none\";\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta) {\n                this.projectionDelta = createDelta();\n                this.projectionDeltaWithTransform = createDelta();\n            }\n            const prevProjectionTransform = this.projectionTransform;\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n            if (this.projectionTransform !== prevProjectionTransform ||\n                this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            projectionFrameData.recalculatedProjection++;\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.scheduleRender && this.options.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot\n                ? snapshot.latestValues\n                : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        boxEquals(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = createBox();\n                    copyBoxInto(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation && this.currentAnimation.stop();\n            if (this.resumingFrom && this.resumingFrom.currentAnimation) {\n                this.resumingFrom.currentAnimation.stop();\n            }\n            if (this.pendingAnimation) {\n                cancelFrame(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = frame.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                this.currentAnimation = animateSingleValue(0, animationTarget, {\n                    ...options,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onComplete: () => {\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n        }\n        getPrevLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected rotation values, we can early return without a forced render.\n            let hasRotate = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ) {\n                hasRotate = true;\n            }\n            // If there's no rotation values, we don't need to do any more.\n            if (!hasRotate)\n                return;\n            const resetValues = {};\n            // Check the rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                const key = \"rotate\" + transformAxes[i];\n                // Record the rotation and then temporarily set it to 0\n                if (latestValues[key]) {\n                    resetValues[key] = latestValues[key];\n                    visualElement.setStaticValue(key, 0);\n                }\n            }\n            // Force a render of this element to apply the transform with all rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp) {\n            var _a, _b;\n            if (!this.instance || this.isSVG)\n                return undefined;\n            if (!this.isVisible) {\n                return hiddenVisibility;\n            }\n            const styles = {\n                visibility: \"\",\n            };\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? (_b = (_a = valuesToRender.opacity) !== null && _a !== void 0 ? _a : this.latestValues.opacity) !== null && _b !== void 0 ? _b : 1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo } = scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = styles.transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    styles[key] = corrected;\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => { var _a; return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop(); });\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    var _a;\n    const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeTargetChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n                        hasRelativeTargetChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    projectionFrameData.totalNodes++;\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetRotation(node) {\n    node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mix(delta.translate, 0, p);\n    output.scale = mix(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mix(from.min, to.min, p);\n    output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\n\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "mappings": ";AAAA,SAASA,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,QAAQ,6BAA6B;AACzG,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,UAAU,EAAEC,MAAM,QAAQ,4BAA4B;AACpH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,uBAAuB;AAC7F,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,4BAA4B;AACnF,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,aAAa;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,YAAY,QAAQ,2CAA2C;AACxE,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,QAAQ,2BAA2B;AAChF,SAASC,IAAI,QAAQ,sBAAsB;AAE3C,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC,MAAMC,gBAAgB,GAAG;EAAEC,UAAU,EAAE;AAAS,CAAC;AACjD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI;AAC5B,IAAIC,EAAE,GAAG,CAAC;AACV;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EACxBC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE,CAAC;EACbC,oBAAoB,EAAE,CAAC;EACvBC,sBAAsB,EAAE;AAC5B,CAAC;AACD,SAASC,oBAAoBA,CAAAC,IAAA,EAA6F;EAAA,IAA5F;IAAEC,oBAAoB;IAAEC,aAAa;IAAEC,aAAa;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAAL,IAAA;EACpH,OAAO,MAAMM,cAAc,CAAC;IACxBC,WAAWA,CAAA,EAA4G;MAAA,IAA3GC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAEG,MAAM,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGP,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC;MACjH;AACZ;AACA;MACY,IAAI,CAACT,EAAE,GAAGA,EAAE,EAAE;MACd;AACZ;AACA;MACY,IAAI,CAACoB,WAAW,GAAG,CAAC;MACpB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;AACZ;AACA;AACA;MACY,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B;AACZ;AACA;AACA;MACY,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B;AACZ;AACA;AACA;MACY,IAAI,CAACC,uBAAuB,GAAG,KAAK;MACpC;AACZ;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B;AACZ;AACA;MACY,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,KAAK,GAAG,KAAK;MAClB;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,SAAS,GAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC/B;AACZ;AACA;MACY,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B;MACA,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACC,iBAAiB,GAAG,MAAM;QAC3B,IAAI,IAAI,CAACZ,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,KAAK;UACvB,IAAI,CAACa,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,MAAM;QAC1B,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC;AAChB;AACA;AACA;QACgB1C,mBAAmB,CAACE,UAAU,GAC1BF,mBAAmB,CAACG,oBAAoB,GACpCH,mBAAmB,CAACI,sBAAsB,GACtC,CAAC;QACb,IAAI,CAAC0C,KAAK,CAACC,OAAO,CAACC,mBAAmB,CAAC;QACvC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACE,kBAAkB,CAAC;QACtC,IAAI,CAACH,KAAK,CAACC,OAAO,CAACG,cAAc,CAAC;QAClC,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACI,eAAe,CAAC;QACnCjE,MAAM,CAACc,mBAAmB,CAAC;MAC/B,CAAC;MACD,IAAI,CAACoD,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;AACZ;AACA;MACY;MACA,IAAI,CAACC,WAAW,GAAG,IAAIhB,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACzB,YAAY,GAAGA,YAAY;MAChC,IAAI,CAAC0C,IAAI,GAAGtC,MAAM,GAAGA,MAAM,CAACsC,IAAI,IAAItC,MAAM,GAAG,IAAI;MACjD,IAAI,CAACuC,IAAI,GAAGvC,MAAM,GAAG,CAAC,GAAGA,MAAM,CAACuC,IAAI,EAAEvC,MAAM,CAAC,GAAG,EAAE;MAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACwC,KAAK,GAAGxC,MAAM,GAAGA,MAAM,CAACwC,KAAK,GAAG,CAAC,GAAG,CAAC;MAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACvC,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC,CAACzB,oBAAoB,GAAG,IAAI;MAC5C;MACA,IAAI,IAAI,CAACsB,IAAI,KAAK,IAAI,EAClB,IAAI,CAACV,KAAK,GAAG,IAAIjE,QAAQ,CAAC,CAAC;IACnC;IACA+E,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC5B,IAAI,CAAC,IAAI,CAACxB,aAAa,CAACyB,GAAG,CAACF,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACvB,aAAa,CAAC0B,GAAG,CAACH,IAAI,EAAE,IAAI3G,mBAAmB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,IAAI,CAACoF,aAAa,CAAC2B,GAAG,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACJ,OAAO,CAAC;IACpD;IACAK,eAAeA,CAACN,IAAI,EAAW;MAC3B,MAAMO,mBAAmB,GAAG,IAAI,CAAC9B,aAAa,CAAC2B,GAAG,CAACJ,IAAI,CAAC;MAAC,SAAAQ,IAAA,GAAAtD,SAAA,CAAAC,MAAA,EADpCsD,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAzD,SAAA,CAAAyD,IAAA;MAAA;MAEzBJ,mBAAmB,IAAIA,mBAAmB,CAACK,MAAM,CAAC,GAAGH,IAAI,CAAC;IAC9D;IACAI,YAAYA,CAACb,IAAI,EAAE;MACf,OAAO,IAAI,CAACvB,aAAa,CAACyB,GAAG,CAACF,IAAI,CAAC;IACvC;IACA;AACR;AACA;IACQc,KAAKA,CAACC,QAAQ,EAA6C;MAAA,IAA3CnD,aAAa,GAAAV,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACyC,IAAI,CAAChB,eAAe;MACrD,IAAI,IAAI,CAACoC,QAAQ,EACb;MACJ,IAAI,CAAC5C,KAAK,GAAG7C,YAAY,CAACyF,QAAQ,CAAC;MACnC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,MAAM;QAAEC,QAAQ;QAAEC,MAAM;QAAEC;MAAc,CAAC,GAAG,IAAI,CAACzD,OAAO;MACxD,IAAIyD,aAAa,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;QACzCD,aAAa,CAACJ,KAAK,CAACC,QAAQ,CAAC;MACjC;MACA,IAAI,CAACpB,IAAI,CAACV,KAAK,CAACoB,GAAG,CAAC,IAAI,CAAC;MACzB,IAAI,CAAChD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,QAAQ,CAAC8C,GAAG,CAAC,IAAI,CAAC;MAC7C,IAAIzC,aAAa,KAAKqD,MAAM,IAAID,QAAQ,CAAC,EAAE;QACvC,IAAI,CAACpD,aAAa,GAAG,IAAI;MAC7B;MACA,IAAIlB,oBAAoB,EAAE;QACtB,IAAI0E,WAAW;QACf,MAAMC,mBAAmB,GAAGA,CAAA,KAAO,IAAI,CAAC1B,IAAI,CAAC1B,qBAAqB,GAAG,KAAM;QAC3EvB,oBAAoB,CAACqE,QAAQ,EAAE,MAAM;UACjC,IAAI,CAACpB,IAAI,CAAC1B,qBAAqB,GAAG,IAAI;UACtCmD,WAAW,IAAIA,WAAW,CAAC,CAAC;UAC5BA,WAAW,GAAGjG,KAAK,CAACkG,mBAAmB,EAAE,GAAG,CAAC;UAC7C,IAAInG,qBAAqB,CAACoG,sBAAsB,EAAE;YAC9CpG,qBAAqB,CAACoG,sBAAsB,GAAG,KAAK;YACpD,IAAI,CAACrC,KAAK,CAACC,OAAO,CAACqC,eAAe,CAAC;UACvC;QACJ,CAAC,CAAC;MACN;MACA,IAAIP,QAAQ,EAAE;QACV,IAAI,CAACrB,IAAI,CAAC6B,kBAAkB,CAACR,QAAQ,EAAE,IAAI,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACvD,OAAO,CAACgE,OAAO,KAAK,KAAK,IAC9BP,aAAa,KACZF,QAAQ,IAAIC,MAAM,CAAC,EAAE;QACtB,IAAI,CAAClB,gBAAgB,CAAC,WAAW,EAAE2B,KAAA,IAA+E;UAAA,IAA9E;YAAEC,KAAK;YAAEC,gBAAgB;YAAEC,wBAAwB;YAAEZ,MAAM,EAAEa;UAAW,CAAC,GAAAJ,KAAA;UACzG,IAAI,IAAI,CAACK,sBAAsB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,MAAM,GAAG5E,SAAS;YACvB,IAAI,CAAC6E,cAAc,GAAG7E,SAAS;YAC/B;UACJ;UACA;UACA,MAAM8E,gBAAgB,GAAG,IAAI,CAACzE,OAAO,CAAC0E,UAAU,IAC5CjB,aAAa,CAACkB,oBAAoB,CAAC,CAAC,IACpCC,uBAAuB;UAC3B,MAAM;YAAEC,sBAAsB;YAAEC;UAA2B,CAAC,GAAGrB,aAAa,CAACsB,QAAQ,CAAC,CAAC;UACvF;AACpB;AACA;AACA;UACoB,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACC,YAAY,IACpC,CAACrI,gBAAgB,CAAC,IAAI,CAACqI,YAAY,EAAEZ,SAAS,CAAC,IAC/CD,wBAAwB;UAC5B;AACpB;AACA;AACA;AACA;UACoB,MAAMc,4BAA4B,GAAG,CAACf,gBAAgB,IAAIC,wBAAwB;UAClF,IAAI,IAAI,CAACpE,OAAO,CAACmF,UAAU,IACtB,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC9B,QAAS,IAC7C4B,4BAA4B,IAC3Bf,gBAAgB,KACZa,aAAa,IAAI,CAAC,IAAI,CAACK,gBAAgB,CAAE,EAAE;YAChD,IAAI,IAAI,CAACD,UAAU,EAAE;cACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,UAAU;cACnC,IAAI,CAACE,YAAY,CAACA,YAAY,GAAG3F,SAAS;YAC9C;YACA,IAAI,CAAC4F,kBAAkB,CAACrB,KAAK,EAAEgB,4BAA4B,CAAC;YAC5D,MAAMM,gBAAgB,GAAAC,aAAA,CAAAA,aAAA,KACf9I,kBAAkB,CAAC8H,gBAAgB,EAAE,QAAQ,CAAC;cACjDiB,MAAM,EAAEb,sBAAsB;cAC9Bc,UAAU,EAAEb;YAAyB,EACxC;YACD,IAAIrB,aAAa,CAACmC,kBAAkB,IAChC,IAAI,CAAC5F,OAAO,CAACmF,UAAU,EAAE;cACzBK,gBAAgB,CAAC9H,KAAK,GAAG,CAAC;cAC1B8H,gBAAgB,CAAC7G,IAAI,GAAG,KAAK;YACjC;YACA,IAAI,CAACkH,cAAc,CAACL,gBAAgB,CAAC;UACzC,CAAC,MACI;YACD;AACxB;AACA;AACA;AACA;YACwB,IAAI,CAACrB,gBAAgB,EAAE;cACnBL,eAAe,CAAC,IAAI,CAAC;YACzB;YACA,IAAI,IAAI,CAACgC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC9F,OAAO,CAAC+F,cAAc,EAAE;cAC9C,IAAI,CAAC/F,OAAO,CAAC+F,cAAc,CAAC,CAAC;YACjC;UACJ;UACA,IAAI,CAACd,YAAY,GAAGZ,SAAS;QACjC,CAAC,CAAC;MACN;IACJ;IACA2B,OAAOA,CAAA,EAAG;MACN,IAAI,CAAChG,OAAO,CAACuD,QAAQ,IAAI,IAAI,CAAC0C,UAAU,CAAC,CAAC;MAC1C,IAAI,CAAC/D,IAAI,CAACV,KAAK,CAAC0E,MAAM,CAAC,IAAI,CAAC;MAC5B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACtG,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,QAAQ,CAACuG,MAAM,CAAC,IAAI,CAAC;MAChD,IAAI,CAAC/C,QAAQ,GAAG3D,SAAS;MACzB3B,WAAW,CAAC,IAAI,CAACuD,gBAAgB,CAAC;IACtC;IACA;IACA+E,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC/F,qBAAqB,GAAG,IAAI;IACrC;IACAgG,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAChG,qBAAqB,GAAG,KAAK;IACtC;IACAiG,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACjG,qBAAqB,IAAI,IAAI,CAACC,qBAAqB;IACnE;IACA8D,sBAAsBA,CAAA,EAAG;MACrB,OAAQ,IAAI,CAACpE,kBAAkB,IAC1B,IAAI,CAACN,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC0E,sBAAsB,CAAC,CAAE,IACrD,KAAK;IACb;IACA;IACAmC,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EACtB;MACJ,IAAI,CAAC/F,UAAU,GAAG,IAAI;MACtB,IAAI,CAACe,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,OAAO,CAACiF,aAAa,CAAC;MAC/C,IAAI,CAAC7G,WAAW,EAAE;IACtB;IACA8G,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAElD;MAAc,CAAC,GAAG,IAAI,CAACzD,OAAO;MACtC,OAAOyD,aAAa,IAAIA,aAAa,CAACsB,QAAQ,CAAC,CAAC,CAAC6B,iBAAiB;IACtE;IACAX,UAAUA,CAAA,EAA+B;MAAA,IAA9BY,qBAAqB,GAAApH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACnC,IAAI,CAACyC,IAAI,CAAChB,eAAe,GAAG,IAAI;MAChC,IAAI,IAAI,CAACgB,IAAI,CAACsE,eAAe,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACxG,OAAO,CAAC+F,cAAc,IAAI,IAAI,CAAC/F,OAAO,CAAC+F,cAAc,CAAC,CAAC;QAC5D;MACJ;MACA,CAAC,IAAI,CAAC7D,IAAI,CAACzB,UAAU,IAAI,IAAI,CAACyB,IAAI,CAACuE,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAACtG,aAAa,EAClB;MACJ,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACvC,MAAMyE,IAAI,GAAG,IAAI,CAAC3E,IAAI,CAACE,CAAC,CAAC;QACzByE,IAAI,CAAClG,oBAAoB,GAAG,IAAI;QAChCkG,IAAI,CAACC,YAAY,CAAC,UAAU,CAAC;QAC7B,IAAID,IAAI,CAAC9G,OAAO,CAACmF,UAAU,EAAE;UACzB2B,IAAI,CAACb,UAAU,CAAC,KAAK,CAAC;QAC1B;MACJ;MACA,MAAM;QAAE1C,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACxD,OAAO;MACzC,IAAIuD,QAAQ,KAAK5D,SAAS,IAAI,CAAC6D,MAAM,EACjC;MACJ,MAAMoD,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,CAACK,0BAA0B,GAAGJ,iBAAiB,GAC7CA,iBAAiB,CAAC,IAAI,CAACpH,YAAY,EAAE,EAAE,CAAC,GACxCG,SAAS;MACf,IAAI,CAACsH,cAAc,CAAC,CAAC;MACrBJ,qBAAqB,IAAI,IAAI,CAAChE,eAAe,CAAC,YAAY,CAAC;IAC/D;IACAqE,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC/F,eAAe,GAAG,KAAK;MAC5B,MAAMgG,gBAAgB,GAAG,IAAI,CAACX,eAAe,CAAC,CAAC;MAC/C;MACA;MACA;MACA,IAAIW,gBAAgB,EAAE;QAClB,IAAI,CAACZ,aAAa,CAAC,CAAC;QACpB,IAAI,CAACjF,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAAC2F,iBAAiB,CAAC;QACrC;MACJ;MACA,IAAI,CAAC,IAAI,CAAC3G,UAAU,EAAE;QAClB,IAAI,CAACe,KAAK,CAACC,OAAO,CAAC4F,kBAAkB,CAAC;MAC1C;MACA,IAAI,CAAC5G,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACe,KAAK,CAACC,OAAO,CAAC6F,mBAAmB,CAAC;MACvC;AACZ;AACA;MACY;MACA,IAAI,CAAC9F,KAAK,CAACC,OAAO,CAAC8F,YAAY,CAAC;MAChC;AACZ;AACA;MACY;MACA,IAAI,CAAC/F,KAAK,CAACC,OAAO,CAAC+F,kBAAkB,CAAC;MACtC,IAAI,CAAClG,iBAAiB,CAAC,CAAC;MACxB;AACZ;AACA;AACA;AACA;MACY,MAAMmG,GAAG,GAAGC,WAAW,CAACD,GAAG,CAAC,CAAC;MAC7BxJ,SAAS,CAACiG,KAAK,GAAGnG,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE0J,GAAG,GAAGxJ,SAAS,CAAC0J,SAAS,CAAC;MAChE1J,SAAS,CAAC0J,SAAS,GAAGF,GAAG;MACzBxJ,SAAS,CAAC2J,YAAY,GAAG,IAAI;MAC7B1J,KAAK,CAACgJ,MAAM,CAACW,OAAO,CAAC5J,SAAS,CAAC;MAC/BC,KAAK,CAAC4J,SAAS,CAACD,OAAO,CAAC5J,SAAS,CAAC;MAClCC,KAAK,CAAC6J,MAAM,CAACF,OAAO,CAAC5J,SAAS,CAAC;MAC/BA,SAAS,CAAC2J,YAAY,GAAG,KAAK;IAClC;IACAI,SAASA,CAAA,EAAG;MACR,IAAI,CAAC,IAAI,CAAC7G,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI;QAC3B8G,cAAc,CAAC,MAAM,IAAI,CAACf,MAAM,CAAC,CAAC,CAAC;MACvC;IACJ;IACA5F,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACyG,aAAa,CAAC;MACjC,IAAI,CAACjG,WAAW,CAACR,OAAO,CAAC0G,mBAAmB,CAAC;IACjD;IACAC,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAAChH,yBAAyB,EAAE;QACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;QACrCjD,KAAK,CAAC2J,SAAS,CAAC,IAAI,CAACvG,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC;MACvD;IACJ;IACA8G,yBAAyBA,CAAA,EAAG;MACxB;AACZ;AACA;AACA;AACA;MACYlK,KAAK,CAACmK,UAAU,CAAC,MAAM;QACnB,IAAI,IAAI,CAACnI,aAAa,EAAE;UACpB,IAAI,CAAC+B,IAAI,CAAC8F,SAAS,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAAC9F,IAAI,CAACb,iBAAiB,CAAC,CAAC;QACjC;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQ4F,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACsB,QAAQ,IAAI,CAAC,IAAI,CAACjF,QAAQ,EAC/B;MACJ,IAAI,CAACiF,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAClC;IACAjB,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAACjE,QAAQ,EACd;MACJ;MACA,IAAI,CAACyD,YAAY,CAAC,CAAC;MACnB,IAAI,EAAE,IAAI,CAAC/G,OAAO,CAACyI,mBAAmB,IAAI,IAAI,CAAC3C,MAAM,CAAC,CAAC,CAAC,IACpD,CAAC,IAAI,CAAC3F,aAAa,EAAE;QACrB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACiF,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAAC9B,QAAQ,EAAE;QAC9C,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;UACvC,MAAMyE,IAAI,GAAG,IAAI,CAAC3E,IAAI,CAACE,CAAC,CAAC;UACzByE,IAAI,CAACC,YAAY,CAAC,CAAC;QACvB;MACJ;MACA,MAAM2B,UAAU,GAAG,IAAI,CAAClF,MAAM;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI,CAACgF,OAAO,CAAC,KAAK,CAAC;MACjC,IAAI,CAACG,eAAe,GAAGlM,SAAS,CAAC,CAAC;MAClC,IAAI,CAAC0D,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACyI,eAAe,GAAGjJ,SAAS;MAChC,IAAI,CAACkD,eAAe,CAAC,SAAS,EAAE,IAAI,CAACW,MAAM,CAACqF,SAAS,CAAC;MACtD,MAAM;QAAEpF;MAAc,CAAC,GAAG,IAAI,CAACzD,OAAO;MACtCyD,aAAa,IACTA,aAAa,CAACN,MAAM,CAAC,eAAe,EAAE,IAAI,CAACK,MAAM,CAACqF,SAAS,EAAEH,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAGlJ,SAAS,CAAC;IACnH;IACAoH,YAAYA,CAAA,EAAoB;MAAA,IAAnB+B,KAAK,GAAArJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;MAC1B,IAAIsJ,gBAAgB,GAAGC,OAAO,CAAC,IAAI,CAAChJ,OAAO,CAACiJ,YAAY,IAAI,IAAI,CAAC3F,QAAQ,CAAC;MAC1E,IAAI,IAAI,CAAC4F,MAAM,IACX,IAAI,CAACA,MAAM,CAACrJ,WAAW,KAAK,IAAI,CAACqC,IAAI,CAACrC,WAAW,IACjD,IAAI,CAACqJ,MAAM,CAACJ,KAAK,KAAKA,KAAK,EAAE;QAC7BC,gBAAgB,GAAG,KAAK;MAC5B;MACA,IAAIA,gBAAgB,EAAE;QAClB,IAAI,CAACG,MAAM,GAAG;UACVrJ,WAAW,EAAE,IAAI,CAACqC,IAAI,CAACrC,WAAW;UAClCiJ,KAAK;UACLK,MAAM,EAAE/J,iBAAiB,CAAC,IAAI,CAACkE,QAAQ,CAAC;UACxC8F,MAAM,EAAEjK,aAAa,CAAC,IAAI,CAACmE,QAAQ;QACvC,CAAC;MACL;IACJ;IACAjE,cAAcA,CAAA,EAAG;MACb,IAAI,CAACA,cAAc,EACf;MACJ,MAAMgK,gBAAgB,GAAG,IAAI,CAAClJ,aAAa,IAAI,IAAI,CAACS,oBAAoB;MACxE,MAAM0I,aAAa,GAAG,IAAI,CAACV,eAAe,IAAI,CAAC/L,WAAW,CAAC,IAAI,CAAC+L,eAAe,CAAC;MAChF,MAAMhC,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,MAAM4C,sBAAsB,GAAG3C,iBAAiB,GAC1CA,iBAAiB,CAAC,IAAI,CAACpH,YAAY,EAAE,EAAE,CAAC,GACxCG,SAAS;MACf,MAAM6J,2BAA2B,GAAGD,sBAAsB,KAAK,IAAI,CAACvC,0BAA0B;MAC9F,IAAIqC,gBAAgB,KACfC,aAAa,IACVlM,YAAY,CAAC,IAAI,CAACoC,YAAY,CAAC,IAC/BgK,2BAA2B,CAAC,EAAE;QAClCnK,cAAc,CAAC,IAAI,CAACiE,QAAQ,EAAEiG,sBAAsB,CAAC;QACrD,IAAI,CAAC3I,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC6I,cAAc,CAAC,CAAC;MACzB;IACJ;IACAjB,OAAOA,CAAA,EAAyB;MAAA,IAAxBkB,eAAe,GAAAjK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC1B,MAAMkK,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACrC,IAAIf,SAAS,GAAG,IAAI,CAACgB,mBAAmB,CAACF,OAAO,CAAC;MACjD;AACZ;AACA;AACA;AACA;MACY,IAAID,eAAe,EAAE;QACjBb,SAAS,GAAG,IAAI,CAACa,eAAe,CAACb,SAAS,CAAC;MAC/C;MACAiB,QAAQ,CAACjB,SAAS,CAAC;MACnB,OAAO;QACHhJ,WAAW,EAAE,IAAI,CAACqC,IAAI,CAACrC,WAAW;QAClCkK,WAAW,EAAEJ,OAAO;QACpBd,SAAS;QACTrJ,YAAY,EAAE,CAAC,CAAC;QAChBwK,MAAM,EAAE,IAAI,CAACvL;MACjB,CAAC;IACL;IACAmL,cAAcA,CAAA,EAAG;MACb,MAAM;QAAEnG;MAAc,CAAC,GAAG,IAAI,CAACzD,OAAO;MACtC,IAAI,CAACyD,aAAa,EACd,OAAOhH,SAAS,CAAC,CAAC;MACtB,MAAMwN,GAAG,GAAGxG,aAAa,CAACyG,kBAAkB,CAAC,CAAC;MAC9C;MACA,MAAM;QAAEhB;MAAO,CAAC,GAAG,IAAI,CAAChH,IAAI;MAC5B,IAAIgH,MAAM,EAAE;QACRnN,aAAa,CAACkO,GAAG,CAACnJ,CAAC,EAAEoI,MAAM,CAACE,MAAM,CAACtI,CAAC,CAAC;QACrC/E,aAAa,CAACkO,GAAG,CAAClJ,CAAC,EAAEmI,MAAM,CAACE,MAAM,CAACrI,CAAC,CAAC;MACzC;MACA,OAAOkJ,GAAG;IACd;IACAJ,mBAAmBA,CAACI,GAAG,EAAE;MACrB,MAAME,gBAAgB,GAAG1N,SAAS,CAAC,CAAC;MACpCX,WAAW,CAACqO,gBAAgB,EAAEF,GAAG,CAAC;MAClC;AACZ;AACA;AACA;MACY,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACvC,MAAMyE,IAAI,GAAG,IAAI,CAAC3E,IAAI,CAACE,CAAC,CAAC;QACzB,MAAM;UAAE6G,MAAM;UAAElJ;QAAQ,CAAC,GAAG8G,IAAI;QAChC,IAAIA,IAAI,KAAK,IAAI,CAAC5E,IAAI,IAAIgH,MAAM,IAAIlJ,OAAO,CAACiJ,YAAY,EAAE;UACtD;AACpB;AACA;AACA;UACoB,IAAIC,MAAM,CAACC,MAAM,EAAE;YACfrN,WAAW,CAACqO,gBAAgB,EAAEF,GAAG,CAAC;YAClC,MAAM;cAAEf,MAAM,EAAEkB;YAAW,CAAC,GAAG,IAAI,CAAClI,IAAI;YACxC;AACxB;AACA;AACA;YACwB,IAAIkI,UAAU,EAAE;cACZrO,aAAa,CAACoO,gBAAgB,CAACrJ,CAAC,EAAE,CAACsJ,UAAU,CAAChB,MAAM,CAACtI,CAAC,CAAC;cACvD/E,aAAa,CAACoO,gBAAgB,CAACpJ,CAAC,EAAE,CAACqJ,UAAU,CAAChB,MAAM,CAACrI,CAAC,CAAC;YAC3D;UACJ;UACAhF,aAAa,CAACoO,gBAAgB,CAACrJ,CAAC,EAAEoI,MAAM,CAACE,MAAM,CAACtI,CAAC,CAAC;UAClD/E,aAAa,CAACoO,gBAAgB,CAACpJ,CAAC,EAAEmI,MAAM,CAACE,MAAM,CAACrI,CAAC,CAAC;QACtD;MACJ;MACA,OAAOoJ,gBAAgB;IAC3B;IACAE,cAAcA,CAACJ,GAAG,EAAyB;MAAA,IAAvBK,aAAa,GAAA7K,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACrC,MAAM8K,cAAc,GAAG9N,SAAS,CAAC,CAAC;MAClCX,WAAW,CAACyO,cAAc,EAAEN,GAAG,CAAC;MAChC,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACvC,MAAMyE,IAAI,GAAG,IAAI,CAAC3E,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAACiI,aAAa,IACdxD,IAAI,CAAC9G,OAAO,CAACiJ,YAAY,IACzBnC,IAAI,CAACoC,MAAM,IACXpC,IAAI,KAAKA,IAAI,CAAC5E,IAAI,EAAE;UACpBlG,YAAY,CAACuO,cAAc,EAAE;YACzBzJ,CAAC,EAAE,CAACgG,IAAI,CAACoC,MAAM,CAACE,MAAM,CAACtI,CAAC;YACxBC,CAAC,EAAE,CAAC+F,IAAI,CAACoC,MAAM,CAACE,MAAM,CAACrI;UAC3B,CAAC,CAAC;QACN;QACA,IAAI,CAAC3D,YAAY,CAAC0J,IAAI,CAACtH,YAAY,CAAC,EAChC;QACJxD,YAAY,CAACuO,cAAc,EAAEzD,IAAI,CAACtH,YAAY,CAAC;MACnD;MACA,IAAIpC,YAAY,CAAC,IAAI,CAACoC,YAAY,CAAC,EAAE;QACjCxD,YAAY,CAACuO,cAAc,EAAE,IAAI,CAAC/K,YAAY,CAAC;MACnD;MACA,OAAO+K,cAAc;IACzB;IACAb,eAAeA,CAACO,GAAG,EAAE;MACjB,MAAMO,mBAAmB,GAAG/N,SAAS,CAAC,CAAC;MACvCX,WAAW,CAAC0O,mBAAmB,EAAEP,GAAG,CAAC;MACrC,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACvC,MAAMyE,IAAI,GAAG,IAAI,CAAC3E,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAACyE,IAAI,CAACxD,QAAQ,EACd;QACJ,IAAI,CAAClG,YAAY,CAAC0J,IAAI,CAACtH,YAAY,CAAC,EAChC;QACJnC,QAAQ,CAACyJ,IAAI,CAACtH,YAAY,CAAC,IAAIsH,IAAI,CAACG,cAAc,CAAC,CAAC;QACpD,MAAMwD,SAAS,GAAGhO,SAAS,CAAC,CAAC;QAC7B,MAAMiO,OAAO,GAAG5D,IAAI,CAAC8C,cAAc,CAAC,CAAC;QACrC9N,WAAW,CAAC2O,SAAS,EAAEC,OAAO,CAAC;QAC/BlO,mBAAmB,CAACgO,mBAAmB,EAAE1D,IAAI,CAACtH,YAAY,EAAEsH,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAACyB,QAAQ,CAACM,SAAS,GAAGlJ,SAAS,EAAE8K,SAAS,CAAC;MAC/H;MACA,IAAIrN,YAAY,CAAC,IAAI,CAACoC,YAAY,CAAC,EAAE;QACjChD,mBAAmB,CAACgO,mBAAmB,EAAE,IAAI,CAAChL,YAAY,CAAC;MAC/D;MACA,OAAOgL,mBAAmB;IAC9B;IACAG,cAAcA,CAACzG,KAAK,EAAE;MAClB,IAAI,CAAC0G,WAAW,GAAG1G,KAAK;MACxB,IAAI,CAAChC,IAAI,CAACkG,wBAAwB,CAAC,CAAC;MACpC,IAAI,CAAChI,iBAAiB,GAAG,IAAI;IACjC;IACAyK,UAAUA,CAAC7K,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAAyF,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACL,IAAI,CAACzF,OAAO,GACZA,OAAO;QACV8K,SAAS,EAAE9K,OAAO,CAAC8K,SAAS,KAAKnL,SAAS,GAAGK,OAAO,CAAC8K,SAAS,GAAG;MAAI,EACxE;IACL;IACA1D,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC8B,MAAM,GAAGvJ,SAAS;MACvB,IAAI,CAAC6D,MAAM,GAAG7D,SAAS;MACvB,IAAI,CAAC4I,QAAQ,GAAG5I,SAAS;MACzB,IAAI,CAACqH,0BAA0B,GAAGrH,SAAS;MAC3C,IAAI,CAACiL,WAAW,GAAGjL,SAAS;MAC5B,IAAI,CAAC4E,MAAM,GAAG5E,SAAS;MACvB,IAAI,CAACQ,aAAa,GAAG,KAAK;IAC9B;IACA4K,kCAAkCA,CAAA,EAAG;MACjC,IAAI,CAAC,IAAI,CAACC,cAAc,EACpB;MACJ;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACA,cAAc,CAACC,wBAAwB,KAC5ChN,SAAS,CAAC0J,SAAS,EAAE;QACrB,IAAI,CAACqD,cAAc,CAACrJ,kBAAkB,CAAC,IAAI,CAAC;MAChD;IACJ;IACAA,kBAAkBA,CAAA,EAA6B;MAAA,IAA5BuJ,kBAAkB,GAAAzL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACzC,IAAI0L,EAAE;MACN;AACZ;AACA;AACA;AACA;MACY,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACjL,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,GAAGgL,IAAI,CAAChL,iBAAiB,CAAC;MAC3E,IAAI,CAACE,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAG8K,IAAI,CAAC9K,gBAAgB,CAAC;MACxE,IAAI,CAACD,uBAAuB,KAAK,IAAI,CAACA,uBAAuB,GAAG+K,IAAI,CAAC/K,uBAAuB,CAAC;MAC7F,MAAMiL,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,IAAI,KAAK8F,IAAI;MAC5D;AACZ;AACA;AACA;MACY,MAAMG,OAAO,GAAG,EAAEL,kBAAkB,IAC/BI,QAAQ,IAAI,IAAI,CAACjL,uBAAwB,IAC1C,IAAI,CAACD,iBAAiB,KACrB,CAAC+K,EAAE,GAAG,IAAI,CAACvL,MAAM,MAAM,IAAI,IAAIuL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/K,iBAAiB,CAAC,IAC9E,IAAI,CAACoL,8BAA8B,CAAC;MACxC,IAAID,OAAO,EACP;MACJ,MAAM;QAAE/H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACvD,OAAO;MACzC;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACwD,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ,IAAI,CAAC0H,wBAAwB,GAAGhN,SAAS,CAAC0J,SAAS;MACnD;AACZ;AACA;AACA;AACA;MACY;MACA,IAAI,CAAC,IAAI,CAACiD,WAAW,IAAI,CAAC,IAAI,CAACpG,cAAc,EAAE;QAC3C;QACA,MAAMwG,cAAc,GAAG,IAAI,CAACS,0BAA0B,CAAC,CAAC;QACxD,IAAIT,cAAc,IACdA,cAAc,CAACxH,MAAM,IACrB,IAAI,CAACxB,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAACgJ,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACvG,cAAc,GAAG/H,SAAS,CAAC,CAAC;UACjC,IAAI,CAACiP,oBAAoB,GAAGjP,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACuP,oBAAoB,EAAE,IAAI,CAAClI,MAAM,CAACqF,SAAS,EAAEmC,cAAc,CAACxH,MAAM,CAACqF,SAAS,CAAC;UACvG/M,WAAW,CAAC,IAAI,CAAC0I,cAAc,EAAE,IAAI,CAACkH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACV,cAAc,GAAG,IAAI,CAACxG,cAAc,GAAG7E,SAAS;QACzD;MACJ;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAAC6E,cAAc,IAAI,CAAC,IAAI,CAACoG,WAAW,EACzC;MACJ;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACrG,MAAM,EAAE;QACd,IAAI,CAACA,MAAM,GAAG9H,SAAS,CAAC,CAAC;QACzB,IAAI,CAACkP,oBAAoB,GAAGlP,SAAS,CAAC,CAAC;MAC3C;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC+H,cAAc,IACnB,IAAI,CAACkH,oBAAoB,IACzB,IAAI,CAACV,cAAc,IACnB,IAAI,CAACA,cAAc,CAACzG,MAAM,EAAE;QAC5B,IAAI,CAACwG,kCAAkC,CAAC,CAAC;QACzC3O,eAAe,CAAC,IAAI,CAACmI,MAAM,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACwG,cAAc,CAACzG,MAAM,CAAC;QAC7E;AAChB;AACA;MACY,CAAC,MACI,IAAI,IAAI,CAACqG,WAAW,EAAE;QACvB,IAAI5B,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,EAAE;UAC5B;UACA,IAAI,CAACf,MAAM,GAAG,IAAI,CAAC8F,cAAc,CAAC,IAAI,CAAC7G,MAAM,CAACqF,SAAS,CAAC;QAC5D,CAAC,MACI;UACD/M,WAAW,CAAC,IAAI,CAACyI,MAAM,EAAE,IAAI,CAACf,MAAM,CAACqF,SAAS,CAAC;QACnD;QACA5M,aAAa,CAAC,IAAI,CAACsI,MAAM,EAAE,IAAI,CAACqG,WAAW,CAAC;MAChD,CAAC,MACI;QACD;AAChB;AACA;QACgB9O,WAAW,CAAC,IAAI,CAACyI,MAAM,EAAE,IAAI,CAACf,MAAM,CAACqF,SAAS,CAAC;MACnD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC2C,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,MAAMR,cAAc,GAAG,IAAI,CAACS,0BAA0B,CAAC,CAAC;QACxD,IAAIT,cAAc,IACdhC,OAAO,CAACgC,cAAc,CAAC1F,YAAY,CAAC,KAChC0D,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAC9B,CAAC0F,cAAc,CAAChL,OAAO,CAACiJ,YAAY,IACpC+B,cAAc,CAACzG,MAAM,IACrB,IAAI,CAACvC,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAACgJ,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACvG,cAAc,GAAG/H,SAAS,CAAC,CAAC;UACjC,IAAI,CAACiP,oBAAoB,GAAGjP,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACuP,oBAAoB,EAAE,IAAI,CAACnH,MAAM,EAAEyG,cAAc,CAACzG,MAAM,CAAC;UACnFzI,WAAW,CAAC,IAAI,CAAC0I,cAAc,EAAE,IAAI,CAACkH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACV,cAAc,GAAG,IAAI,CAACxG,cAAc,GAAG7E,SAAS;QACzD;MACJ;MACA;AACZ;AACA;MACYjB,mBAAmB,CAACG,oBAAoB,EAAE;IAC9C;IACA4M,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC7L,MAAM,IACZvC,QAAQ,CAAC,IAAI,CAACuC,MAAM,CAACJ,YAAY,CAAC,IAClClC,cAAc,CAAC,IAAI,CAACsC,MAAM,CAACJ,YAAY,CAAC,EAAE;QAC1C,OAAOG,SAAS;MACpB;MACA,IAAI,IAAI,CAACC,MAAM,CAACgM,YAAY,CAAC,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAChM,MAAM;MACtB,CAAC,MACI;QACD,OAAO,IAAI,CAACA,MAAM,CAAC6L,0BAA0B,CAAC,CAAC;MACnD;IACJ;IACAG,YAAYA,CAAA,EAAG;MACX,OAAO5C,OAAO,CAAC,CAAC,IAAI,CAACxE,cAAc,IAC/B,IAAI,CAACoG,WAAW,IAChB,IAAI,CAAC5K,OAAO,CAACmF,UAAU,KACvB,IAAI,CAAC3B,MAAM,CAAC;IACpB;IACA5B,cAAcA,CAAA,EAAG;MACb,IAAIuJ,EAAE;MACN,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,MAAMC,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAC,IAAI,IAAI,KAAK8F,IAAI;MAC5D,IAAIG,OAAO,GAAG,IAAI;MAClB;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACnL,iBAAiB,KAAK,CAAC+K,EAAE,GAAG,IAAI,CAACvL,MAAM,MAAM,IAAI,IAAIuL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/K,iBAAiB,CAAC,EAAE;QAC1GmL,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAID,QAAQ,KACP,IAAI,CAACjL,uBAAuB,IAAI,IAAI,CAACC,gBAAgB,CAAC,EAAE;QACzDiL,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACN,wBAAwB,KAAKhN,SAAS,CAAC0J,SAAS,EAAE;QACvD4D,OAAO,GAAG,KAAK;MACnB;MACA,IAAIA,OAAO,EACP;MACJ,MAAM;QAAE/H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACvD,OAAO;MACzC;AACZ;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG+I,OAAO,CAAE,IAAI,CAACpJ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACK,eAAe,IACtE,IAAI,CAACoF,gBAAgB,IACrB,IAAI,CAACwG,gBAAgB,CAAC;MAC1B,IAAI,CAAC,IAAI,CAAC5L,eAAe,EAAE;QACvB,IAAI,CAAC2K,WAAW,GAAG,IAAI,CAACpG,cAAc,GAAG7E,SAAS;MACtD;MACA,IAAI,CAAC,IAAI,CAAC6D,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;MACYzH,WAAW,CAAC,IAAI,CAAC6M,eAAe,EAAE,IAAI,CAACnF,MAAM,CAACqF,SAAS,CAAC;MACxD;AACZ;AACA;MACY,MAAMiD,cAAc,GAAG,IAAI,CAACjL,SAAS,CAACC,CAAC;MACvC,MAAMiL,cAAc,GAAG,IAAI,CAAClL,SAAS,CAACE,CAAC;MACvC;AACZ;AACA;AACA;MACY7E,eAAe,CAAC,IAAI,CAACyM,eAAe,EAAE,IAAI,CAAC9H,SAAS,EAAE,IAAI,CAACsB,IAAI,EAAEmJ,QAAQ,CAAC;MAC1E;AACZ;AACA;AACA;MACY,IAAIF,IAAI,CAAC5H,MAAM,IACX,CAAC4H,IAAI,CAAC7G,MAAM,KACX,IAAI,CAAC1D,SAAS,CAACC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACD,SAAS,CAACE,CAAC,KAAK,CAAC,CAAC,EAAE;QACpDqK,IAAI,CAAC7G,MAAM,GAAG6G,IAAI,CAAC5H,MAAM,CAACqF,SAAS;MACvC;MACA,MAAM;QAAEtE;MAAO,CAAC,GAAG6G,IAAI;MACvB,IAAI,CAAC7G,MAAM,EAAE;QACT;AAChB;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACyH,mBAAmB,EAAE;UAC1B,IAAI,CAACpD,eAAe,GAAGlM,WAAW,CAAC,CAAC;UACpC,IAAI,CAACsP,mBAAmB,GAAG,MAAM;UACjC,IAAI,CAACvC,cAAc,CAAC,CAAC;QACzB;QACA;MACJ;MACA,IAAI,CAAC,IAAI,CAACb,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAGlM,WAAW,CAAC,CAAC;QACpC,IAAI,CAACuP,4BAA4B,GAAGvP,WAAW,CAAC,CAAC;MACrD;MACA,MAAMwP,uBAAuB,GAAG,IAAI,CAACF,mBAAmB;MACxD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY3P,YAAY,CAAC,IAAI,CAACuM,eAAe,EAAE,IAAI,CAACD,eAAe,EAAEpE,MAAM,EAAE,IAAI,CAAC/E,YAAY,CAAC;MACnF,IAAI,CAACwM,mBAAmB,GAAG9O,wBAAwB,CAAC,IAAI,CAAC0L,eAAe,EAAE,IAAI,CAAC/H,SAAS,CAAC;MACzF,IAAI,IAAI,CAACmL,mBAAmB,KAAKE,uBAAuB,IACpD,IAAI,CAACrL,SAAS,CAACC,CAAC,KAAKgL,cAAc,IACnC,IAAI,CAACjL,SAAS,CAACE,CAAC,KAAKgL,cAAc,EAAE;QACrC,IAAI,CAACjK,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC2H,cAAc,CAAC,CAAC;QACrB,IAAI,CAAC5G,eAAe,CAAC,kBAAkB,EAAE0B,MAAM,CAAC;MACpD;MACA;AACZ;AACA;MACY7F,mBAAmB,CAACI,sBAAsB,EAAE;IAChD;IACAqN,IAAIA,CAAA,EAAG;MACH,IAAI,CAACpK,SAAS,GAAG,KAAK;MACtB;IACJ;IACAqK,IAAIA,CAAA,EAAG;MACH,IAAI,CAACrK,SAAS,GAAG,IAAI;MACrB;IACJ;IACA0H,cAAcA,CAAA,EAAmB;MAAA,IAAlB4C,SAAS,GAAA5M,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC3B,IAAI,CAACO,OAAO,CAACyJ,cAAc,IAAI,IAAI,CAACzJ,OAAO,CAACyJ,cAAc,CAAC,CAAC;MAC5D,IAAI4C,SAAS,EAAE;QACX,MAAMlG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC7BD,KAAK,IAAIA,KAAK,CAACsD,cAAc,CAAC,CAAC;MACnC;MACA,IAAI,IAAI,CAACnE,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAAChC,QAAQ,EAAE;QAClD,IAAI,CAACgC,YAAY,GAAG3F,SAAS;MACjC;IACJ;IACA4F,kBAAkBA,CAACrB,KAAK,EAAwC;MAAA,IAAtCgB,4BAA4B,GAAAzF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAC1D,MAAM8I,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAM+D,oBAAoB,GAAG/D,QAAQ,GAC/BA,QAAQ,CAAC/I,YAAY,GACrB,CAAC,CAAC;MACR,MAAM+M,WAAW,GAAA9G,aAAA,KAAQ,IAAI,CAACjG,YAAY,CAAE;MAC5C,MAAMoL,WAAW,GAAGlO,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC,IAAI,CAACsO,cAAc,IACpB,CAAC,IAAI,CAACA,cAAc,CAAChL,OAAO,CAACmF,UAAU,EAAE;QACzC,IAAI,CAACX,cAAc,GAAG,IAAI,CAACkH,oBAAoB,GAAG/L,SAAS;MAC/D;MACA,IAAI,CAAC6L,8BAA8B,GAAG,CAACtG,4BAA4B;MACnE,MAAMsH,cAAc,GAAG/P,SAAS,CAAC,CAAC;MAClC,MAAMgQ,cAAc,GAAGlE,QAAQ,GAAGA,QAAQ,CAACyB,MAAM,GAAGrK,SAAS;MAC7D,MAAM+M,YAAY,GAAG,IAAI,CAAClJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACwG,MAAM,GAAGrK,SAAS;MACjE,MAAMgN,uBAAuB,GAAGF,cAAc,KAAKC,YAAY;MAC/D,MAAMvG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMwG,YAAY,GAAG,CAACzG,KAAK,IAAIA,KAAK,CAAC0G,OAAO,CAACnN,MAAM,IAAI,CAAC;MACxD,MAAMoN,sBAAsB,GAAG9D,OAAO,CAAC2D,uBAAuB,IAC1D,CAACC,YAAY,IACb,IAAI,CAAC5M,OAAO,CAAC8K,SAAS,KAAK,IAAI,IAC/B,CAAC,IAAI,CAAC3I,IAAI,CAAC4K,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACzC,IAAI,CAAChL,iBAAiB,GAAG,CAAC;MAC1B,IAAIiL,kBAAkB;MACtB,IAAI,CAACC,cAAc,GAAIC,MAAM,IAAK;QAC9B,MAAMC,QAAQ,GAAGD,MAAM,GAAG,IAAI;QAC9BE,YAAY,CAACzC,WAAW,CAAC9J,CAAC,EAAEoD,KAAK,CAACpD,CAAC,EAAEsM,QAAQ,CAAC;QAC9CC,YAAY,CAACzC,WAAW,CAAC7J,CAAC,EAAEmD,KAAK,CAACnD,CAAC,EAAEqM,QAAQ,CAAC;QAC9C,IAAI,CAACzC,cAAc,CAACC,WAAW,CAAC;QAChC,IAAI,IAAI,CAACpG,cAAc,IACnB,IAAI,CAACkH,oBAAoB,IACzB,IAAI,CAAClI,MAAM,IACX,IAAI,CAACwH,cAAc,IACnB,IAAI,CAACA,cAAc,CAACxH,MAAM,EAAE;UAC5BrH,oBAAoB,CAACqQ,cAAc,EAAE,IAAI,CAAChJ,MAAM,CAACqF,SAAS,EAAE,IAAI,CAACmC,cAAc,CAACxH,MAAM,CAACqF,SAAS,CAAC;UACjGyE,MAAM,CAAC,IAAI,CAAC9I,cAAc,EAAE,IAAI,CAACkH,oBAAoB,EAAEc,cAAc,EAAEY,QAAQ,CAAC;UAChF;AACpB;AACA;AACA;UACoB,IAAIH,kBAAkB,IAClBlQ,SAAS,CAAC,IAAI,CAACyH,cAAc,EAAEyI,kBAAkB,CAAC,EAAE;YACpD,IAAI,CAAC7M,iBAAiB,GAAG,KAAK;UAClC;UACA,IAAI,CAAC6M,kBAAkB,EACnBA,kBAAkB,GAAGxQ,SAAS,CAAC,CAAC;UACpCX,WAAW,CAACmR,kBAAkB,EAAE,IAAI,CAACzI,cAAc,CAAC;QACxD;QACA,IAAImI,uBAAuB,EAAE;UACzB,IAAI,CAACY,eAAe,GAAGhB,WAAW;UAClC1Q,SAAS,CAAC0Q,WAAW,EAAED,oBAAoB,EAAE,IAAI,CAAC9M,YAAY,EAAE4N,QAAQ,EAAEN,sBAAsB,EAAEF,YAAY,CAAC;QACnH;QACA,IAAI,CAAC1K,IAAI,CAACkG,wBAAwB,CAAC,CAAC;QACpC,IAAI,CAACqB,cAAc,CAAC,CAAC;QACrB,IAAI,CAACzH,iBAAiB,GAAGoL,QAAQ;MACrC,CAAC;MACD,IAAI,CAACF,cAAc,CAAC,IAAI,CAAClN,OAAO,CAACmF,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;IAC3D;IACAU,cAAcA,CAAC7F,OAAO,EAAE;MACpB,IAAI,CAAC6C,eAAe,CAAC,gBAAgB,CAAC;MACtC,IAAI,CAACwC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACmI,IAAI,CAAC,CAAC;MACrD,IAAI,IAAI,CAAClI,YAAY,IAAI,IAAI,CAACA,YAAY,CAACD,gBAAgB,EAAE;QACzD,IAAI,CAACC,YAAY,CAACD,gBAAgB,CAACmI,IAAI,CAAC,CAAC;MAC7C;MACA,IAAI,IAAI,CAAC3B,gBAAgB,EAAE;QACvB7N,WAAW,CAAC,IAAI,CAAC6N,gBAAgB,CAAC;QAClC,IAAI,CAACA,gBAAgB,GAAGlM,SAAS;MACrC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACkM,gBAAgB,GAAG1N,KAAK,CAAC+I,MAAM,CAAC,MAAM;QACvCzJ,qBAAqB,CAACoG,sBAAsB,GAAG,IAAI;QACnD,IAAI,CAACwB,gBAAgB,GAAGvH,kBAAkB,CAAC,CAAC,EAAEU,eAAe,EAAAiH,aAAA,CAAAA,aAAA,KACtDzF,OAAO;UACVyN,QAAQ,EAAGN,MAAM,IAAK;YAClB,IAAI,CAACD,cAAc,CAACC,MAAM,CAAC;YAC3BnN,OAAO,CAACyN,QAAQ,IAAIzN,OAAO,CAACyN,QAAQ,CAACN,MAAM,CAAC;UAChD,CAAC;UACDxH,UAAU,EAAEA,CAAA,KAAM;YACd3F,OAAO,CAAC2F,UAAU,IAAI3F,OAAO,CAAC2F,UAAU,CAAC,CAAC;YAC1C,IAAI,CAAC+H,iBAAiB,CAAC,CAAC;UAC5B;QAAC,EACJ,CAAC;QACF,IAAI,IAAI,CAACpI,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;QAC9D;QACA,IAAI,CAACwG,gBAAgB,GAAGlM,SAAS;MACrC,CAAC,CAAC;IACN;IACA+N,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACpI,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG1F,SAAS;QAC9C,IAAI,CAAC2F,YAAY,CAACqI,eAAe,GAAGhO,SAAS;MACjD;MACA,MAAMwG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACyH,qBAAqB,CAAC,CAAC;MACtC,IAAI,CAACtI,YAAY,GACb,IAAI,CAACD,gBAAgB,GACjB,IAAI,CAACkI,eAAe,GAChB5N,SAAS;MACrB,IAAI,CAACkD,eAAe,CAAC,mBAAmB,CAAC;IAC7C;IACAiB,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACuB,gBAAgB,EAAE;QACvB,IAAI,CAAC6H,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC1O,eAAe,CAAC;QAC3D,IAAI,CAAC6G,gBAAgB,CAACmI,IAAI,CAAC,CAAC;MAChC;MACA,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAC5B;IACAG,uBAAuBA,CAAA,EAAG;MACtB,MAAMzC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI;QAAEM,oBAAoB;QAAEpH,MAAM;QAAEf,MAAM;QAAEhE;MAAa,CAAC,GAAG4L,IAAI;MACjE,IAAI,CAACO,oBAAoB,IAAI,CAACpH,MAAM,IAAI,CAACf,MAAM,EAC3C;MACJ;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,KAAK4H,IAAI,IACb,IAAI,CAAC5H,MAAM,IACXA,MAAM,IACNsK,yBAAyB,CAAC,IAAI,CAAC9N,OAAO,CAAC+N,aAAa,EAAE,IAAI,CAACvK,MAAM,CAACqF,SAAS,EAAErF,MAAM,CAACqF,SAAS,CAAC,EAAE;QAChGtE,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI9H,SAAS,CAAC,CAAC;QACnC,MAAMuR,OAAO,GAAG1R,UAAU,CAAC,IAAI,CAACkH,MAAM,CAACqF,SAAS,CAAC/H,CAAC,CAAC;QACnDyD,MAAM,CAACzD,CAAC,CAACmN,GAAG,GAAG7C,IAAI,CAAC7G,MAAM,CAACzD,CAAC,CAACmN,GAAG;QAChC1J,MAAM,CAACzD,CAAC,CAACoN,GAAG,GAAG3J,MAAM,CAACzD,CAAC,CAACmN,GAAG,GAAGD,OAAO;QACrC,MAAMG,OAAO,GAAG7R,UAAU,CAAC,IAAI,CAACkH,MAAM,CAACqF,SAAS,CAAC9H,CAAC,CAAC;QACnDwD,MAAM,CAACxD,CAAC,CAACkN,GAAG,GAAG7C,IAAI,CAAC7G,MAAM,CAACxD,CAAC,CAACkN,GAAG;QAChC1J,MAAM,CAACxD,CAAC,CAACmN,GAAG,GAAG3J,MAAM,CAACxD,CAAC,CAACkN,GAAG,GAAGE,OAAO;MACzC;MACArS,WAAW,CAAC6P,oBAAoB,EAAEpH,MAAM,CAAC;MACzC;AACZ;AACA;AACA;AACA;MACYvI,YAAY,CAAC2P,oBAAoB,EAAEnM,YAAY,CAAC;MAChD;AACZ;AACA;AACA;AACA;AACA;MACYnD,YAAY,CAAC,IAAI,CAAC4P,4BAA4B,EAAE,IAAI,CAACtD,eAAe,EAAEgD,oBAAoB,EAAEnM,YAAY,CAAC;IAC7G;IACAuE,kBAAkBA,CAACR,QAAQ,EAAEuD,IAAI,EAAE;MAC/B,IAAI,CAAC,IAAI,CAAC7E,WAAW,CAACQ,GAAG,CAACc,QAAQ,CAAC,EAAE;QACjC,IAAI,CAACtB,WAAW,CAACS,GAAG,CAACa,QAAQ,EAAE,IAAIvG,SAAS,CAAC,CAAC,CAAC;MACnD;MACA,MAAMmJ,KAAK,GAAG,IAAI,CAAClE,WAAW,CAACU,GAAG,CAACY,QAAQ,CAAC;MAC5C4C,KAAK,CAACvD,GAAG,CAACkE,IAAI,CAAC;MACf,MAAMsH,MAAM,GAAGtH,IAAI,CAAC9G,OAAO,CAACqO,sBAAsB;MAClDvH,IAAI,CAACwH,OAAO,CAAC;QACT5J,UAAU,EAAE0J,MAAM,GAAGA,MAAM,CAAC1J,UAAU,GAAG/E,SAAS;QAClD4O,qBAAqB,EAAEH,MAAM,IAAIA,MAAM,CAACI,2BAA2B,GAC7DJ,MAAM,CAACI,2BAA2B,CAAC1H,IAAI,CAAC,GACxCnH;MACV,CAAC,CAAC;IACN;IACAmG,MAAMA,CAAA,EAAG;MACL,MAAMK,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,OAAOD,KAAK,GAAGA,KAAK,CAACiF,IAAI,KAAK,IAAI,GAAG,IAAI;IAC7C;IACAC,OAAOA,CAAA,EAAG;MACN,IAAIF,EAAE;MACN,MAAM;QAAE5H;MAAS,CAAC,GAAG,IAAI,CAACvD,OAAO;MACjC,OAAOuD,QAAQ,GAAG,CAAC,CAAC4H,EAAE,GAAG,IAAI,CAAC/E,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI+E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,KAAK,IAAI,GAAG,IAAI;IAC1G;IACAqD,WAAWA,CAAA,EAAG;MACV,IAAItD,EAAE;MACN,MAAM;QAAE5H;MAAS,CAAC,GAAG,IAAI,CAACvD,OAAO;MACjC,OAAOuD,QAAQ,GAAG,CAAC4H,EAAE,GAAG,IAAI,CAAC/E,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI+E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuD,QAAQ,GAAG/O,SAAS;IACzG;IACAyG,QAAQA,CAAA,EAAG;MACP,MAAM;QAAE7C;MAAS,CAAC,GAAG,IAAI,CAACvD,OAAO;MACjC,IAAIuD,QAAQ,EACR,OAAO,IAAI,CAACrB,IAAI,CAACD,WAAW,CAACU,GAAG,CAACY,QAAQ,CAAC;IAClD;IACA+K,OAAOA,CAAA,EAA0D;MAAA,IAAzD;QAAE3N,UAAU;QAAE+D,UAAU;QAAE6J;MAAuB,CAAC,GAAA9O,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC3D,MAAM0G,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EACLA,KAAK,CAACmI,OAAO,CAAC,IAAI,EAAEC,qBAAqB,CAAC;MAC9C,IAAI5N,UAAU,EAAE;QACZ,IAAI,CAACiI,eAAe,GAAGjJ,SAAS;QAChC,IAAI,CAACgB,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI+D,UAAU,EACV,IAAI,CAACmG,UAAU,CAAC;QAAEnG;MAAW,CAAC,CAAC;IACvC;IACAiK,QAAQA,CAAA,EAAG;MACP,MAAMxI,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EAAE;QACP,OAAOA,KAAK,CAACwI,QAAQ,CAAC,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACAjI,aAAaA,CAAA,EAAG;MACZ,MAAM;QAAEjD;MAAc,CAAC,GAAG,IAAI,CAACzD,OAAO;MACtC,IAAI,CAACyD,aAAa,EACd;MACJ;MACA,IAAImL,SAAS,GAAG,KAAK;MACrB;AACZ;AACA;AACA;MACY,MAAM;QAAEpP;MAAa,CAAC,GAAGiE,aAAa;MACtC,IAAIjE,YAAY,CAACqP,MAAM,IACnBrP,YAAY,CAACsP,OAAO,IACpBtP,YAAY,CAACuP,OAAO,IACpBvP,YAAY,CAACwP,OAAO,EAAE;QACtBJ,SAAS,GAAG,IAAI;MACpB;MACA;MACA,IAAI,CAACA,SAAS,EACV;MACJ,MAAMK,WAAW,GAAG,CAAC,CAAC;MACtB;MACA,KAAK,IAAI5M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhE,aAAa,CAACqB,MAAM,EAAE2C,CAAC,EAAE,EAAE;QAC3C,MAAM6M,GAAG,GAAG,QAAQ,GAAG7Q,aAAa,CAACgE,CAAC,CAAC;QACvC;QACA,IAAI7C,YAAY,CAAC0P,GAAG,CAAC,EAAE;UACnBD,WAAW,CAACC,GAAG,CAAC,GAAG1P,YAAY,CAAC0P,GAAG,CAAC;UACpCzL,aAAa,CAAC0L,cAAc,CAACD,GAAG,EAAE,CAAC,CAAC;QACxC;MACJ;MACA;MACA;MACAzL,aAAa,CAACsE,MAAM,CAAC,CAAC;MACtB;MACA,KAAK,MAAMmH,GAAG,IAAID,WAAW,EAAE;QAC3BxL,aAAa,CAAC0L,cAAc,CAACD,GAAG,EAAED,WAAW,CAACC,GAAG,CAAC,CAAC;MACvD;MACA;MACA;MACAzL,aAAa,CAACgG,cAAc,CAAC,CAAC;IAClC;IACA2F,mBAAmBA,CAACC,SAAS,EAAE;MAC3B,IAAIlE,EAAE,EAAEmE,EAAE;MACV,IAAI,CAAC,IAAI,CAAChM,QAAQ,IAAI,IAAI,CAAC5C,KAAK,EAC5B,OAAOf,SAAS;MACpB,IAAI,CAAC,IAAI,CAACoC,SAAS,EAAE;QACjB,OAAOzD,gBAAgB;MAC3B;MACA,MAAMiR,MAAM,GAAG;QACXhR,UAAU,EAAE;MAChB,CAAC;MACD,MAAMqI,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,IAAI,CAAChG,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvB4O,MAAM,CAACC,OAAO,GAAG,EAAE;QACnBD,MAAM,CAACE,aAAa,GAChBjS,kBAAkB,CAAC6R,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QAC3GF,MAAM,CAACG,SAAS,GAAG9I,iBAAiB,GAC9BA,iBAAiB,CAAC,IAAI,CAACpH,YAAY,EAAE,EAAE,CAAC,GACxC,MAAM;QACZ,OAAO+P,MAAM;MACjB;MACA,MAAMnE,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,IAAI,CAACzC,eAAe,IAAI,CAAC,IAAI,CAACpF,MAAM,IAAI,CAAC4H,IAAI,CAAC7G,MAAM,EAAE;QACvD,MAAMoL,WAAW,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC3P,OAAO,CAACuD,QAAQ,EAAE;UACvBoM,WAAW,CAACH,OAAO,GACf,IAAI,CAAChQ,YAAY,CAACgQ,OAAO,KAAK7P,SAAS,GACjC,IAAI,CAACH,YAAY,CAACgQ,OAAO,GACzB,CAAC;UACXG,WAAW,CAACF,aAAa,GACrBjS,kBAAkB,CAAC6R,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QAC/G;QACA,IAAI,IAAI,CAAC3N,YAAY,IAAI,CAAC1E,YAAY,CAAC,IAAI,CAACoC,YAAY,CAAC,EAAE;UACvDmQ,WAAW,CAACD,SAAS,GAAG9I,iBAAiB,GACnCA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;UACZ,IAAI,CAAC9E,YAAY,GAAG,KAAK;QAC7B;QACA,OAAO6N,WAAW;MACtB;MACA,MAAMC,cAAc,GAAGxE,IAAI,CAACmC,eAAe,IAAInC,IAAI,CAAC5L,YAAY;MAChE,IAAI,CAACqO,uBAAuB,CAAC,CAAC;MAC9B0B,MAAM,CAACG,SAAS,GAAGxS,wBAAwB,CAAC,IAAI,CAAC+O,4BAA4B,EAAE,IAAI,CAACpL,SAAS,EAAE+O,cAAc,CAAC;MAC9G,IAAIhJ,iBAAiB,EAAE;QACnB2I,MAAM,CAACG,SAAS,GAAG9I,iBAAiB,CAACgJ,cAAc,EAAEL,MAAM,CAACG,SAAS,CAAC;MAC1E;MACA,MAAM;QAAE5O,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAI,CAAC6H,eAAe;MACrC2G,MAAM,CAACM,eAAe,MAAAC,MAAA,CAAMhP,CAAC,CAACiP,MAAM,GAAG,GAAG,QAAAD,MAAA,CAAK/O,CAAC,CAACgP,MAAM,GAAG,GAAG,QAAK;MAClE,IAAI3E,IAAI,CAACmC,eAAe,EAAE;QACtB;AAChB;AACA;AACA;QACgBgC,MAAM,CAACC,OAAO,GACVpE,IAAI,KAAK,IAAI,GACP,CAACkE,EAAE,GAAG,CAACnE,EAAE,GAAGyE,cAAc,CAACJ,OAAO,MAAM,IAAI,IAAIrE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC3L,YAAY,CAACgQ,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,GAClI,IAAI,CAAC3B,eAAe,GAChB,IAAI,CAACnO,YAAY,CAACgQ,OAAO,GACzBI,cAAc,CAACI,WAAW;MAC5C,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBT,MAAM,CAACC,OAAO,GACVpE,IAAI,KAAK,IAAI,GACPwE,cAAc,CAACJ,OAAO,KAAK7P,SAAS,GAChCiQ,cAAc,CAACJ,OAAO,GACtB,EAAE,GACNI,cAAc,CAACI,WAAW,KAAKrQ,SAAS,GACpCiQ,cAAc,CAACI,WAAW,GAC1B,CAAC;MACnB;MACA;AACZ;AACA;MACY,KAAK,MAAMd,GAAG,IAAIjS,eAAe,EAAE;QAC/B,IAAI2S,cAAc,CAACV,GAAG,CAAC,KAAKvP,SAAS,EACjC;QACJ,MAAM;UAAEsQ,OAAO;UAAEC;QAAQ,CAAC,GAAGjT,eAAe,CAACiS,GAAG,CAAC;QACjD;AAChB;AACA;AACA;AACA;AACA;QACgB,MAAMiB,SAAS,GAAGZ,MAAM,CAACG,SAAS,KAAK,MAAM,GACvCE,cAAc,CAACV,GAAG,CAAC,GACnBe,OAAO,CAACL,cAAc,CAACV,GAAG,CAAC,EAAE9D,IAAI,CAAC;QACxC,IAAI8E,OAAO,EAAE;UACT,MAAME,GAAG,GAAGF,OAAO,CAACxQ,MAAM;UAC1B,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+N,GAAG,EAAE/N,CAAC,EAAE,EAAE;YAC1BkN,MAAM,CAACW,OAAO,CAAC7N,CAAC,CAAC,CAAC,GAAG8N,SAAS;UAClC;QACJ,CAAC,MACI;UACDZ,MAAM,CAACL,GAAG,CAAC,GAAGiB,SAAS;QAC3B;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACnQ,OAAO,CAACuD,QAAQ,EAAE;QACvBgM,MAAM,CAACE,aAAa,GAChBrE,IAAI,KAAK,IAAI,GACP5N,kBAAkB,CAAC6R,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE,GACvG,MAAM;MACpB;MACA,OAAOF,MAAM;IACjB;IACArH,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC9C,UAAU,GAAG,IAAI,CAACmD,QAAQ,GAAG5I,SAAS;IAC/C;IACA;IACA0Q,SAASA,CAAA,EAAG;MACR,IAAI,CAACnO,IAAI,CAACV,KAAK,CAACC,OAAO,CAAEqF,IAAI,IAAK;QAAE,IAAIqE,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGrE,IAAI,CAACzB,gBAAgB,MAAM,IAAI,IAAI8F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAAC,CAAC;MAAE,CAAC,CAAC;MAClI,IAAI,CAACtL,IAAI,CAACV,KAAK,CAACC,OAAO,CAAC2F,iBAAiB,CAAC;MAC1C,IAAI,CAAClF,IAAI,CAACD,WAAW,CAACqO,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC;AACL;AACA,SAAS/I,YAAYA,CAACT,IAAI,EAAE;EACxBA,IAAI,CAACS,YAAY,CAAC,CAAC;AACvB;AACA,SAASC,kBAAkBA,CAACV,IAAI,EAAE;EAC9B,IAAIqE,EAAE;EACN,MAAM5C,QAAQ,GAAG,CAAC,CAAC4C,EAAE,GAAGrE,IAAI,CAAC1B,UAAU,MAAM,IAAI,IAAI+F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5C,QAAQ,KAAKzB,IAAI,CAACyB,QAAQ;EAC3G,IAAIzB,IAAI,CAAChB,MAAM,CAAC,CAAC,IACbgB,IAAI,CAACtD,MAAM,IACX+E,QAAQ,IACRzB,IAAI,CAAC1D,YAAY,CAAC,WAAW,CAAC,EAAE;IAChC,MAAM;MAAEyF,SAAS,EAAErF,MAAM;MAAEuG,WAAW,EAAEwG;IAAe,CAAC,GAAGzJ,IAAI,CAACtD,MAAM;IACtE,MAAM;MAAEuK;IAAc,CAAC,GAAGjH,IAAI,CAAC9G,OAAO;IACtC,MAAMsL,QAAQ,GAAG/C,QAAQ,CAACyB,MAAM,KAAKlD,IAAI,CAACtD,MAAM,CAACwG,MAAM;IACvD;IACA;IACA,IAAI+D,aAAa,KAAK,MAAM,EAAE;MAC1B5Q,QAAQ,CAAEqT,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGnF,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACyG,IAAI,CAAC,GAC1BjI,QAAQ,CAACM,SAAS,CAAC2H,IAAI,CAAC;QAC9B,MAAM9Q,MAAM,GAAGpD,UAAU,CAACmU,YAAY,CAAC;QACvCA,YAAY,CAACxC,GAAG,GAAGzK,MAAM,CAACgN,IAAI,CAAC,CAACvC,GAAG;QACnCwC,YAAY,CAACvC,GAAG,GAAGuC,YAAY,CAACxC,GAAG,GAAGvO,MAAM;MAChD,CAAC,CAAC;IACN,CAAC,MACI,IAAIoO,yBAAyB,CAACC,aAAa,EAAExF,QAAQ,CAACM,SAAS,EAAErF,MAAM,CAAC,EAAE;MAC3ErG,QAAQ,CAAEqT,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGnF,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACyG,IAAI,CAAC,GAC1BjI,QAAQ,CAACM,SAAS,CAAC2H,IAAI,CAAC;QAC9B,MAAM9Q,MAAM,GAAGpD,UAAU,CAACkH,MAAM,CAACgN,IAAI,CAAC,CAAC;QACvCC,YAAY,CAACvC,GAAG,GAAGuC,YAAY,CAACxC,GAAG,GAAGvO,MAAM;QAC5C;AAChB;AACA;QACgB,IAAIoH,IAAI,CAACtC,cAAc,IAAI,CAACsC,IAAI,CAACzB,gBAAgB,EAAE;UAC/CyB,IAAI,CAAC1G,iBAAiB,GAAG,IAAI;UAC7B0G,IAAI,CAACtC,cAAc,CAACgM,IAAI,CAAC,CAACtC,GAAG,GACzBpH,IAAI,CAACtC,cAAc,CAACgM,IAAI,CAAC,CAACvC,GAAG,GAAGvO,MAAM;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,MAAMgR,WAAW,GAAGhU,WAAW,CAAC,CAAC;IACjCL,YAAY,CAACqU,WAAW,EAAElN,MAAM,EAAE+E,QAAQ,CAACM,SAAS,CAAC;IACrD,MAAM8H,WAAW,GAAGjU,WAAW,CAAC,CAAC;IACjC,IAAI4O,QAAQ,EAAE;MACVjP,YAAY,CAACsU,WAAW,EAAE7J,IAAI,CAACuD,cAAc,CAACkG,cAAc,EAAE,IAAI,CAAC,EAAEhI,QAAQ,CAACwB,WAAW,CAAC;IAC9F,CAAC,MACI;MACD1N,YAAY,CAACsU,WAAW,EAAEnN,MAAM,EAAE+E,QAAQ,CAACM,SAAS,CAAC;IACzD;IACA,MAAM1E,gBAAgB,GAAG,CAACtH,WAAW,CAAC6T,WAAW,CAAC;IAClD,IAAItM,wBAAwB,GAAG,KAAK;IACpC,IAAI,CAAC0C,IAAI,CAAC1B,UAAU,EAAE;MAClB,MAAM4F,cAAc,GAAGlE,IAAI,CAAC2E,0BAA0B,CAAC,CAAC;MACxD;AACZ;AACA;AACA;MACY,IAAIT,cAAc,IAAI,CAACA,cAAc,CAAC5F,UAAU,EAAE;QAC9C,MAAM;UAAEmD,QAAQ,EAAEqI,cAAc;UAAEpN,MAAM,EAAEqN;QAAa,CAAC,GAAG7F,cAAc;QACzE,IAAI4F,cAAc,IAAIC,YAAY,EAAE;UAChC,MAAMC,gBAAgB,GAAGrU,SAAS,CAAC,CAAC;UACpCN,oBAAoB,CAAC2U,gBAAgB,EAAEvI,QAAQ,CAACM,SAAS,EAAE+H,cAAc,CAAC/H,SAAS,CAAC;UACpF,MAAM2D,cAAc,GAAG/P,SAAS,CAAC,CAAC;UAClCN,oBAAoB,CAACqQ,cAAc,EAAEhJ,MAAM,EAAEqN,YAAY,CAAChI,SAAS,CAAC;UACpE,IAAI,CAACjM,gBAAgB,CAACkU,gBAAgB,EAAEtE,cAAc,CAAC,EAAE;YACrDpI,wBAAwB,GAAG,IAAI;UACnC;UACA,IAAI4G,cAAc,CAAChL,OAAO,CAACmF,UAAU,EAAE;YACnC2B,IAAI,CAACtC,cAAc,GAAGgI,cAAc;YACpC1F,IAAI,CAAC4E,oBAAoB,GAAGoF,gBAAgB;YAC5ChK,IAAI,CAACkE,cAAc,GAAGA,cAAc;UACxC;QACJ;MACJ;IACJ;IACAlE,IAAI,CAACjE,eAAe,CAAC,WAAW,EAAE;MAC9BW,MAAM;MACN+E,QAAQ;MACRrE,KAAK,EAAEyM,WAAW;MAClBD,WAAW;MACXvM,gBAAgB;MAChBC;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAI0C,IAAI,CAAChB,MAAM,CAAC,CAAC,EAAE;IACpB,MAAM;MAAEC;IAAe,CAAC,GAAGe,IAAI,CAAC9G,OAAO;IACvC+F,cAAc,IAAIA,cAAc,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIe,IAAI,CAAC9G,OAAO,CAAC0E,UAAU,GAAG/E,SAAS;AACvC;AACA,SAAS+B,mBAAmBA,CAACoF,IAAI,EAAE;EAC/B;AACJ;AACA;EACIpI,mBAAmB,CAACE,UAAU,EAAE;EAChC,IAAI,CAACkI,IAAI,CAAClH,MAAM,EACZ;EACJ;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,CAACkH,IAAI,CAAC8E,YAAY,CAAC,CAAC,EAAE;IACtB9E,IAAI,CAAC1G,iBAAiB,GAAG0G,IAAI,CAAClH,MAAM,CAACQ,iBAAiB;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACI0G,IAAI,CAACzG,uBAAuB,KAAKyG,IAAI,CAACzG,uBAAuB,GAAG2I,OAAO,CAAClC,IAAI,CAAC1G,iBAAiB,IAC1F0G,IAAI,CAAClH,MAAM,CAACQ,iBAAiB,IAC7B0G,IAAI,CAAClH,MAAM,CAACS,uBAAuB,CAAC,CAAC;EACzCyG,IAAI,CAACxG,gBAAgB,KAAKwG,IAAI,CAACxG,gBAAgB,GAAGwG,IAAI,CAAClH,MAAM,CAACU,gBAAgB,CAAC;AACnF;AACA,SAASuB,eAAeA,CAACiF,IAAI,EAAE;EAC3BA,IAAI,CAAC1G,iBAAiB,GAClB0G,IAAI,CAACzG,uBAAuB,GACxByG,IAAI,CAACxG,gBAAgB,GACjB,KAAK;AACrB;AACA,SAAS4H,aAAaA,CAACpB,IAAI,EAAE;EACzBA,IAAI,CAACoB,aAAa,CAAC,CAAC;AACxB;AACA,SAASd,iBAAiBA,CAACN,IAAI,EAAE;EAC7BA,IAAI,CAACM,iBAAiB,CAAC,CAAC;AAC5B;AACA,SAASC,kBAAkBA,CAACP,IAAI,EAAE;EAC9BA,IAAI,CAAC3G,aAAa,GAAG,KAAK;AAC9B;AACA,SAASmH,mBAAmBA,CAACR,IAAI,EAAE;EAC/B,MAAM;IAAErD;EAAc,CAAC,GAAGqD,IAAI,CAAC9G,OAAO;EACtC,IAAIyD,aAAa,IAAIA,aAAa,CAACsB,QAAQ,CAAC,CAAC,CAACgM,qBAAqB,EAAE;IACjEtN,aAAa,CAACN,MAAM,CAAC,qBAAqB,CAAC;EAC/C;EACA2D,IAAI,CAACzH,cAAc,CAAC,CAAC;AACzB;AACA,SAASyE,eAAeA,CAACgD,IAAI,EAAE;EAC3BA,IAAI,CAAChD,eAAe,CAAC,CAAC;EACtBgD,IAAI,CAAC8D,WAAW,GAAG9D,IAAI,CAACtC,cAAc,GAAGsC,IAAI,CAACvC,MAAM,GAAG5E,SAAS;EAChEmH,IAAI,CAAC1G,iBAAiB,GAAG,IAAI;AACjC;AACA,SAASuB,kBAAkBA,CAACmF,IAAI,EAAE;EAC9BA,IAAI,CAACnF,kBAAkB,CAAC,CAAC;AAC7B;AACA,SAASC,cAAcA,CAACkF,IAAI,EAAE;EAC1BA,IAAI,CAAClF,cAAc,CAAC,CAAC;AACzB;AACA,SAAS8E,aAAaA,CAACI,IAAI,EAAE;EACzBA,IAAI,CAACJ,aAAa,CAAC,CAAC;AACxB;AACA,SAASyB,mBAAmBA,CAAChC,KAAK,EAAE;EAChCA,KAAK,CAAC6K,kBAAkB,CAAC,CAAC;AAC9B;AACA,SAAS3D,YAAYA,CAAC4D,MAAM,EAAE/M,KAAK,EAAEgN,CAAC,EAAE;EACpCD,MAAM,CAACE,SAAS,GAAGxT,GAAG,CAACuG,KAAK,CAACiN,SAAS,EAAE,CAAC,EAAED,CAAC,CAAC;EAC7CD,MAAM,CAACG,KAAK,GAAGzT,GAAG,CAACuG,KAAK,CAACkN,KAAK,EAAE,CAAC,EAAEF,CAAC,CAAC;EACrCD,MAAM,CAAClB,MAAM,GAAG7L,KAAK,CAAC6L,MAAM;EAC5BkB,MAAM,CAACI,WAAW,GAAGnN,KAAK,CAACmN,WAAW;AAC1C;AACA,SAASC,OAAOA,CAACL,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EAClCD,MAAM,CAAChD,GAAG,GAAGtQ,GAAG,CAAC4T,IAAI,CAACtD,GAAG,EAAEuD,EAAE,CAACvD,GAAG,EAAEiD,CAAC,CAAC;EACrCD,MAAM,CAAC/C,GAAG,GAAGvQ,GAAG,CAAC4T,IAAI,CAACrD,GAAG,EAAEsD,EAAE,CAACtD,GAAG,EAAEgD,CAAC,CAAC;AACzC;AACA,SAAS5D,MAAMA,CAAC2D,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EACjCI,OAAO,CAACL,MAAM,CAACnQ,CAAC,EAAEyQ,IAAI,CAACzQ,CAAC,EAAE0Q,EAAE,CAAC1Q,CAAC,EAAEoQ,CAAC,CAAC;EAClCI,OAAO,CAACL,MAAM,CAAClQ,CAAC,EAAEwQ,IAAI,CAACxQ,CAAC,EAAEyQ,EAAE,CAACzQ,CAAC,EAAEmQ,CAAC,CAAC;AACtC;AACA,SAASlE,mBAAmBA,CAAClG,IAAI,EAAE;EAC/B,OAAQA,IAAI,CAACyG,eAAe,IAAIzG,IAAI,CAACyG,eAAe,CAACyC,WAAW,KAAKrQ,SAAS;AAClF;AACA,MAAMiF,uBAAuB,GAAG;EAC5B6M,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,CAAC;AACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK,OAAOC,SAAS,KAAK,WAAW,IAClEA,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA,MAAMK,UAAU,GAAGN,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAACA,iBAAiB,CAAC,SAAS,CAAC,GAC/EO,IAAI,CAACC,KAAK,GACV/T,IAAI;AACV,SAASgU,SAASA,CAAC5B,IAAI,EAAE;EACrB;EACAA,IAAI,CAACvC,GAAG,GAAGgE,UAAU,CAACzB,IAAI,CAACvC,GAAG,CAAC;EAC/BuC,IAAI,CAACtC,GAAG,GAAG+D,UAAU,CAACzB,IAAI,CAACtC,GAAG,CAAC;AACnC;AACA,SAASpE,QAAQA,CAACG,GAAG,EAAE;EACnBmI,SAAS,CAACnI,GAAG,CAACnJ,CAAC,CAAC;EAChBsR,SAAS,CAACnI,GAAG,CAAClJ,CAAC,CAAC;AACpB;AACA,SAAS+M,yBAAyBA,CAACC,aAAa,EAAExF,QAAQ,EAAE/E,MAAM,EAAE;EAChE,OAAQuK,aAAa,KAAK,UAAU,IAC/BA,aAAa,KAAK,iBAAiB,IAChC,CAACxR,MAAM,CAACO,WAAW,CAACyL,QAAQ,CAAC,EAAEzL,WAAW,CAAC0G,MAAM,CAAC,EAAE,GAAG,CAAE;AACrE;AAEA,SAAS3B,eAAe,EAAE9C,oBAAoB,EAAEuS,OAAO,EAAEjE,YAAY,EAAEC,MAAM,EAAE5L,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}