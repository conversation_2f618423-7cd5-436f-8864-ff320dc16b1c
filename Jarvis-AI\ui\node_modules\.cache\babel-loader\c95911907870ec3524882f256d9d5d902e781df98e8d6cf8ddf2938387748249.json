{"ast": null, "code": "import { resolveCSSVariables } from './css-variables-conversion.mjs';\nimport { unitConversion } from './unit-conversion.mjs';\n\n/**\n * Parse a DOM variant to make it animatable. This involves resolving CSS variables\n * and ensuring animations like \"20%\" => \"calc(50vw)\" are performed in pixels.\n */\nconst parseDomVariant = (visualElement, target, origin, transitionEnd) => {\n  const resolved = resolveCSSVariables(visualElement, target, transitionEnd);\n  target = resolved.target;\n  transitionEnd = resolved.transitionEnd;\n  return unitConversion(visualElement, target, origin, transitionEnd);\n};\nexport { parseDomVariant };", "map": {"version": 3, "names": ["resolveCSSVariables", "unitConversion", "parseDomVariant", "visualElement", "target", "origin", "transitionEnd", "resolved"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/dom/utils/parse-dom-variant.mjs"], "sourcesContent": ["import { resolveCSSVariables } from './css-variables-conversion.mjs';\nimport { unitConversion } from './unit-conversion.mjs';\n\n/**\n * Parse a DOM variant to make it animatable. This involves resolving CSS variables\n * and ensuring animations like \"20%\" => \"calc(50vw)\" are performed in pixels.\n */\nconst parseDomVariant = (visualElement, target, origin, transitionEnd) => {\n    const resolved = resolveCSSVariables(visualElement, target, transitionEnd);\n    target = resolved.target;\n    transitionEnd = resolved.transitionEnd;\n    return unitConversion(visualElement, target, origin, transitionEnd);\n};\n\nexport { parseDomVariant };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,cAAc,QAAQ,uBAAuB;;AAEtD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAACC,aAAa,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,KAAK;EACtE,MAAMC,QAAQ,GAAGP,mBAAmB,CAACG,aAAa,EAAEC,MAAM,EAAEE,aAAa,CAAC;EAC1EF,MAAM,GAAGG,QAAQ,CAACH,MAAM;EACxBE,aAAa,GAAGC,QAAQ,CAACD,aAAa;EACtC,OAAOL,cAAc,CAACE,aAAa,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,CAAC;AACvE,CAAC;AAED,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}