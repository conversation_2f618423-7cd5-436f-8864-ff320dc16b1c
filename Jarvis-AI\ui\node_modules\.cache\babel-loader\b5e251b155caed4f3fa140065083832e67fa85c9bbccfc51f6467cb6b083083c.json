{"ast": null, "code": "import { addUniqueItem, removeItem } from '../../utils/array.mjs';\nclass NodeStack {\n  constructor() {\n    this.members = [];\n  }\n  add(node) {\n    addUniqueItem(this.members, node);\n    node.scheduleRender();\n  }\n  remove(node) {\n    removeItem(this.members, node);\n    if (node === this.prevLead) {\n      this.prevLead = undefined;\n    }\n    if (node === this.lead) {\n      const prevLead = this.members[this.members.length - 1];\n      if (prevLead) {\n        this.promote(prevLead);\n      }\n    }\n  }\n  relegate(node) {\n    const indexOfNode = this.members.findIndex(member => node === member);\n    if (indexOfNode === 0) return false;\n    /**\n     * Find the next projection node that is present\n     */\n    let prevLead;\n    for (let i = indexOfNode; i >= 0; i--) {\n      const member = this.members[i];\n      if (member.isPresent !== false) {\n        prevLead = member;\n        break;\n      }\n    }\n    if (prevLead) {\n      this.promote(prevLead);\n      return true;\n    } else {\n      return false;\n    }\n  }\n  promote(node, preserveFollowOpacity) {\n    const prevLead = this.lead;\n    if (node === prevLead) return;\n    this.prevLead = prevLead;\n    this.lead = node;\n    node.show();\n    if (prevLead) {\n      prevLead.instance && prevLead.scheduleRender();\n      node.scheduleRender();\n      node.resumeFrom = prevLead;\n      if (preserveFollowOpacity) {\n        node.resumeFrom.preserveOpacity = true;\n      }\n      if (prevLead.snapshot) {\n        node.snapshot = prevLead.snapshot;\n        node.snapshot.latestValues = prevLead.animationValues || prevLead.latestValues;\n      }\n      if (node.root && node.root.isUpdating) {\n        node.isLayoutDirty = true;\n      }\n      const {\n        crossfade\n      } = node.options;\n      if (crossfade === false) {\n        prevLead.hide();\n      }\n      /**\n       * TODO:\n       *   - Test border radius when previous node was deleted\n       *   - boxShadow mixing\n       *   - Shared between element A in scrolled container and element B (scroll stays the same or changes)\n       *   - Shared between element A in transformed container and element B (transform stays the same or changes)\n       *   - Shared between element A in scrolled page and element B (scroll stays the same or changes)\n       * ---\n       *   - Crossfade opacity of root nodes\n       *   - layoutId changes after animation\n       *   - layoutId changes mid animation\n       */\n    }\n  }\n  exitAnimationComplete() {\n    this.members.forEach(node => {\n      const {\n        options,\n        resumingFrom\n      } = node;\n      options.onExitComplete && options.onExitComplete();\n      if (resumingFrom) {\n        resumingFrom.options.onExitComplete && resumingFrom.options.onExitComplete();\n      }\n    });\n  }\n  scheduleRender() {\n    this.members.forEach(node => {\n      node.instance && node.scheduleRender(false);\n    });\n  }\n  /**\n   * Clear any leads that have been removed this render to prevent them from being\n   * used in future animations and to prevent memory leaks\n   */\n  removeLeadSnapshot() {\n    if (this.lead && this.lead.snapshot) {\n      this.lead.snapshot = undefined;\n    }\n  }\n}\nexport { NodeStack };", "map": {"version": 3, "names": ["addUniqueItem", "removeItem", "NodeStack", "constructor", "members", "add", "node", "scheduleRender", "remove", "prevLead", "undefined", "lead", "length", "promote", "relegate", "indexOfNode", "findIndex", "member", "i", "isPresent", "preserveFollowOpacity", "show", "instance", "resumeFrom", "preserveOpacity", "snapshot", "latestValues", "animationValues", "root", "isUpdating", "isLayoutDirty", "crossfade", "options", "hide", "exitAnimationComplete", "for<PERSON>ach", "resumingFrom", "onExitComplete", "removeLeadSnapshot"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/projection/shared/stack.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from '../../utils/array.mjs';\n\nclass NodeStack {\n    constructor() {\n        this.members = [];\n    }\n    add(node) {\n        addUniqueItem(this.members, node);\n        node.scheduleRender();\n    }\n    remove(node) {\n        removeItem(this.members, node);\n        if (node === this.prevLead) {\n            this.prevLead = undefined;\n        }\n        if (node === this.lead) {\n            const prevLead = this.members[this.members.length - 1];\n            if (prevLead) {\n                this.promote(prevLead);\n            }\n        }\n    }\n    relegate(node) {\n        const indexOfNode = this.members.findIndex((member) => node === member);\n        if (indexOfNode === 0)\n            return false;\n        /**\n         * Find the next projection node that is present\n         */\n        let prevLead;\n        for (let i = indexOfNode; i >= 0; i--) {\n            const member = this.members[i];\n            if (member.isPresent !== false) {\n                prevLead = member;\n                break;\n            }\n        }\n        if (prevLead) {\n            this.promote(prevLead);\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    promote(node, preserveFollowOpacity) {\n        const prevLead = this.lead;\n        if (node === prevLead)\n            return;\n        this.prevLead = prevLead;\n        this.lead = node;\n        node.show();\n        if (prevLead) {\n            prevLead.instance && prevLead.scheduleRender();\n            node.scheduleRender();\n            node.resumeFrom = prevLead;\n            if (preserveFollowOpacity) {\n                node.resumeFrom.preserveOpacity = true;\n            }\n            if (prevLead.snapshot) {\n                node.snapshot = prevLead.snapshot;\n                node.snapshot.latestValues =\n                    prevLead.animationValues || prevLead.latestValues;\n            }\n            if (node.root && node.root.isUpdating) {\n                node.isLayoutDirty = true;\n            }\n            const { crossfade } = node.options;\n            if (crossfade === false) {\n                prevLead.hide();\n            }\n            /**\n             * TODO:\n             *   - Test border radius when previous node was deleted\n             *   - boxShadow mixing\n             *   - Shared between element A in scrolled container and element B (scroll stays the same or changes)\n             *   - Shared between element A in transformed container and element B (transform stays the same or changes)\n             *   - Shared between element A in scrolled page and element B (scroll stays the same or changes)\n             * ---\n             *   - Crossfade opacity of root nodes\n             *   - layoutId changes after animation\n             *   - layoutId changes mid animation\n             */\n        }\n    }\n    exitAnimationComplete() {\n        this.members.forEach((node) => {\n            const { options, resumingFrom } = node;\n            options.onExitComplete && options.onExitComplete();\n            if (resumingFrom) {\n                resumingFrom.options.onExitComplete &&\n                    resumingFrom.options.onExitComplete();\n            }\n        });\n    }\n    scheduleRender() {\n        this.members.forEach((node) => {\n            node.instance && node.scheduleRender(false);\n        });\n    }\n    /**\n     * Clear any leads that have been removed this render to prevent them from being\n     * used in future animations and to prevent memory leaks\n     */\n    removeLeadSnapshot() {\n        if (this.lead && this.lead.snapshot) {\n            this.lead.snapshot = undefined;\n        }\n    }\n}\n\nexport { NodeStack };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,uBAAuB;AAEjE,MAAMC,SAAS,CAAC;EACZC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,EAAE;EACrB;EACAC,GAAGA,CAACC,IAAI,EAAE;IACNN,aAAa,CAAC,IAAI,CAACI,OAAO,EAAEE,IAAI,CAAC;IACjCA,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAC,MAAMA,CAACF,IAAI,EAAE;IACTL,UAAU,CAAC,IAAI,CAACG,OAAO,EAAEE,IAAI,CAAC;IAC9B,IAAIA,IAAI,KAAK,IAAI,CAACG,QAAQ,EAAE;MACxB,IAAI,CAACA,QAAQ,GAAGC,SAAS;IAC7B;IACA,IAAIJ,IAAI,KAAK,IAAI,CAACK,IAAI,EAAE;MACpB,MAAMF,QAAQ,GAAG,IAAI,CAACL,OAAO,CAAC,IAAI,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC;MACtD,IAAIH,QAAQ,EAAE;QACV,IAAI,CAACI,OAAO,CAACJ,QAAQ,CAAC;MAC1B;IACJ;EACJ;EACAK,QAAQA,CAACR,IAAI,EAAE;IACX,MAAMS,WAAW,GAAG,IAAI,CAACX,OAAO,CAACY,SAAS,CAAEC,MAAM,IAAKX,IAAI,KAAKW,MAAM,CAAC;IACvE,IAAIF,WAAW,KAAK,CAAC,EACjB,OAAO,KAAK;IAChB;AACR;AACA;IACQ,IAAIN,QAAQ;IACZ,KAAK,IAAIS,CAAC,GAAGH,WAAW,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACnC,MAAMD,MAAM,GAAG,IAAI,CAACb,OAAO,CAACc,CAAC,CAAC;MAC9B,IAAID,MAAM,CAACE,SAAS,KAAK,KAAK,EAAE;QAC5BV,QAAQ,GAAGQ,MAAM;QACjB;MACJ;IACJ;IACA,IAAIR,QAAQ,EAAE;MACV,IAAI,CAACI,OAAO,CAACJ,QAAQ,CAAC;MACtB,OAAO,IAAI;IACf,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACAI,OAAOA,CAACP,IAAI,EAAEc,qBAAqB,EAAE;IACjC,MAAMX,QAAQ,GAAG,IAAI,CAACE,IAAI;IAC1B,IAAIL,IAAI,KAAKG,QAAQ,EACjB;IACJ,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,IAAI,GAAGL,IAAI;IAChBA,IAAI,CAACe,IAAI,CAAC,CAAC;IACX,IAAIZ,QAAQ,EAAE;MACVA,QAAQ,CAACa,QAAQ,IAAIb,QAAQ,CAACF,cAAc,CAAC,CAAC;MAC9CD,IAAI,CAACC,cAAc,CAAC,CAAC;MACrBD,IAAI,CAACiB,UAAU,GAAGd,QAAQ;MAC1B,IAAIW,qBAAqB,EAAE;QACvBd,IAAI,CAACiB,UAAU,CAACC,eAAe,GAAG,IAAI;MAC1C;MACA,IAAIf,QAAQ,CAACgB,QAAQ,EAAE;QACnBnB,IAAI,CAACmB,QAAQ,GAAGhB,QAAQ,CAACgB,QAAQ;QACjCnB,IAAI,CAACmB,QAAQ,CAACC,YAAY,GACtBjB,QAAQ,CAACkB,eAAe,IAAIlB,QAAQ,CAACiB,YAAY;MACzD;MACA,IAAIpB,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACsB,IAAI,CAACC,UAAU,EAAE;QACnCvB,IAAI,CAACwB,aAAa,GAAG,IAAI;MAC7B;MACA,MAAM;QAAEC;MAAU,CAAC,GAAGzB,IAAI,CAAC0B,OAAO;MAClC,IAAID,SAAS,KAAK,KAAK,EAAE;QACrBtB,QAAQ,CAACwB,IAAI,CAAC,CAAC;MACnB;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ;EACJ;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC9B,OAAO,CAAC+B,OAAO,CAAE7B,IAAI,IAAK;MAC3B,MAAM;QAAE0B,OAAO;QAAEI;MAAa,CAAC,GAAG9B,IAAI;MACtC0B,OAAO,CAACK,cAAc,IAAIL,OAAO,CAACK,cAAc,CAAC,CAAC;MAClD,IAAID,YAAY,EAAE;QACdA,YAAY,CAACJ,OAAO,CAACK,cAAc,IAC/BD,YAAY,CAACJ,OAAO,CAACK,cAAc,CAAC,CAAC;MAC7C;IACJ,CAAC,CAAC;EACN;EACA9B,cAAcA,CAAA,EAAG;IACb,IAAI,CAACH,OAAO,CAAC+B,OAAO,CAAE7B,IAAI,IAAK;MAC3BA,IAAI,CAACgB,QAAQ,IAAIhB,IAAI,CAACC,cAAc,CAAC,KAAK,CAAC;IAC/C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI+B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC3B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACc,QAAQ,EAAE;MACjC,IAAI,CAACd,IAAI,CAACc,QAAQ,GAAGf,SAAS;IAClC;EACJ;AACJ;AAEA,SAASR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}