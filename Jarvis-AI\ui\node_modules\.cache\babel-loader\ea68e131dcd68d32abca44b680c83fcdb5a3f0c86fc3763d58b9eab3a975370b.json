{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\", \"style\", \"value\", \"as\", \"onDrag\", \"layout\"];\nimport { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction useDefaultMotionValue(value) {\n  let defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItem(_ref, externalRef) {\n  let {\n      children,\n      style = {},\n      value,\n      as = \"li\",\n      onDrag,\n      layout = true\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  const Component = useConstant(() => motion(as));\n  const context = useContext(ReorderContext);\n  const point = {\n    x: useDefaultMotionValue(style.x),\n    y: useDefaultMotionValue(style.y)\n  };\n  const zIndex = useTransform([point.x, point.y], _ref2 => {\n    let [latestX, latestY] = _ref2;\n    return latestX || latestY ? 1 : \"unset\";\n  });\n  invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n  const {\n    axis,\n    registerItem,\n    updateOrder\n  } = context;\n  return React.createElement(Component, _objectSpread(_objectSpread({\n    drag: axis\n  }, props), {}, {\n    dragSnapToOrigin: true,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      x: point.x,\n      y: point.y,\n      zIndex\n    }),\n    layout: layout,\n    onDrag: (event, gesturePoint) => {\n      const {\n        velocity\n      } = gesturePoint;\n      velocity[axis] && updateOrder(value, point[axis].get(), velocity[axis]);\n      onDrag && onDrag(event, gesturePoint);\n    },\n    onLayoutMeasure: measured => registerItem(value, measured),\n    ref: externalRef,\n    ignoreStrict: true\n  }), children);\n}\nconst Item = forwardRef(ReorderItem);\nexport { Item, ReorderItem };", "map": {"version": 3, "names": ["invariant", "React", "forwardRef", "useContext", "ReorderContext", "motion", "useConstant", "useMotionValue", "useTransform", "isMotionValue", "useDefaultMotionValue", "value", "defaultValue", "arguments", "length", "undefined", "ReorderItem", "_ref", "externalRef", "children", "style", "as", "onDrag", "layout", "props", "_objectWithoutProperties", "_excluded", "Component", "context", "point", "x", "y", "zIndex", "_ref2", "latestX", "latestY", "Boolean", "axis", "registerItem", "updateOrder", "createElement", "_objectSpread", "drag", "dragSnapToO<PERSON>in", "event", "gesturePoint", "velocity", "get", "onLayoutMeasure", "measured", "ref", "ignoreStrict", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/components/Reorder/Item.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction useDefaultMotionValue(value, defaultValue = 0) {\n    return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItem({ children, style = {}, value, as = \"li\", onDrag, layout = true, ...props }, externalRef) {\n    const Component = useConstant(() => motion(as));\n    const context = useContext(ReorderContext);\n    const point = {\n        x: useDefaultMotionValue(style.x),\n        y: useDefaultMotionValue(style.y),\n    };\n    const zIndex = useTransform([point.x, point.y], ([latestX, latestY]) => latestX || latestY ? 1 : \"unset\");\n    invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n    const { axis, registerItem, updateOrder } = context;\n    return (React.createElement(Component, { drag: axis, ...props, dragSnapToOrigin: true, style: { ...style, x: point.x, y: point.y, zIndex }, layout: layout, onDrag: (event, gesturePoint) => {\n            const { velocity } = gesturePoint;\n            velocity[axis] &&\n                updateOrder(value, point[axis].get(), velocity[axis]);\n            onDrag && onDrag(event, gesturePoint);\n        }, onLayoutMeasure: (measured) => registerItem(value, measured), ref: externalRef, ignoreStrict: true }, children));\n}\nconst Item = forwardRef(ReorderItem);\n\nexport { Item, ReorderItem };\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,qBAAqBA,CAACC,KAAK,EAAoB;EAAA,IAAlBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAClD,OAAOJ,aAAa,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAGJ,cAAc,CAACK,YAAY,CAAC;AACtE;AACA,SAASI,WAAWA,CAAAC,IAAA,EAA8EC,WAAW,EAAE;EAAA,IAA1F;MAAEC,QAAQ;MAAEC,KAAK,GAAG,CAAC,CAAC;MAAET,KAAK;MAAEU,EAAE,GAAG,IAAI;MAAEC,MAAM;MAAEC,MAAM,GAAG;IAAe,CAAC,GAAAN,IAAA;IAAPO,KAAK,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EAC1F,MAAMC,SAAS,GAAGrB,WAAW,CAAC,MAAMD,MAAM,CAACgB,EAAE,CAAC,CAAC;EAC/C,MAAMO,OAAO,GAAGzB,UAAU,CAACC,cAAc,CAAC;EAC1C,MAAMyB,KAAK,GAAG;IACVC,CAAC,EAAEpB,qBAAqB,CAACU,KAAK,CAACU,CAAC,CAAC;IACjCC,CAAC,EAAErB,qBAAqB,CAACU,KAAK,CAACW,CAAC;EACpC,CAAC;EACD,MAAMC,MAAM,GAAGxB,YAAY,CAAC,CAACqB,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,CAAC,EAAEE,KAAA;IAAA,IAAC,CAACC,OAAO,EAAEC,OAAO,CAAC,GAAAF,KAAA;IAAA,OAAKC,OAAO,IAAIC,OAAO,GAAG,CAAC,GAAG,OAAO;EAAA,EAAC;EACzGnC,SAAS,CAACoC,OAAO,CAACR,OAAO,CAAC,EAAE,+CAA+C,CAAC;EAC5E,MAAM;IAAES,IAAI;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGX,OAAO;EACnD,OAAQ3B,KAAK,CAACuC,aAAa,CAACb,SAAS,EAAAc,aAAA,CAAAA,aAAA;IAAIC,IAAI,EAAEL;EAAI,GAAKb,KAAK;IAAEmB,gBAAgB,EAAE,IAAI;IAAEvB,KAAK,EAAAqB,aAAA,CAAAA,aAAA,KAAOrB,KAAK;MAAEU,CAAC,EAAED,KAAK,CAACC,CAAC;MAAEC,CAAC,EAAEF,KAAK,CAACE,CAAC;MAAEC;IAAM,EAAE;IAAET,MAAM,EAAEA,MAAM;IAAED,MAAM,EAAEA,CAACsB,KAAK,EAAEC,YAAY,KAAK;MACrL,MAAM;QAAEC;MAAS,CAAC,GAAGD,YAAY;MACjCC,QAAQ,CAACT,IAAI,CAAC,IACVE,WAAW,CAAC5B,KAAK,EAAEkB,KAAK,CAACQ,IAAI,CAAC,CAACU,GAAG,CAAC,CAAC,EAAED,QAAQ,CAACT,IAAI,CAAC,CAAC;MACzDf,MAAM,IAAIA,MAAM,CAACsB,KAAK,EAAEC,YAAY,CAAC;IACzC,CAAC;IAAEG,eAAe,EAAGC,QAAQ,IAAKX,YAAY,CAAC3B,KAAK,EAAEsC,QAAQ,CAAC;IAAEC,GAAG,EAAEhC,WAAW;IAAEiC,YAAY,EAAE;EAAI,IAAIhC,QAAQ,CAAC;AAC1H;AACA,MAAMiC,IAAI,GAAGlD,UAAU,CAACc,WAAW,CAAC;AAEpC,SAASoC,IAAI,EAAEpC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}