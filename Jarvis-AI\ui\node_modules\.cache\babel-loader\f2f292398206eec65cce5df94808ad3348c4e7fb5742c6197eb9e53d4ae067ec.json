{"ast": null, "code": "import { resolveElements } from '../render/dom/utils/resolve-element.mjs';\nimport { visualElementStore } from '../render/store.mjs';\nimport { invariant } from '../utils/errors.mjs';\nimport { GroupPlaybackControls } from './GroupPlaybackControls.mjs';\nimport { isDOMKeyframes } from './utils/is-dom-keyframes.mjs';\nimport { animateTarget } from './interfaces/visual-element-target.mjs';\nimport { createVisualElement } from './utils/create-visual-element.mjs';\nimport { animateSingleValue } from './interfaces/single-value.mjs';\nimport { createAnimationsFromSequence } from './sequence/create.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\nfunction animateElements(elementOrSelector, keyframes, options, scope) {\n  const elements = resolveElements(elementOrSelector, scope);\n  const numElements = elements.length;\n  invariant(Boolean(numElements), \"No valid element provided.\");\n  const animations = [];\n  for (let i = 0; i < numElements; i++) {\n    const element = elements[i];\n    /**\n     * Check each element for an associated VisualElement. If none exists,\n     * we need to create one.\n     */\n    if (!visualElementStore.has(element)) {\n      /**\n       * TODO: We only need render-specific parts of the VisualElement.\n       * With some additional work the size of the animate() function\n       * could be reduced significantly.\n       */\n      createVisualElement(element);\n    }\n    const visualElement = visualElementStore.get(element);\n    const transition = {\n      ...options\n    };\n    /**\n     * Resolve stagger function if provided.\n     */\n    if (typeof transition.delay === \"function\") {\n      transition.delay = transition.delay(i, numElements);\n    }\n    animations.push(...animateTarget(visualElement, {\n      ...keyframes,\n      transition\n    }, {}));\n  }\n  return new GroupPlaybackControls(animations);\n}\nconst isSequence = value => Array.isArray(value) && Array.isArray(value[0]);\nfunction animateSequence(sequence, options, scope) {\n  const animations = [];\n  const animationDefinitions = createAnimationsFromSequence(sequence, options, scope);\n  animationDefinitions.forEach(({\n    keyframes,\n    transition\n  }, subject) => {\n    let animation;\n    if (isMotionValue(subject)) {\n      animation = animateSingleValue(subject, keyframes.default, transition.default);\n    } else {\n      animation = animateElements(subject, keyframes, transition);\n    }\n    animations.push(animation);\n  });\n  return new GroupPlaybackControls(animations);\n}\nconst createScopedAnimate = scope => {\n  /**\n   * Implementation\n   */\n  function scopedAnimate(valueOrElementOrSequence, keyframes, options) {\n    let animation;\n    if (isSequence(valueOrElementOrSequence)) {\n      animation = animateSequence(valueOrElementOrSequence, keyframes, scope);\n    } else if (isDOMKeyframes(keyframes)) {\n      animation = animateElements(valueOrElementOrSequence, keyframes, options, scope);\n    } else {\n      animation = animateSingleValue(valueOrElementOrSequence, keyframes, options);\n    }\n    if (scope) {\n      scope.animations.push(animation);\n    }\n    return animation;\n  }\n  return scopedAnimate;\n};\nconst animate = createScopedAnimate();\nexport { animate, createScopedAnimate };", "map": {"version": 3, "names": ["resolveElements", "visualElementStore", "invariant", "GroupPlaybackControls", "isDOMKeyframes", "animate<PERSON>arget", "createVisualElement", "animateSingleValue", "createAnimationsFromSequence", "isMotionValue", "animateElements", "elementOrSelector", "keyframes", "options", "scope", "elements", "numElements", "length", "Boolean", "animations", "i", "element", "has", "visualElement", "get", "transition", "delay", "push", "isSequence", "value", "Array", "isArray", "animateSequence", "sequence", "animationDefinitions", "for<PERSON>ach", "subject", "animation", "default", "createScopedAnimate", "scopedAnimate", "valueOrElementOrSequence", "animate"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/animation/animate.mjs"], "sourcesContent": ["import { resolveElements } from '../render/dom/utils/resolve-element.mjs';\nimport { visualElementStore } from '../render/store.mjs';\nimport { invariant } from '../utils/errors.mjs';\nimport { GroupPlaybackControls } from './GroupPlaybackControls.mjs';\nimport { isDOMKeyframes } from './utils/is-dom-keyframes.mjs';\nimport { animateTarget } from './interfaces/visual-element-target.mjs';\nimport { createVisualElement } from './utils/create-visual-element.mjs';\nimport { animateSingleValue } from './interfaces/single-value.mjs';\nimport { createAnimationsFromSequence } from './sequence/create.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\n\nfunction animateElements(elementOrSelector, keyframes, options, scope) {\n    const elements = resolveElements(elementOrSelector, scope);\n    const numElements = elements.length;\n    invariant(Boolean(numElements), \"No valid element provided.\");\n    const animations = [];\n    for (let i = 0; i < numElements; i++) {\n        const element = elements[i];\n        /**\n         * Check each element for an associated VisualElement. If none exists,\n         * we need to create one.\n         */\n        if (!visualElementStore.has(element)) {\n            /**\n             * TODO: We only need render-specific parts of the VisualElement.\n             * With some additional work the size of the animate() function\n             * could be reduced significantly.\n             */\n            createVisualElement(element);\n        }\n        const visualElement = visualElementStore.get(element);\n        const transition = { ...options };\n        /**\n         * Resolve stagger function if provided.\n         */\n        if (typeof transition.delay === \"function\") {\n            transition.delay = transition.delay(i, numElements);\n        }\n        animations.push(...animateTarget(visualElement, { ...keyframes, transition }, {}));\n    }\n    return new GroupPlaybackControls(animations);\n}\nconst isSequence = (value) => Array.isArray(value) && Array.isArray(value[0]);\nfunction animateSequence(sequence, options, scope) {\n    const animations = [];\n    const animationDefinitions = createAnimationsFromSequence(sequence, options, scope);\n    animationDefinitions.forEach(({ keyframes, transition }, subject) => {\n        let animation;\n        if (isMotionValue(subject)) {\n            animation = animateSingleValue(subject, keyframes.default, transition.default);\n        }\n        else {\n            animation = animateElements(subject, keyframes, transition);\n        }\n        animations.push(animation);\n    });\n    return new GroupPlaybackControls(animations);\n}\nconst createScopedAnimate = (scope) => {\n    /**\n     * Implementation\n     */\n    function scopedAnimate(valueOrElementOrSequence, keyframes, options) {\n        let animation;\n        if (isSequence(valueOrElementOrSequence)) {\n            animation = animateSequence(valueOrElementOrSequence, keyframes, scope);\n        }\n        else if (isDOMKeyframes(keyframes)) {\n            animation = animateElements(valueOrElementOrSequence, keyframes, options, scope);\n        }\n        else {\n            animation = animateSingleValue(valueOrElementOrSequence, keyframes, options);\n        }\n        if (scope) {\n            scope.animations.push(animation);\n        }\n        return animation;\n    }\n    return scopedAnimate;\n};\nconst animate = createScopedAnimate();\n\nexport { animate, createScopedAnimate };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,yCAAyC;AACzE,SAASC,kBAAkB,QAAQ,qBAAqB;AACxD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,aAAa,QAAQ,wCAAwC;AACtE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,4BAA4B,QAAQ,uBAAuB;AACpE,SAASC,aAAa,QAAQ,oCAAoC;AAElE,SAASC,eAAeA,CAACC,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACnE,MAAMC,QAAQ,GAAGf,eAAe,CAACW,iBAAiB,EAAEG,KAAK,CAAC;EAC1D,MAAME,WAAW,GAAGD,QAAQ,CAACE,MAAM;EACnCf,SAAS,CAACgB,OAAO,CAACF,WAAW,CAAC,EAAE,4BAA4B,CAAC;EAC7D,MAAMG,UAAU,GAAG,EAAE;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAEI,CAAC,EAAE,EAAE;IAClC,MAAMC,OAAO,GAAGN,QAAQ,CAACK,CAAC,CAAC;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACnB,kBAAkB,CAACqB,GAAG,CAACD,OAAO,CAAC,EAAE;MAClC;AACZ;AACA;AACA;AACA;MACYf,mBAAmB,CAACe,OAAO,CAAC;IAChC;IACA,MAAME,aAAa,GAAGtB,kBAAkB,CAACuB,GAAG,CAACH,OAAO,CAAC;IACrD,MAAMI,UAAU,GAAG;MAAE,GAAGZ;IAAQ,CAAC;IACjC;AACR;AACA;IACQ,IAAI,OAAOY,UAAU,CAACC,KAAK,KAAK,UAAU,EAAE;MACxCD,UAAU,CAACC,KAAK,GAAGD,UAAU,CAACC,KAAK,CAACN,CAAC,EAAEJ,WAAW,CAAC;IACvD;IACAG,UAAU,CAACQ,IAAI,CAAC,GAAGtB,aAAa,CAACkB,aAAa,EAAE;MAAE,GAAGX,SAAS;MAAEa;IAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtF;EACA,OAAO,IAAItB,qBAAqB,CAACgB,UAAU,CAAC;AAChD;AACA,MAAMS,UAAU,GAAIC,KAAK,IAAKC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7E,SAASG,eAAeA,CAACC,QAAQ,EAAEpB,OAAO,EAAEC,KAAK,EAAE;EAC/C,MAAMK,UAAU,GAAG,EAAE;EACrB,MAAMe,oBAAoB,GAAG1B,4BAA4B,CAACyB,QAAQ,EAAEpB,OAAO,EAAEC,KAAK,CAAC;EACnFoB,oBAAoB,CAACC,OAAO,CAAC,CAAC;IAAEvB,SAAS;IAAEa;EAAW,CAAC,EAAEW,OAAO,KAAK;IACjE,IAAIC,SAAS;IACb,IAAI5B,aAAa,CAAC2B,OAAO,CAAC,EAAE;MACxBC,SAAS,GAAG9B,kBAAkB,CAAC6B,OAAO,EAAExB,SAAS,CAAC0B,OAAO,EAAEb,UAAU,CAACa,OAAO,CAAC;IAClF,CAAC,MACI;MACDD,SAAS,GAAG3B,eAAe,CAAC0B,OAAO,EAAExB,SAAS,EAAEa,UAAU,CAAC;IAC/D;IACAN,UAAU,CAACQ,IAAI,CAACU,SAAS,CAAC;EAC9B,CAAC,CAAC;EACF,OAAO,IAAIlC,qBAAqB,CAACgB,UAAU,CAAC;AAChD;AACA,MAAMoB,mBAAmB,GAAIzB,KAAK,IAAK;EACnC;AACJ;AACA;EACI,SAAS0B,aAAaA,CAACC,wBAAwB,EAAE7B,SAAS,EAAEC,OAAO,EAAE;IACjE,IAAIwB,SAAS;IACb,IAAIT,UAAU,CAACa,wBAAwB,CAAC,EAAE;MACtCJ,SAAS,GAAGL,eAAe,CAACS,wBAAwB,EAAE7B,SAAS,EAAEE,KAAK,CAAC;IAC3E,CAAC,MACI,IAAIV,cAAc,CAACQ,SAAS,CAAC,EAAE;MAChCyB,SAAS,GAAG3B,eAAe,CAAC+B,wBAAwB,EAAE7B,SAAS,EAAEC,OAAO,EAAEC,KAAK,CAAC;IACpF,CAAC,MACI;MACDuB,SAAS,GAAG9B,kBAAkB,CAACkC,wBAAwB,EAAE7B,SAAS,EAAEC,OAAO,CAAC;IAChF;IACA,IAAIC,KAAK,EAAE;MACPA,KAAK,CAACK,UAAU,CAACQ,IAAI,CAACU,SAAS,CAAC;IACpC;IACA,OAAOA,SAAS;EACpB;EACA,OAAOG,aAAa;AACxB,CAAC;AACD,MAAME,OAAO,GAAGH,mBAAmB,CAAC,CAAC;AAErC,SAASG,OAAO,EAAEH,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}