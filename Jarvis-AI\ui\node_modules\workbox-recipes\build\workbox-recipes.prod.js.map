{"version": 3, "file": "workbox-recipes.prod.js", "sources": ["../_version.js", "../warmStrategyCache.js", "../googleFontsCache.js", "../imageCache.js", "../offlineFallback.js", "../pageCache.js", "../staticResourceCache.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:recipes:6.5.4'] && _();\n}\ncatch (e) { }\n", "import './_version.js';\n/**\n * @memberof workbox-recipes\n \n * @param {Object} options\n * @param {string[]} options.urls Paths to warm the strategy's cache with\n * @param {Strategy} options.strategy Strategy to use\n */\nfunction warmStrategyCache(options) {\n    self.addEventListener('install', (event) => {\n        const done = options.urls.map((path) => options.strategy.handleAll({\n            event,\n            request: new Request(path),\n        })[1]);\n        event.waitUntil(Promise.all(done));\n    });\n}\nexport { warmStrategyCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { StaleWhileRevalidate } from 'workbox-strategies/StaleWhileRevalidate.js';\nimport { CacheFirst } from 'workbox-strategies/CacheFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport { ExpirationPlugin } from 'workbox-expiration/ExpirationPlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [Google fonts]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#google_fonts} caching recipe\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cachePrefix] Cache prefix for caching stylesheets and webfonts. Defaults to google-fonts\n * @param {number} [options.maxAgeSeconds] Maximum age, in seconds, that font entries will be cached for. Defaults to 1 year\n * @param {number} [options.maxEntries] Maximum number of fonts that will be cached. Defaults to 30\n */\nfunction googleFontsCache(options = {}) {\n    const sheetCacheName = `${options.cachePrefix || 'google-fonts'}-stylesheets`;\n    const fontCacheName = `${options.cachePrefix || 'google-fonts'}-webfonts`;\n    const maxAgeSeconds = options.maxAgeSeconds || 60 * 60 * 24 * 365;\n    const maxEntries = options.maxEntries || 30;\n    // Cache the Google Fonts stylesheets with a stale-while-revalidate strategy.\n    registerRoute(({ url }) => url.origin === 'https://fonts.googleapis.com', new StaleWhileRevalidate({\n        cacheName: sheetCacheName,\n    }));\n    // Cache the underlying font files with a cache-first strategy for 1 year.\n    registerRoute(({ url }) => url.origin === 'https://fonts.gstatic.com', new CacheFirst({\n        cacheName: fontCacheName,\n        plugins: [\n            new CacheableResponsePlugin({\n                statuses: [0, 200],\n            }),\n            new ExpirationPlugin({\n                maxAgeSeconds,\n                maxEntries,\n            }),\n        ],\n    }));\n}\nexport { googleFontsCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { CacheFirst } from 'workbox-strategies/CacheFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport { ExpirationPlugin } from 'workbox-expiration/ExpirationPlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [image caching recipe]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#caching_images}\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to images\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.destination === 'image';\n * @param {number} [options.maxAgeSeconds] Maximum age, in seconds, that font entries will be cached for. Defaults to 30 days\n * @param {number} [options.maxEntries] Maximum number of images that will be cached. Defaults to 60\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction imageCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.destination === 'image';\n    const cacheName = options.cacheName || 'images';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const maxAgeSeconds = options.maxAgeSeconds || 30 * 24 * 60 * 60;\n    const maxEntries = options.maxEntries || 60;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    plugins.push(new ExpirationPlugin({\n        maxEntries,\n        maxAgeSeconds,\n    }));\n    const strategy = new CacheFirst({\n        cacheName,\n        plugins,\n    });\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { imageCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { setCatchHandler } from 'workbox-routing/setCatchHandler.js';\nimport { matchPrecache } from 'workbox-precaching/matchPrecache.js';\nimport './_version.js';\n/**\n * An implementation of the [comprehensive fallbacks recipe]{@link https://developers.google.com/web/tools/workbox/guides/advanced-recipes#comprehensive_fallbacks}. Be sure to include the fallbacks in your precache injection\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.pageFallback] Precache name to match for pag fallbacks. Defaults to offline.html\n * @param {string} [options.imageFallback] Precache name to match for image fallbacks.\n * @param {string} [options.fontFallback] Precache name to match for font fallbacks.\n */\nfunction offlineFallback(options = {}) {\n    const pageFallback = options.pageFallback || 'offline.html';\n    const imageFallback = options.imageFallback || false;\n    const fontFallback = options.fontFallback || false;\n    self.addEventListener('install', (event) => {\n        const files = [pageFallback];\n        if (imageFallback) {\n            files.push(imageFallback);\n        }\n        if (fontFallback) {\n            files.push(fontFallback);\n        }\n        event.waitUntil(self.caches\n            .open('workbox-offline-fallbacks')\n            .then((cache) => cache.addAll(files)));\n    });\n    const handler = async (options) => {\n        const dest = options.request.destination;\n        const cache = await self.caches.open('workbox-offline-fallbacks');\n        if (dest === 'document') {\n            const match = (await matchPrecache(pageFallback)) ||\n                (await cache.match(pageFallback));\n            return match || Response.error();\n        }\n        if (dest === 'image' && imageFallback !== false) {\n            const match = (await matchPrecache(imageFallback)) ||\n                (await cache.match(imageFallback));\n            return match || Response.error();\n        }\n        if (dest === 'font' && fontFallback !== false) {\n            const match = (await matchPrecache(fontFallback)) ||\n                (await cache.match(fontFallback));\n            return match || Response.error();\n        }\n        return Response.error();\n    };\n    setCatchHandler(handler);\n}\nexport { offlineFallback };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { NetworkFirst } from 'workbox-strategies/NetworkFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport './_version.js';\n/**\n * An implementation of a page caching recipe with a network timeout\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to pages\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.mode === 'navigate';\n * @param {number} [options.networkTimoutSeconds] Maximum amount of time, in seconds, to wait on the network before falling back to cache. Defaults to 3\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction pageCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.mode === 'navigate';\n    const cacheName = options.cacheName || 'pages';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const networkTimeoutSeconds = options.networkTimeoutSeconds || 3;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    const strategy = new NetworkFirst({\n        networkTimeoutSeconds,\n        cacheName,\n        plugins,\n    });\n    // Registers the route\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { pageCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { StaleWhileRevalidate } from 'workbox-strategies/StaleWhileRevalidate.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [CSS and JavaScript files recipe]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#cache_css_and_javascript_files}\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to static-resources\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.destination === 'style' || request.destination === 'script' || request.destination === 'worker';\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction staticResourceCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.destination === 'style' ||\n        request.destination === 'script' ||\n        request.destination === 'worker';\n    const cacheName = options.cacheName || 'static-resources';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    const strategy = new StaleWhileRevalidate({\n        cacheName,\n        plugins,\n    });\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { staticResourceCache };\n"], "names": ["self", "_", "e", "warmStrategyCache", "options", "addEventListener", "event", "done", "urls", "map", "path", "strategy", "handleAll", "request", "Request", "waitUntil", "Promise", "all", "sheetCacheName", "cachePrefix", "fontCacheName", "maxAgeSeconds", "maxEntries", "registerRoute", "url", "origin", "StaleWhileRevalidate", "cacheName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plugins", "CacheableResponsePlugin", "statuses", "ExpirationPlugin", "matchCallback", "destination", "push", "warmCache", "pageFallback", "imageFallback", "fontFallback", "files", "caches", "open", "then", "cache", "addAll", "setCatchHandler", "async", "dest", "matchPrecache", "match", "Response", "error", "mode", "networkTimeoutSeconds", "NetworkFirst"], "mappings": "4FAEA,IACIA,KAAK,0BAA4BC,GACpC,CACD,MAAOC,ICGP,SAASC,EAAkBC,GACvBJ,KAAKK,iBAAiB,WAAYC,UACxBC,EAAOH,EAAQI,KAAKC,KAAKC,GAASN,EAAQO,SAASC,UAAU,CAC/DN,QACAO,QAAS,IAAIC,QAAQJ,KACtB,KACHJ,EAAMS,UAAUC,QAAQC,IAAIV,MAEnC,2BCOD,SAA0BH,EAAU,UAC1Bc,EAAkB,GAAEd,EAAQe,aAAe,6BAC3CC,EAAiB,GAAEhB,EAAQe,aAAe,0BAC1CE,EAAgBjB,EAAQiB,eAAiB,QACzCC,EAAalB,EAAQkB,YAAc,GAEzCC,iBAAc,EAAGC,SAAyB,iCAAfA,EAAIC,QAA2C,IAAIC,uBAAqB,CAC/FC,UAAWT,KAGfK,iBAAc,EAAGC,SAAyB,8BAAfA,EAAIC,QAAwC,IAAIG,aAAW,CAClFD,UAAWP,EACXS,QAAS,CACL,IAAIC,0BAAwB,CACxBC,SAAU,CAAC,EAAG,OAElB,IAAIC,mBAAiB,CACjBX,gBACAC,kBAIf,eCnBD,SAAoBlB,EAAU,UAEpBuB,EAAYvB,EAAQuB,WAAa,SACjCM,EAAgB7B,EAAQ6B,eAFD,GAAGpB,aAAsC,UAAxBA,EAAQqB,aAGhDb,EAAgBjB,EAAQiB,eAAiB,OACzCC,EAAalB,EAAQkB,YAAc,GACnCO,EAAUzB,EAAQyB,SAAW,GACnCA,EAAQM,KAAK,IAAIL,0BAAwB,CACrCC,SAAU,CAAC,EAAG,QAElBF,EAAQM,KAAK,IAAIH,mBAAiB,CAC9BV,aACAD,yBAEEV,EAAW,IAAIiB,aAAW,CAC5BD,YACAE,YAEJN,gBAAcU,EAAetB,GAEzBP,EAAQgC,WACRjC,EAAkB,CAAEK,KAAMJ,EAAQgC,UAAWzB,YAEpD,oBC7BD,SAAyBP,EAAU,UACzBiC,EAAejC,EAAQiC,cAAgB,eACvCC,EAAgBlC,EAAQkC,gBAAiB,EACzCC,EAAenC,EAAQmC,eAAgB,EAC7CvC,KAAKK,iBAAiB,WAAYC,UACxBkC,EAAQ,CAACH,GACXC,GACAE,EAAML,KAAKG,GAEXC,GACAC,EAAML,KAAKI,GAEfjC,EAAMS,UAAUf,KAAKyC,OAChBC,KAAK,6BACLC,MAAMC,GAAUA,EAAMC,OAAOL,SAsBtCM,mBApBgBC,gBACNC,EAAO5C,EAAQS,QAAQqB,YACvBU,QAAc5C,KAAKyC,OAAOC,KAAK,gCACxB,aAATM,EAAqB,cACAC,gBAAcZ,UACxBO,EAAMM,MAAMb,IACPc,SAASC,WAEhB,UAATJ,IAAsC,IAAlBV,EAAyB,cACxBW,gBAAcX,UACxBM,EAAMM,MAAMZ,IACPa,SAASC,WAEhB,SAATJ,IAAoC,IAAjBT,EAAwB,cACtBU,gBAAcV,UACxBK,EAAMM,MAAMX,IACPY,SAASC,eAEtBD,SAASC,OAAhB,GAGP,cCjCD,SAAmBhD,EAAU,UAEnBuB,EAAYvB,EAAQuB,WAAa,QACjCM,EAAgB7B,EAAQ6B,eAFD,GAAGpB,aAA+B,aAAjBA,EAAQwC,MAGhDC,EAAwBlD,EAAQkD,uBAAyB,EACzDzB,EAAUzB,EAAQyB,SAAW,GACnCA,EAAQM,KAAK,IAAIL,0BAAwB,CACrCC,SAAU,CAAC,EAAG,cAEZpB,EAAW,IAAI4C,eAAa,CAC9BD,wBACA3B,YACAE,YAGJN,gBAAcU,EAAetB,GAEzBP,EAAQgC,WACRjC,EAAkB,CAAEK,KAAMJ,EAAQgC,UAAWzB,YAEpD,wBCrBD,SAA6BP,EAAU,UAI7BuB,EAAYvB,EAAQuB,WAAa,mBACjCM,EAAgB7B,EAAQ6B,eAJD,GAAGpB,aAAsC,UAAxBA,EAAQqB,aAC1B,WAAxBrB,EAAQqB,aACgB,WAAxBrB,EAAQqB,aAGNL,EAAUzB,EAAQyB,SAAW,GACnCA,EAAQM,KAAK,IAAIL,0BAAwB,CACrCC,SAAU,CAAC,EAAG,cAEZpB,EAAW,IAAIe,uBAAqB,CACtCC,YACAE,YAEJN,gBAAcU,EAAetB,GAEzBP,EAAQgC,WACRjC,EAAkB,CAAEK,KAAMJ,EAAQgC,UAAWzB,YAEpD"}