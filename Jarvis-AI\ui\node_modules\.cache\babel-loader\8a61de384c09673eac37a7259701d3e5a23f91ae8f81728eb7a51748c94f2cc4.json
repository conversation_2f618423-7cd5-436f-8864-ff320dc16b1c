{"ast": null, "code": "import { observeTimeline } from '../render/dom/scroll/observe.mjs';\nimport { supportsScrollTimeline } from '../render/dom/scroll/supports.mjs';\nclass GroupPlaybackControls {\n  constructor(animations) {\n    this.animations = animations.filter(Boolean);\n  }\n  then(onResolve, onReject) {\n    return Promise.all(this.animations).then(onResolve).catch(onReject);\n  }\n  /**\n   * TODO: Filter out cancelled or stopped animations before returning\n   */\n  getAll(propName) {\n    return this.animations[0][propName];\n  }\n  setAll(propName, newValue) {\n    for (let i = 0; i < this.animations.length; i++) {\n      this.animations[i][propName] = newValue;\n    }\n  }\n  attachTimeline(timeline) {\n    const cancelAll = this.animations.map(animation => {\n      if (supportsScrollTimeline() && animation.attachTimeline) {\n        animation.attachTimeline(timeline);\n      } else {\n        animation.pause();\n        return observeTimeline(progress => {\n          animation.time = animation.duration * progress;\n        }, timeline);\n      }\n    });\n    return () => {\n      cancelAll.forEach((cancelTimeline, i) => {\n        if (cancelTimeline) cancelTimeline();\n        this.animations[i].stop();\n      });\n    };\n  }\n  get time() {\n    return this.getAll(\"time\");\n  }\n  set time(time) {\n    this.setAll(\"time\", time);\n  }\n  get speed() {\n    return this.getAll(\"speed\");\n  }\n  set speed(speed) {\n    this.setAll(\"speed\", speed);\n  }\n  get duration() {\n    let max = 0;\n    for (let i = 0; i < this.animations.length; i++) {\n      max = Math.max(max, this.animations[i].duration);\n    }\n    return max;\n  }\n  runAll(methodName) {\n    this.animations.forEach(controls => controls[methodName]());\n  }\n  play() {\n    this.runAll(\"play\");\n  }\n  pause() {\n    this.runAll(\"pause\");\n  }\n  stop() {\n    this.runAll(\"stop\");\n  }\n  cancel() {\n    this.runAll(\"cancel\");\n  }\n  complete() {\n    this.runAll(\"complete\");\n  }\n}\nexport { GroupPlaybackControls };", "map": {"version": 3, "names": ["observeTimeline", "supportsScrollTimeline", "GroupPlaybackControls", "constructor", "animations", "filter", "Boolean", "then", "onResolve", "onReject", "Promise", "all", "catch", "getAll", "propName", "setAll", "newValue", "i", "length", "attachTimeline", "timeline", "cancelAll", "map", "animation", "pause", "progress", "time", "duration", "for<PERSON>ach", "cancelTimeline", "stop", "speed", "max", "Math", "runAll", "methodName", "controls", "play", "cancel", "complete"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/animation/GroupPlaybackControls.mjs"], "sourcesContent": ["import { observeTimeline } from '../render/dom/scroll/observe.mjs';\nimport { supportsScrollTimeline } from '../render/dom/scroll/supports.mjs';\n\nclass GroupPlaybackControls {\n    constructor(animations) {\n        this.animations = animations.filter(Boolean);\n    }\n    then(onResolve, onReject) {\n        return Promise.all(this.animations).then(onResolve).catch(onReject);\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline) {\n        const cancelAll = this.animations.map((animation) => {\n            if (supportsScrollTimeline() && animation.attachTimeline) {\n                animation.attachTimeline(timeline);\n            }\n            else {\n                animation.pause();\n                return observeTimeline((progress) => {\n                    animation.time = animation.duration * progress;\n                }, timeline);\n            }\n        });\n        return () => {\n            cancelAll.forEach((cancelTimeline, i) => {\n                if (cancelTimeline)\n                    cancelTimeline();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    stop() {\n        this.runAll(\"stop\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\nexport { GroupPlaybackControls };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kCAAkC;AAClE,SAASC,sBAAsB,QAAQ,mCAAmC;AAE1E,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU,CAACC,MAAM,CAACC,OAAO,CAAC;EAChD;EACAC,IAAIA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACtB,OAAOC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACP,UAAU,CAAC,CAACG,IAAI,CAACC,SAAS,CAAC,CAACI,KAAK,CAACH,QAAQ,CAAC;EACvE;EACA;AACJ;AACA;EACII,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI,CAACV,UAAU,CAAC,CAAC,CAAC,CAACU,QAAQ,CAAC;EACvC;EACAC,MAAMA,CAACD,QAAQ,EAAEE,QAAQ,EAAE;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACb,UAAU,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACb,UAAU,CAACa,CAAC,CAAC,CAACH,QAAQ,CAAC,GAAGE,QAAQ;IAC3C;EACJ;EACAG,cAAcA,CAACC,QAAQ,EAAE;IACrB,MAAMC,SAAS,GAAG,IAAI,CAACjB,UAAU,CAACkB,GAAG,CAAEC,SAAS,IAAK;MACjD,IAAItB,sBAAsB,CAAC,CAAC,IAAIsB,SAAS,CAACJ,cAAc,EAAE;QACtDI,SAAS,CAACJ,cAAc,CAACC,QAAQ,CAAC;MACtC,CAAC,MACI;QACDG,SAAS,CAACC,KAAK,CAAC,CAAC;QACjB,OAAOxB,eAAe,CAAEyB,QAAQ,IAAK;UACjCF,SAAS,CAACG,IAAI,GAAGH,SAAS,CAACI,QAAQ,GAAGF,QAAQ;QAClD,CAAC,EAAEL,QAAQ,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,OAAO,MAAM;MACTC,SAAS,CAACO,OAAO,CAAC,CAACC,cAAc,EAAEZ,CAAC,KAAK;QACrC,IAAIY,cAAc,EACdA,cAAc,CAAC,CAAC;QACpB,IAAI,CAACzB,UAAU,CAACa,CAAC,CAAC,CAACa,IAAI,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC;EACL;EACA,IAAIJ,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACb,MAAM,CAAC,MAAM,CAAC;EAC9B;EACA,IAAIa,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACX,MAAM,CAAC,MAAM,EAAEW,IAAI,CAAC;EAC7B;EACA,IAAIK,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAClB,MAAM,CAAC,OAAO,CAAC;EAC/B;EACA,IAAIkB,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAAChB,MAAM,CAAC,OAAO,EAAEgB,KAAK,CAAC;EAC/B;EACA,IAAIJ,QAAQA,CAAA,EAAG;IACX,IAAIK,GAAG,GAAG,CAAC;IACX,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACb,UAAU,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7Ce,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACA,GAAG,EAAE,IAAI,CAAC5B,UAAU,CAACa,CAAC,CAAC,CAACU,QAAQ,CAAC;IACpD;IACA,OAAOK,GAAG;EACd;EACAE,MAAMA,CAACC,UAAU,EAAE;IACf,IAAI,CAAC/B,UAAU,CAACwB,OAAO,CAAEQ,QAAQ,IAAKA,QAAQ,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC;EACjE;EACAE,IAAIA,CAAA,EAAG;IACH,IAAI,CAACH,MAAM,CAAC,MAAM,CAAC;EACvB;EACAV,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACU,MAAM,CAAC,OAAO,CAAC;EACxB;EACAJ,IAAIA,CAAA,EAAG;IACH,IAAI,CAACI,MAAM,CAAC,MAAM,CAAC;EACvB;EACAI,MAAMA,CAAA,EAAG;IACL,IAAI,CAACJ,MAAM,CAAC,QAAQ,CAAC;EACzB;EACAK,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,MAAM,CAAC,UAAU,CAAC;EAC3B;AACJ;AAEA,SAAShC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}