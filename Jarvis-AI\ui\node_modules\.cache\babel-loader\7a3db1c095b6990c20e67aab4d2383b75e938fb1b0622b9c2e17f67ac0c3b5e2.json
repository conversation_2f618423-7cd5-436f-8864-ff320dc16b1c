{"ast": null, "code": "import { createProjectionNode } from './create-projection-node.mjs';\nimport { DocumentProjectionNode } from './DocumentProjectionNode.mjs';\nconst rootProjectionNode = {\n  current: undefined\n};\nconst HTMLProjectionNode = createProjectionNode({\n  measureScroll: instance => ({\n    x: instance.scrollLeft,\n    y: instance.scrollTop\n  }),\n  defaultParent: () => {\n    if (!rootProjectionNode.current) {\n      const documentNode = new DocumentProjectionNode({});\n      documentNode.mount(window);\n      documentNode.setOptions({\n        layoutScroll: true\n      });\n      rootProjectionNode.current = documentNode;\n    }\n    return rootProjectionNode.current;\n  },\n  resetTransform: (instance, value) => {\n    instance.style.transform = value !== undefined ? value : \"none\";\n  },\n  checkIsScrollRoot: instance => Boolean(window.getComputedStyle(instance).position === \"fixed\")\n});\nexport { HTMLProjectionNode, rootProjectionNode };", "map": {"version": 3, "names": ["createProjectionNode", "DocumentProjectionNode", "rootProjectionNode", "current", "undefined", "HTMLProjectionNode", "measureScroll", "instance", "x", "scrollLeft", "y", "scrollTop", "defaultParent", "documentNode", "mount", "window", "setOptions", "layoutScroll", "resetTransform", "value", "style", "transform", "checkIsScrollRoot", "Boolean", "getComputedStyle", "position"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs"], "sourcesContent": ["import { createProjectionNode } from './create-projection-node.mjs';\nimport { DocumentProjectionNode } from './DocumentProjectionNode.mjs';\n\nconst rootProjectionNode = {\n    current: undefined,\n};\nconst HTMLProjectionNode = createProjectionNode({\n    measureScroll: (instance) => ({\n        x: instance.scrollLeft,\n        y: instance.scrollTop,\n    }),\n    defaultParent: () => {\n        if (!rootProjectionNode.current) {\n            const documentNode = new DocumentProjectionNode({});\n            documentNode.mount(window);\n            documentNode.setOptions({ layoutScroll: true });\n            rootProjectionNode.current = documentNode;\n        }\n        return rootProjectionNode.current;\n    },\n    resetTransform: (instance, value) => {\n        instance.style.transform = value !== undefined ? value : \"none\";\n    },\n    checkIsScrollRoot: (instance) => Boolean(window.getComputedStyle(instance).position === \"fixed\"),\n});\n\nexport { HTMLProjectionNode, rootProjectionNode };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,sBAAsB,QAAQ,8BAA8B;AAErE,MAAMC,kBAAkB,GAAG;EACvBC,OAAO,EAAEC;AACb,CAAC;AACD,MAAMC,kBAAkB,GAAGL,oBAAoB,CAAC;EAC5CM,aAAa,EAAGC,QAAQ,KAAM;IAC1BC,CAAC,EAAED,QAAQ,CAACE,UAAU;IACtBC,CAAC,EAAEH,QAAQ,CAACI;EAChB,CAAC,CAAC;EACFC,aAAa,EAAEA,CAAA,KAAM;IACjB,IAAI,CAACV,kBAAkB,CAACC,OAAO,EAAE;MAC7B,MAAMU,YAAY,GAAG,IAAIZ,sBAAsB,CAAC,CAAC,CAAC,CAAC;MACnDY,YAAY,CAACC,KAAK,CAACC,MAAM,CAAC;MAC1BF,YAAY,CAACG,UAAU,CAAC;QAAEC,YAAY,EAAE;MAAK,CAAC,CAAC;MAC/Cf,kBAAkB,CAACC,OAAO,GAAGU,YAAY;IAC7C;IACA,OAAOX,kBAAkB,CAACC,OAAO;EACrC,CAAC;EACDe,cAAc,EAAEA,CAACX,QAAQ,EAAEY,KAAK,KAAK;IACjCZ,QAAQ,CAACa,KAAK,CAACC,SAAS,GAAGF,KAAK,KAAKf,SAAS,GAAGe,KAAK,GAAG,MAAM;EACnE,CAAC;EACDG,iBAAiB,EAAGf,QAAQ,IAAKgB,OAAO,CAACR,MAAM,CAACS,gBAAgB,CAACjB,QAAQ,CAAC,CAACkB,QAAQ,KAAK,OAAO;AACnG,CAAC,CAAC;AAEF,SAASpB,kBAAkB,EAAEH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}