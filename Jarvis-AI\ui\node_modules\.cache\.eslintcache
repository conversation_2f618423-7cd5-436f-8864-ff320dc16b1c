[{"C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js": "1", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js": "2", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js": "3", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js": "4", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js": "5", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js": "6", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js": "7", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js": "8", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js": "9", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js": "10", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js": "11", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js": "12", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js": "13", "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js": "14", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\index.js": "15", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\App.js": "16", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\Jarvis.js": "17", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\HomeScreen.js": "18", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ChatScreen.js": "19", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\AnimatedButton.js": "20", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\ExpandableSidebar.js": "21", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\CuteMenuIcon.js": "22", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\hooks\\useJarvis.js": "23", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\layout\\Footer.js": "24", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\MinimizedJarvis.js": "25", "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\services\\jarvisApi.js": "26"}, {"size": 254, "mtime": 1750762253811, "results": "27", "hashOfConfig": "28"}, {"size": 2839, "mtime": 1751008229522, "results": "29", "hashOfConfig": "28"}, {"size": 5858, "mtime": 1750966557156, "results": "30", "hashOfConfig": "28"}, {"size": 11538, "mtime": 1751024912323, "results": "31", "hashOfConfig": "28"}, {"size": 910, "mtime": 1750784249896, "results": "32", "hashOfConfig": "28"}, {"size": 1166, "mtime": 1750784262464, "results": "33", "hashOfConfig": "28"}, {"size": 1566, "mtime": 1750784237894, "results": "34", "hashOfConfig": "28"}, {"size": 6839, "mtime": 1750966415822, "results": "35", "hashOfConfig": "28"}, {"size": 1577, "mtime": 1750784209479, "results": "36", "hashOfConfig": "28"}, {"size": 1673, "mtime": 1750784224221, "results": "37", "hashOfConfig": "28"}, {"size": 940, "mtime": 1750785802095, "results": "38", "hashOfConfig": "28"}, {"size": 9562, "mtime": 1750838136053, "results": "39", "hashOfConfig": "28"}, {"size": 13812, "mtime": 1750968668896, "results": "40", "hashOfConfig": "28"}, {"size": 18133, "mtime": 1751022245383, "results": "41", "hashOfConfig": "28"}, {"size": 254, "mtime": 1750762253811, "results": "42", "hashOfConfig": "43"}, {"size": 2839, "mtime": 1751008229522, "results": "44", "hashOfConfig": "43"}, {"size": 14221, "mtime": 1751646979921, "results": "45", "hashOfConfig": "43"}, {"size": 5858, "mtime": 1750966557156, "results": "46", "hashOfConfig": "43"}, {"size": 14648, "mtime": 1751647084780, "results": "47", "hashOfConfig": "43"}, {"size": 1566, "mtime": 1750784237894, "results": "48", "hashOfConfig": "43"}, {"size": 9562, "mtime": 1750838136053, "results": "49", "hashOfConfig": "43"}, {"size": 6839, "mtime": 1750966415822, "results": "50", "hashOfConfig": "43"}, {"size": 5925, "mtime": 1751646883028, "results": "51", "hashOfConfig": "43"}, {"size": 1166, "mtime": 1750784262464, "results": "52", "hashOfConfig": "43"}, {"size": 18133, "mtime": 1751022245383, "results": "53", "hashOfConfig": "43"}, {"size": 3315, "mtime": 1751646812216, "results": "54", "hashOfConfig": "43"}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1edix7f", {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "oz9tep", {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\HomeScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\Jarvis.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\AnimatedButton.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\CuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\StatusIndicator.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\TestCuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\ExpandableSidebar.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ChatScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\UI Designolddd\\jarvis-ui\\src\\components\\ui\\MinimizedJarvis.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\index.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\App.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\Jarvis.js", ["133", "134", "135", "136"], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\HomeScreen.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ChatScreen.js", ["137", "138", "139", "140", "141"], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\AnimatedButton.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\ExpandableSidebar.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\CuteMenuIcon.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\hooks\\useJarvis.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\layout\\Footer.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\components\\ui\\MinimizedJarvis.js", [], [], "C:\\Users\\<USER>\\My Code Work\\AI Adventures\\Jarvis-AI\\ui\\src\\services\\jarvisApi.js", [], [], {"ruleId": "142", "severity": 1, "message": "143", "line": 1, "column": 20, "nodeType": "144", "messageId": "145", "endLine": 1, "endColumn": 29}, {"ruleId": "142", "severity": 1, "message": "146", "line": 15, "column": 5, "nodeType": "144", "messageId": "145", "endLine": 15, "endColumn": 17}, {"ruleId": "142", "severity": 1, "message": "147", "line": 21, "column": 5, "nodeType": "144", "messageId": "145", "endLine": 21, "endColumn": 14}, {"ruleId": "142", "severity": 1, "message": "148", "line": 314, "column": 27, "nodeType": "144", "messageId": "145", "endLine": 314, "endColumn": 35}, {"ruleId": "142", "severity": 1, "message": "149", "line": 20, "column": 5, "nodeType": "144", "messageId": "145", "endLine": 20, "endColumn": 10}, {"ruleId": "142", "severity": 1, "message": "150", "line": 23, "column": 5, "nodeType": "144", "messageId": "145", "endLine": 23, "endColumn": 10}, {"ruleId": "142", "severity": 1, "message": "147", "line": 25, "column": 5, "nodeType": "144", "messageId": "145", "endLine": 25, "endColumn": 14}, {"ruleId": "142", "severity": 1, "message": "151", "line": 26, "column": 5, "nodeType": "144", "messageId": "145", "endLine": 26, "endColumn": 24}, {"ruleId": "142", "severity": 1, "message": "152", "line": 27, "column": 5, "nodeType": "144", "messageId": "145", "endLine": 27, "endColumn": 17}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'isProcessing' is assigned a value but never used.", "'speakText' is assigned a value but never used.", "'response' is assigned a value but never used.", "'state' is assigned a value but never used.", "'error' is assigned a value but never used.", "'conversationHistory' is assigned a value but never used.", "'clearHistory' is assigned a value but never used."]