import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const MinimizedJarvis = ({ onExpand, isProcessing = false }) => {
  const [state, setState] = useState('rest');
  const [isVisible, setIsVisible] = useState(true);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [showControls, setShowControls] = useState(false);



  // ========================================
  // JARVIS ACTIVITY MEDIA CONFIGURATION
  // ========================================
  //
  // TO CHANGE ACTIVITY IMAGES/VIDEOS IN THE ICON SPHERE:
  //
  // 1. Replace the 'media' filename with your desired file
  // 2. Place your media files in the 'public/assets/' folder
  // 3. Supported formats: .gif, .mp4, .webm
  // 4. You can also change the colors and text for each state
  //
  // Current media files used:
  // - startup.gif    (System initialization animation)
  // - rest.mp4       (Idle/standby state - main resting animation)
  // - listening.gif  (When JARVIS is listening for input)
  // - thinking.gif   (When JARVIS is processing/analyzing)
  // - speaking.gif   (When JARVIS is responding/speaking)
  //
  // ========================================
  // ACTIVITY-SPECIFIC POSITIONING & SIZING
  // ========================================
  //
  // Each activity can have different position and size:
  //
  // POSITION CONTROLS:
  // - translateX: Horizontal ('-50%' = center, '-40%' = right, '-60%' = left)
  // - translateY: Vertical ('-50%' = center, '-60%' = up, '-40%' = down)
  //
  // SIZE CONTROLS:
  // - scale: Size multiplier (1.0 = normal, 1.5 = bigger, 0.8 = smaller)
  //
  // EXAMPLES:
  // - Bigger thinking icon: scale: 1.6, translateY: '-60%'
  // - Smaller speaking icon: scale: 1.1, translateY: '-45%'
  // - Move listening right: translateX: '-40%'
  //
  // ========================================

  const stateConfig = {
    startup: {
      media: 'startup.gif',        // CHANGE: Replace with your startup animation file
      title: 'SYSTEM INITIALIZATION',
      description: 'J.A.R.V.I.S. ONLINE',
      color: '#00eeff',            // CHANGE: Startup glow color (cyan)
      loop: true,
      // POSITION & SIZE SETTINGS:
      scale: 1,                  // CHANGE: Size multiplier (1.0 = normal, 1.5 = 50% bigger)
      translateX: '-50%',          // CHANGE: Horizontal position (-50% = centered)
      translateY: '-50%'           // CHANGE: Vertical position (-50% = centered, -60% = higher up)
    },
    rest: {
      media: 'rest.mp4',           // CHANGE: Replace with your idle/resting animation file
      title: 'STANDBY MODE',
      description: 'AWAITING COMMAND',
      color: '#00eeff',            // CHANGE: Rest glow color (cyan)
      loop: true,
      // POSITION & SIZE SETTINGS:
      scale: 1.1,                  // CHANGE: Size multiplier
      translateX: '-45%',          // CHANGE: Horizontal position
      translateY: '-45%'           // CHANGE: Vertical position
    },
    listening: {
      media: 'listening.gif',      // CHANGE: Replace with your listening animation file
      title: 'LISTENING',
      description: 'PROCESSING AUDIO INPUT',
      color: '#ff00aa',            // CHANGE: Listening glow color (pink)
      loop: true,
      // POSITION & SIZE SETTINGS:
      scale: 1,                  // CHANGE: Size multiplier (slightly bigger for listening)
      translateX: '-50%',          // CHANGE: Horizontal position
      translateY: '-50%'           // CHANGE: Vertical position (slightly higher)
    },
    thinking: {
      media: 'thinking.gif',       // CHANGE: Replace with your thinking/processing animation file
      title: 'PROCESSING',
      description: 'ANALYZING REQUEST',
      color: '#ff9900',            // CHANGE: Thinking glow color (orange)
      loop: true,
      // POSITION & SIZE SETTINGS:
      scale: 1.1,                  // CHANGE: Size multiplier (bigger for emphasis)
      translateX: '-45%',          // CHANGE: Horizontal position
      translateY: '-48%'           // CHANGE: Vertical position (moved up to center better)
    },
    speaking: {
      media: 'speaking.gif',       // CHANGE: Replace with your speaking/responding animation file
      title: 'RESPONDING',
      description: 'OUTPUT GENERATION',
      color: '#00ff88',            // CHANGE: Speaking glow color (green)
      loop: true,
      // POSITION & SIZE SETTINGS:
      scale: 1,                 // CHANGE: Size multiplier (medium size)
      translateX: '-50%',          // CHANGE: Horizontal position
      translateY: '-50%'           // CHANGE: Vertical position (slightly lower)
    }
  };


  // Enhanced magnetized snap to optimized positions
  const snapToCorner = (x, y) => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const componentWidth = 280;
    const componentHeight = 360;

    // Optimized margins for different screen areas
    const topMargin = 12;           // Close to top edge
    const sideMargin = 12;          // Close to side edges
    const bottomMargin = 12;        // Close to bottom edge
    const headerOffset = 80;       // Below typical headers/navbars
    const chatInputOffset = 100;   // Above chat input areas

    // Define magnetized snap zones with priorities
    const snapZones = [
      // Top corners (below header area)
      {
        x: windowWidth - componentWidth - sideMargin,
        y: headerOffset,
        corner: 'top-right',
        priority: 1,
        magnetRadius: 150
      },
      {
        x: sideMargin,
        y: headerOffset,
        corner: 'top-left',
        priority: 2,
        magnetRadius: 150
      },

      // Middle right/left (vertical center)
      {
        x: windowWidth - componentWidth - sideMargin,
        y: (windowHeight - componentHeight) / 2,
        corner: 'middle-right',
        priority: 3,
        magnetRadius: 120
      },
      {
        x: sideMargin,
        y: (windowHeight - componentHeight) / 2,
        corner: 'middle-left',
        priority: 4,
        magnetRadius: 120
      },

      // Bottom corners (above chat input)
      {
        x: windowWidth - componentWidth - sideMargin,
        y: windowHeight - componentHeight - chatInputOffset,
        corner: 'bottom-right',
        priority: 5,
        magnetRadius: 130
      },
      {
        x: sideMargin,
        y: windowHeight - componentHeight - chatInputOffset,
        corner: 'bottom-left',
        priority: 6,
        magnetRadius: 130
      },

      // Extreme corners (very edge positions)
      {
        x: windowWidth - componentWidth - topMargin,
        y: topMargin,
        corner: 'extreme-top-right',
        priority: 7,
        magnetRadius: 100
      },
      {
        x: windowWidth - componentWidth - sideMargin,
        y: windowHeight - componentHeight - bottomMargin,
        corner: 'extreme-bottom-right',
        priority: 8,
        magnetRadius: 100
      }
    ];

    // Find the best snap position with magnetism
    let bestSnap = snapZones[0];
    let minWeightedDistance = Infinity;

    snapZones.forEach(zone => {
      const distance = Math.sqrt(Math.pow(x - zone.x, 2) + Math.pow(y - zone.y, 2));

      // Apply magnetism - closer zones within magnet radius get priority boost
      const isInMagnetZone = distance <= zone.magnetRadius;
      const priorityWeight = zone.priority * 0.1; // Lower priority number = better
      const magnetBoost = isInMagnetZone ? 0.5 : 1; // 50% boost if in magnet zone

      const weightedDistance = (distance * magnetBoost) + priorityWeight;

      if (weightedDistance < minWeightedDistance) {
        minWeightedDistance = weightedDistance;
        bestSnap = zone;
      }
    });

    return { x: bestSnap.x, y: bestSnap.y, corner: bestSnap.corner };
  };

  // Handle drag events
  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDrag = (event, info) => {
    // Keep drag functionality without visual preview
    // The snap will happen on drag end
  };

  const handleDragEnd = (event, info) => {
    setIsDragging(false);

    const newPos = snapToCorner(info.point.x - 140, info.point.y - 180);

    // Smooth animation to magnetized position
    setTimeout(() => {
      setPosition(newPos);
    }, 50);
  };

  // Double-tap functionality
  const [tapCount, setTapCount] = useState(0);
  const [tapTimer, setTapTimer] = useState(null);

  const handleDoubleTap = () => {
    setTapCount(prev => prev + 1);

    if (tapTimer) {
      clearTimeout(tapTimer);
    }

    const timer = setTimeout(() => {
      if (tapCount + 1 >= 2) {
        // Double tap detected - show controls
        setShowControls(true);

        // Auto-hide controls after 3 seconds
        setTimeout(() => {
          setShowControls(false);
        }, 3000);
      }
      setTapCount(0);
    }, 300); // 300ms window for double tap

    setTapTimer(timer);
  };

  // Simulate JARVIS states based on processing
  useEffect(() => {
    if (isProcessing) {
      setState('thinking');
      const timer = setTimeout(() => {
        setState('speaking');
        setTimeout(() => setState('rest'), 2000);
      }, 1500);
      return () => clearTimeout(timer);
    } else {
      setState('rest');
    }
  }, [isProcessing]);

  // Auto-cycle through states for demo
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isProcessing) {
        setState(prev => {
          switch (prev) {
            case 'rest': return 'listening';
            case 'listening': return 'thinking';
            case 'thinking': return 'speaking';
            case 'speaking': return 'rest';
            default: return 'rest';
          }
        });
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [isProcessing]);

  const getStateColor = () => {
    return stateConfig[state]?.color || '#00eeff';
  };

  const getStateText = () => {
    return stateConfig[state]?.title || 'STANDBY MODE';
  };

  if (!isVisible) return null;

  return (
    <>


      {/* Main Draggable Window */}
    <motion.div
      drag
      dragMomentum={false}
      dragElastic={0.1}
      dragConstraints={{
        left: 0,
        right: window.innerWidth - 280,
        top: 0,
        bottom: window.innerHeight - 360
      }}
      onDragStart={handleDragStart}
      onDrag={handleDrag}
      onDragEnd={handleDragEnd}
      onClick={handleDoubleTap}
      initial={{ scale: 0, opacity: 0, x: window.innerWidth - 196, y: 76 }} // Start at top-right, lower position
      animate={{
        scale: 1,
        opacity: 1,
        x: position.x || window.innerWidth - 296,
        y: position.y || 76,
        transition: {
          type: "spring",
          damping: 25,
          stiffness: 200,
          duration: 0.6
        }
      }}
      exit={{ scale: 0, opacity: 0 }}
      className={`fixed z-30 select-none ${
        isDragging ? 'cursor-grabbing' : 'cursor-grab'
      }`}
      style={{ width: '280px', height: '360px' }}
      whileHover={{ scale: 1.02 }}
      whileDrag={{ scale: 1.05, zIndex: 50 }}
    >


      {/* Control Buttons - Only visible when double-tapped */}
      <AnimatePresence>
        {showControls && (
          <>
            {/* Close/Minimize Button */}
            <motion.button
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ type: "spring", damping: 15, stiffness: 300 }}
              onClick={() => setIsVisible(false)}
              className="absolute -top-2 -right-2 w-10 h-10 rounded-full bg-red-500/30 hover:bg-red-500/50 border-2 border-red-500/70 flex items-center justify-center text-red-300 hover:text-red-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-red-500/30"
              onMouseDown={(e) => e.stopPropagation()} // Prevent drag when clicking button
              style={{
                boxShadow: '0 0 20px rgba(239, 68, 68, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'
              }}
            >
              ×
            </motion.button>

            {/* Expand Button */}
            <motion.button
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ type: "spring", damping: 15, stiffness: 300, delay: 0.1 }}
              onClick={onExpand}
              className="absolute -top-2 -left-2 w-10 h-10 rounded-full bg-cyan-500/30 hover:bg-cyan-500/50 border-2 border-cyan-500/70 flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-cyan-500/30"
              title="Expand JARVIS"
              onMouseDown={(e) => e.stopPropagation()} // Prevent drag when clicking button
              style={{
                boxShadow: '0 0 20px rgba(6, 182, 212, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'
              }}
            >
              ↗
            </motion.button>
          </>
        )}
      </AnimatePresence>

      {/* JARVIS Media Display - Larger and more circular with Audio Reactivity */}
      <div className="flex flex-col items-center justify-center h-full">
        {/* Media Container */}
        <motion.div
          className="relative w-56 h-56 rounded-full overflow-hidden mb-3"
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={state}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.05 }}
              transition={{
                duration: 0.6,
                ease: [0.4, 0, 0.2, 1],
                opacity: { duration: 0.4 }
              }}
              className="absolute inset-0 flex items-center justify-center rounded-full overflow-hidden"
            >
              {stateConfig[state].media.endsWith('.mp4') ? (
                <video
                  src={`/assets/${stateConfig[state].media}`}
                  autoPlay
                  muted
                  loop
                  playsInline
                  className="rounded-full"
                  style={{
                    clipPath: 'circle(50% at 50% 50%)',
                    width: '120%',
                    height: '120%',
                    objectFit: 'cover',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,
                    pointerEvents: 'none',
                    userSelect: 'none',
                    transition: 'all 0.3s ease-in-out'
                  }}
                  onError={(e) => {
                    // Fallback to SVG if video fails to load
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <img
                  src={`/assets/${stateConfig[state].media}`}
                  alt={state}
                  draggable={false}
                  className="rounded-full"
                  style={{
                    clipPath: 'circle(50% at 50% 50%)',
                    width: '120%',
                    height: '120%',
                    objectFit: 'cover',
                    imageRendering: 'auto',
                    filter: 'contrast(1.1) brightness(1.05)',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,
                    transition: 'all 0.3s ease-in-out',
                    pointerEvents: 'none',
                    userSelect: 'none'
                  }}
                  onError={(e) => {
                    // Fallback to SVG if GIF fails to load
                    e.target.style.display = 'none';
                    e.target.parentElement.innerHTML = `
                      <div class="w-full h-full rounded-full border-2 flex items-center justify-center"
                           style="border-color: ${getStateColor()}; box-shadow: 0 0 20px ${getStateColor()}40;">
                        <svg viewBox="0 0 100 100" class="w-16 h-16">
                          <circle cx="50" cy="50" r="30" fill="none" stroke="${getStateColor()}" stroke-width="2"/>
                          <circle cx="50" cy="50" r="20" fill="none" stroke="${getStateColor()}" stroke-width="1.5" opacity="0.7"/>
                          <circle cx="50" cy="50" r="8" fill="${getStateColor()}"/>
                          <path d="M30 50 L70 50 M50 30 L50 70" stroke="${getStateColor()}" stroke-width="1.5" opacity="0.8"/>
                        </svg>
                      </div>
                    `;
                  }}
                />
              )}
            </motion.div>
          </AnimatePresence>

          {/* Blinking Circle Animation */}
          <motion.div
            className="absolute inset-0 rounded-full pointer-events-none border-2"
            style={{
              borderColor: getStateColor(),
              animation: `blinkingCircle 2s ease-in-out infinite`,
              boxShadow: `0 0 20px ${getStateColor()}60`
            }}
          />
        </motion.div>

        {/* Status Text */}
        <div className="text-center">
          <div
            className="text-base font-bold transition-colors duration-300 mb-1"
            style={{
              color: getStateColor()
            }}
          >
            {getStateText()}
          </div>
          <div className="text-sm text-gray-300 font-medium tracking-wider">
            JARVIS
          </div>
          <div className="text-xs text-gray-400">
            {stateConfig[state]?.description}
          </div>
        </div>
      </div>


    </motion.div>
    </>
  );
};

export default MinimizedJarvis;
