{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\components\\\\HomeScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport Footer from './layout/Footer';\nimport AnimatedButton from './ui/AnimatedButton';\n\n// Import the existing cute menu icon (keeping your original implementation)\nimport CuteMenuIcon from './ui/CuteMenuIcon';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeScreen = ({\n  onEnterJarvis\n}) => {\n  _s();\n  const [isEntering, setIsEntering] = useState(false);\n  const [showExitAnimation, setShowExitAnimation] = useState(false);\n  const [menuOpen, setMenuOpen] = useState(false);\n  const wallpaperRef = useRef(null);\n  const exitAnimationRef = useRef(null);\n\n  // Handle entering Jarvis mode\n  const handleEnterJarvis = () => {\n    setIsEntering(true);\n    setShowExitAnimation(true);\n\n    // Wait for exit animation to complete before transitioning\n    setTimeout(() => {\n      onEnterJarvis();\n    }, 2000); // Adjust timing based on screenexit.gif duration\n  };\n\n  // Handle menu actions\n  const handleMenuToggle = isOpen => {\n    setMenuOpen(isOpen);\n  };\n  const handleSettingsClick = () => {\n    console.log('Settings clicked');\n  };\n  const handleHelpClick = () => {\n    console.log('Help clicked');\n  };\n  const handleVoiceCommandsClick = () => {\n    console.log('Voice Commands clicked');\n  };\n  const handleAboutClick = () => {\n    console.log('About clicked');\n  };\n\n  // Additive dissolve effect for wallpaper looping\n  useEffect(() => {\n    const wallpaper = wallpaperRef.current;\n    if (wallpaper) {\n      wallpaper.style.mixBlendMode = 'screen';\n      wallpaper.style.filter = 'brightness(0.8) contrast(1.2)';\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homescreen-container\",\n    children: [/*#__PURE__*/_jsxDEV(CuteMenuIcon, {\n      onMenuToggle: handleMenuToggle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",\n      children: /*#__PURE__*/_jsxDEV(ExpandableSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"wallpaper-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        ref: wallpaperRef,\n        src: \"/assets/HomeSettingsscreen.gif\",\n        alt: \"Animated Wallpaper\",\n        className: \"wallpaper-background\",\n        onError: e => {\n          // Fallback to a gradient if gif not found\n          e.target.style.display = 'none';\n          e.target.parentElement.style.background = 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"wallpaper-overlay\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), showExitAnimation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exit-animation-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        ref: exitAnimationRef,\n        src: \"/assets/screenexit.gif\",\n        alt: \"Screen Exit Animation\",\n        className: \"exit-animation\",\n        onError: e => {\n          // Fallback animation if gif not found\n          e.target.style.display = 'none';\n          e.target.parentElement.innerHTML = '<div class=\"fallback-exit-animation\"></div>';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `homescreen-content ${isEntering ? 'entering' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"marvel-logo\",\n          children: \"MARVEL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"main-title\",\n          children: \"JARVIS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subtitle\",\n          children: \"PRESS \\u26A1 TO START\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"jarvis-icon-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"jarvis-icon\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon-core\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 100 100\",\n              className: \"jarvis-svg\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"30\",\n                className: \"outer-ring\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"20\",\n                className: \"middle-ring\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"10\",\n                className: \"inner-core\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M30 50 L70 50 M50 30 L50 70\",\n                className: \"cross-lines\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatedButton, {\n          onClick: handleEnterJarvis,\n          variant: \"primary\",\n          size: \"large\",\n          loading: isEntering,\n          className: \"mt-4\",\n          children: isEntering ? 'ACTIVATING...' : 'ACTIVATE JARVIS'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bottom-menu\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatedButton, {\n          onClick: handleSettingsClick,\n          variant: \"secondary\",\n          size: \"small\",\n          className: \"menu-button\",\n          children: \"\\u2699\\uFE0F Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatedButton, {\n          onClick: handleVoiceCommandsClick,\n          variant: \"secondary\",\n          size: \"small\",\n          className: \"menu-button\",\n          children: \"\\uD83C\\uDFA4 Voice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatedButton, {\n          onClick: handleHelpClick,\n          variant: \"secondary\",\n          size: \"small\",\n          className: \"menu-button\",\n          children: \"\\u2753 Help\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatedButton, {\n          onClick: handleAboutClick,\n          variant: \"secondary\",\n          size: \"small\",\n          className: \"menu-button\",\n          children: \"\\u2139\\uFE0F About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {\n      onHomeClick: () => console.log('Home clicked'),\n      onSettingsClick: handleSettingsClick,\n      onHelpClick: handleHelpClick,\n      showNavigation: !menuOpen // Hide footer nav when menu is open\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(HomeScreen, \"U1xHmCk1EN5k4W8fXYRFD7qxYSo=\");\n_c = HomeScreen;\nexport default HomeScreen;\nvar _c;\n$RefreshReg$(_c, \"HomeScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Footer", "AnimatedButton", "CuteMenuIcon", "ExpandableSidebar", "jsxDEV", "_jsxDEV", "HomeScreen", "on<PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "isEntering", "setIsEntering", "showExitAnimation", "setShowExitAnimation", "menuOpen", "setMenuOpen", "wallpaperRef", "exitAnimationRef", "handleE<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "handleMenuToggle", "isOpen", "handleSettingsClick", "console", "log", "handleHelpClick", "handleVoiceCommandsClick", "handleAboutClick", "wallpaper", "current", "style", "mixBlendMode", "filter", "className", "children", "onMenuToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "src", "alt", "onError", "e", "target", "display", "parentElement", "background", "innerHTML", "viewBox", "cx", "cy", "r", "d", "onClick", "variant", "size", "loading", "onHomeClick", "onSettingsClick", "onHelpClick", "showNavigation", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/HomeScreen.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport Footer from './layout/Footer';\nimport AnimatedButton from './ui/AnimatedButton';\n\n// Import the existing cute menu icon (keeping your original implementation)\nimport CuteMenuIcon from './ui/CuteMenuIcon';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\n\nconst HomeScreen = ({ onEnterJarvis }) => {\n  const [isEntering, setIsEntering] = useState(false);\n  const [showExitAnimation, setShowExitAnimation] = useState(false);\n  const [menuOpen, setMenuOpen] = useState(false);\n  const wallpaperRef = useRef(null);\n  const exitAnimationRef = useRef(null);\n\n  // Handle entering Jarvis mode\n  const handleEnterJarvis = () => {\n    setIsEntering(true);\n    setShowExitAnimation(true);\n\n    // Wait for exit animation to complete before transitioning\n    setTimeout(() => {\n      onEnterJarvis();\n    }, 2000); // Adjust timing based on screenexit.gif duration\n  };\n\n  // Handle menu actions\n  const handleMenuToggle = (isOpen) => {\n    setMenuOpen(isOpen);\n  };\n\n  const handleSettingsClick = () => {\n    console.log('Settings clicked');\n  };\n\n  const handleHelpClick = () => {\n    console.log('Help clicked');\n  };\n\n  const handleVoiceCommandsClick = () => {\n    console.log('Voice Commands clicked');\n  };\n\n  const handleAboutClick = () => {\n    console.log('About clicked');\n  };\n\n  // Additive dissolve effect for wallpaper looping\n  useEffect(() => {\n    const wallpaper = wallpaperRef.current;\n    if (wallpaper) {\n      wallpaper.style.mixBlendMode = 'screen';\n      wallpaper.style.filter = 'brightness(0.8) contrast(1.2)';\n    }\n  }, []);\n\n  return (\n    <div className=\"homescreen-container\">\n      {/* Cute Menu Icon in Top Left */}\n      <CuteMenuIcon onMenuToggle={handleMenuToggle} />\n      \n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Animated Wallpaper Background */}\n      <div className=\"wallpaper-container\">\n        <img\n          ref={wallpaperRef}\n          src=\"/assets/HomeSettingsscreen.gif\"\n          alt=\"Animated Wallpaper\"\n          className=\"wallpaper-background\"\n          onError={(e) => {\n            // Fallback to a gradient if gif not found\n            e.target.style.display = 'none';\n            e.target.parentElement.style.background =\n              'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)';\n          }}\n        />\n\n        {/* Overlay for better text readability */}\n        <div className=\"wallpaper-overlay\"></div>\n      </div>\n\n      {/* Exit Animation Overlay */}\n      {showExitAnimation && (\n        <div className=\"exit-animation-overlay\">\n          <img\n            ref={exitAnimationRef}\n            src=\"/assets/screenexit.gif\"\n            alt=\"Screen Exit Animation\"\n            className=\"exit-animation\"\n            onError={(e) => {\n              // Fallback animation if gif not found\n              e.target.style.display = 'none';\n              e.target.parentElement.innerHTML = '<div class=\"fallback-exit-animation\"></div>';\n            }}\n          />\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className={`homescreen-content ${isEntering ? 'entering' : ''}`}>\n        {/* Spider-Man Style Title */}\n        <div className=\"title-section\">\n          <div className=\"marvel-logo\">MARVEL</div>\n          <h1 className=\"main-title\">JARVIS</h1>\n          <div className=\"subtitle\">PRESS ⚡ TO START</div>\n        </div>\n\n        {/* Central Jarvis Icon/Button - Now using AnimatedButton */}\n        <div className=\"jarvis-icon-container\">\n          <div className=\"jarvis-icon\">\n            <div className=\"icon-glow\"></div>\n            <div className=\"icon-core\">\n              <svg viewBox=\"0 0 100 100\" className=\"jarvis-svg\">\n                <circle cx=\"50\" cy=\"50\" r=\"30\" className=\"outer-ring\" />\n                <circle cx=\"50\" cy=\"50\" r=\"20\" className=\"middle-ring\" />\n                <circle cx=\"50\" cy=\"50\" r=\"10\" className=\"inner-core\" />\n                <path d=\"M30 50 L70 50 M50 30 L50 70\" className=\"cross-lines\" />\n              </svg>\n            </div>\n          </div>\n          <AnimatedButton\n            onClick={handleEnterJarvis}\n            variant=\"primary\"\n            size=\"large\"\n            loading={isEntering}\n            className=\"mt-4\"\n          >\n            {isEntering ? 'ACTIVATING...' : 'ACTIVATE JARVIS'}\n          </AnimatedButton>\n        </div>\n\n        {/* Bottom Menu - Now using AnimatedButtons */}\n        <div className=\"bottom-menu\">\n          <AnimatedButton\n            onClick={handleSettingsClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            ⚙️ Settings\n          </AnimatedButton>\n\n          <AnimatedButton\n            onClick={handleVoiceCommandsClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            🎤 Voice\n          </AnimatedButton>\n\n          <AnimatedButton\n            onClick={handleHelpClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            ❓ Help\n          </AnimatedButton>\n\n          <AnimatedButton\n            onClick={handleAboutClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            ℹ️ About\n          </AnimatedButton>\n        </div>\n      </div>\n\n      {/* Footer with additional navigation */}\n      <Footer\n        onHomeClick={() => console.log('Home clicked')}\n        onSettingsClick={handleSettingsClick}\n        onHelpClick={handleHelpClick}\n        showNavigation={!menuOpen} // Hide footer nav when menu is open\n      />\n    </div>\n\n      \n\n\n  );\n};\n\nexport default HomeScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,cAAc,MAAM,qBAAqB;;AAEhD;AACA,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,iBAAiB,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMkB,YAAY,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMiB,gBAAgB,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAMkB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BP,aAAa,CAAC,IAAI,CAAC;IACnBE,oBAAoB,CAAC,IAAI,CAAC;;IAE1B;IACAM,UAAU,CAAC,MAAM;MACfX,aAAa,CAAC,CAAC;IACjB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAIC,MAAM,IAAK;IACnCN,WAAW,CAACM,MAAM,CAAC;EACrB,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B,CAAC;EAED,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCH,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BJ,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM6B,SAAS,GAAGZ,YAAY,CAACa,OAAO;IACtC,IAAID,SAAS,EAAE;MACbA,SAAS,CAACE,KAAK,CAACC,YAAY,GAAG,QAAQ;MACvCH,SAAS,CAACE,KAAK,CAACE,MAAM,GAAG,+BAA+B;IAC1D;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE1B,OAAA;IAAK2B,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBAEnC5B,OAAA,CAACH,YAAY;MAACgC,YAAY,EAAEf;IAAiB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhDjC,OAAA;MAAK2B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnE5B,OAAA,CAACF,iBAAiB;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGNjC,OAAA;MAAK2B,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC5B,OAAA;QACEkC,GAAG,EAAExB,YAAa;QAClByB,GAAG,EAAC,gCAAgC;QACpCC,GAAG,EAAC,oBAAoB;QACxBT,SAAS,EAAC,sBAAsB;QAChCU,OAAO,EAAGC,CAAC,IAAK;UACd;UACAA,CAAC,CAACC,MAAM,CAACf,KAAK,CAACgB,OAAO,GAAG,MAAM;UAC/BF,CAAC,CAACC,MAAM,CAACE,aAAa,CAACjB,KAAK,CAACkB,UAAU,GACrC,gEAAgE;QACpE;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGFjC,OAAA;QAAK2B,SAAS,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,EAGL3B,iBAAiB,iBAChBN,OAAA;MAAK2B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrC5B,OAAA;QACEkC,GAAG,EAAEvB,gBAAiB;QACtBwB,GAAG,EAAC,wBAAwB;QAC5BC,GAAG,EAAC,uBAAuB;QAC3BT,SAAS,EAAC,gBAAgB;QAC1BU,OAAO,EAAGC,CAAC,IAAK;UACd;UACAA,CAAC,CAACC,MAAM,CAACf,KAAK,CAACgB,OAAO,GAAG,MAAM;UAC/BF,CAAC,CAACC,MAAM,CAACE,aAAa,CAACE,SAAS,GAAG,6CAA6C;QAClF;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDjC,OAAA;MAAK2B,SAAS,EAAE,sBAAsBvB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;MAAAwB,QAAA,gBAEnE5B,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzCjC,OAAA;UAAI2B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtCjC,OAAA;UAAK2B,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAGNjC,OAAA;QAAK2B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC5B,OAAA;UAAK2B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B5B,OAAA;YAAK2B,SAAS,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCjC,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB5B,OAAA;cAAK4C,OAAO,EAAC,aAAa;cAACjB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC/C5B,OAAA;gBAAQ6C,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,IAAI;gBAACpB,SAAS,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDjC,OAAA;gBAAQ6C,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,IAAI;gBAACpB,SAAS,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzDjC,OAAA;gBAAQ6C,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,IAAI;gBAACpB,SAAS,EAAC;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDjC,OAAA;gBAAMgD,CAAC,EAAC,6BAA6B;gBAACrB,SAAS,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjC,OAAA,CAACJ,cAAc;UACbqD,OAAO,EAAErC,iBAAkB;UAC3BsC,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEhD,UAAW;UACpBuB,SAAS,EAAC,MAAM;UAAAC,QAAA,EAEfxB,UAAU,GAAG,eAAe,GAAG;QAAiB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAGNjC,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5B,OAAA,CAACJ,cAAc;UACbqD,OAAO,EAAEjC,mBAAoB;UAC7BkC,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,OAAO;UACZxB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAEjBjC,OAAA,CAACJ,cAAc;UACbqD,OAAO,EAAE7B,wBAAyB;UAClC8B,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,OAAO;UACZxB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAEjBjC,OAAA,CAACJ,cAAc;UACbqD,OAAO,EAAE9B,eAAgB;UACzB+B,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,OAAO;UACZxB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAEjBjC,OAAA,CAACJ,cAAc;UACbqD,OAAO,EAAE5B,gBAAiB;UAC1B6B,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,OAAO;UACZxB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACxB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA,CAACL,MAAM;MACL0D,WAAW,EAAEA,CAAA,KAAMpC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE;MAC/CoC,eAAe,EAAEtC,mBAAoB;MACrCuC,WAAW,EAAEpC,eAAgB;MAC7BqC,cAAc,EAAE,CAAChD,QAAS,CAAC;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAMV,CAAC;AAAC9B,EAAA,CApLIF,UAAU;AAAAwD,EAAA,GAAVxD,UAAU;AAsLhB,eAAeA,UAAU;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}