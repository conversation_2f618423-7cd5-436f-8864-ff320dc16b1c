{"ast": null, "code": "/**\n * Jarvis API Service\n * Handles communication between React frontend and Python backend\n */\n\nconst API_BASE_URL = 'http://localhost:5000/api';\nclass JarvisApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  /**\n   * Make HTTP request to API\n   */\n  async makeRequest(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const defaultOptions = {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    };\n    const requestOptions = {\n      ...defaultOptions,\n      ...options\n    };\n    try {\n      const response = await fetch(url, requestOptions);\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Health check\n   */\n  async healthCheck() {\n    return this.makeRequest('/health');\n  }\n\n  /**\n   * Get current Jarvis state\n   */\n  async getState() {\n    return this.makeRequest('/state');\n  }\n\n  /**\n   * Set Jarvis state\n   */\n  async setState(state) {\n    return this.makeRequest('/state', {\n      method: 'POST',\n      body: JSON.stringify({\n        state\n      })\n    });\n  }\n\n  /**\n   * Send chat message and get response\n   */\n  async sendMessage(message) {\n    return this.makeRequest('/chat', {\n      method: 'POST',\n      body: JSON.stringify({\n        message\n      })\n    });\n  }\n\n  /**\n   * Start speech recognition\n   */\n  async startSpeechRecognition() {\n    return this.makeRequest('/speech-to-text', {\n      method: 'POST'\n    });\n  }\n\n  /**\n   * Convert text to speech\n   */\n  async textToSpeech(text) {\n    return this.makeRequest('/text-to-speech', {\n      method: 'POST',\n      body: JSON.stringify({\n        text\n      })\n    });\n  }\n\n  /**\n   * Get conversation history\n   */\n  async getConversationHistory() {\n    return this.makeRequest('/conversation-history');\n  }\n\n  /**\n   * Clear conversation history\n   */\n  async clearConversationHistory() {\n    return this.makeRequest('/conversation-history', {\n      method: 'DELETE'\n    });\n  }\n\n  /**\n   * Check if API server is running\n   */\n  async isServerRunning() {\n    try {\n      await this.healthCheck();\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Poll state changes (for real-time updates)\n   */\n  async pollState(callback, interval = 1000) {\n    const poll = async () => {\n      try {\n        const state = await this.getState();\n        callback(state);\n      } catch (error) {\n        console.error('State polling error:', error);\n      }\n    };\n\n    // Initial poll\n    await poll();\n\n    // Set up interval polling\n    return setInterval(poll, interval);\n  }\n\n  /**\n   * Stop polling\n   */\n  stopPolling(intervalId) {\n    if (intervalId) {\n      clearInterval(intervalId);\n    }\n  }\n}\n\n// Create singleton instance\nconst jarvisApi = new JarvisApiService();\nexport default jarvisApi;\n\n// Export individual methods for convenience\nexport const {\n  healthCheck,\n  getState,\n  setState,\n  sendMessage,\n  startSpeechRecognition,\n  textToSpeech,\n  getConversationHistory,\n  clearConversationHistory,\n  isServerRunning,\n  pollState,\n  stopPolling\n} = jarvisApi;", "map": {"version": 3, "names": ["API_BASE_URL", "JarvisApiService", "constructor", "baseURL", "makeRequest", "endpoint", "options", "url", "defaultOptions", "headers", "requestOptions", "response", "fetch", "ok", "errorData", "json", "catch", "Error", "error", "status", "statusText", "console", "healthCheck", "getState", "setState", "state", "method", "body", "JSON", "stringify", "sendMessage", "message", "startSpeechRecognition", "textToSpeech", "text", "getConversationHistory", "clearConversationHistory", "isServerRunning", "pollState", "callback", "interval", "poll", "setInterval", "stopPolling", "intervalId", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/services/jarvisApi.js"], "sourcesContent": ["/**\n * Jarvis API Service\n * Handles communication between React frontend and Python backend\n */\n\nconst API_BASE_URL = 'http://localhost:5000/api';\n\nclass JarvisApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  /**\n   * Make HTTP request to API\n   */\n  async makeRequest(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const defaultOptions = {\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    };\n\n    const requestOptions = { ...defaultOptions, ...options };\n\n    try {\n      const response = await fetch(url, requestOptions);\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Health check\n   */\n  async healthCheck() {\n    return this.makeRequest('/health');\n  }\n\n  /**\n   * Get current Jarvis state\n   */\n  async getState() {\n    return this.makeRequest('/state');\n  }\n\n  /**\n   * Set Jarvis state\n   */\n  async setState(state) {\n    return this.makeRequest('/state', {\n      method: 'POST',\n      body: JSON.stringify({ state }),\n    });\n  }\n\n  /**\n   * Send chat message and get response\n   */\n  async sendMessage(message) {\n    return this.makeRequest('/chat', {\n      method: 'POST',\n      body: JSON.stringify({ message }),\n    });\n  }\n\n  /**\n   * Start speech recognition\n   */\n  async startSpeechRecognition() {\n    return this.makeRequest('/speech-to-text', {\n      method: 'POST',\n    });\n  }\n\n  /**\n   * Convert text to speech\n   */\n  async textToSpeech(text) {\n    return this.makeRequest('/text-to-speech', {\n      method: 'POST',\n      body: JSON.stringify({ text }),\n    });\n  }\n\n  /**\n   * Get conversation history\n   */\n  async getConversationHistory() {\n    return this.makeRequest('/conversation-history');\n  }\n\n  /**\n   * Clear conversation history\n   */\n  async clearConversationHistory() {\n    return this.makeRequest('/conversation-history', {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Check if API server is running\n   */\n  async isServerRunning() {\n    try {\n      await this.healthCheck();\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Poll state changes (for real-time updates)\n   */\n  async pollState(callback, interval = 1000) {\n    const poll = async () => {\n      try {\n        const state = await this.getState();\n        callback(state);\n      } catch (error) {\n        console.error('State polling error:', error);\n      }\n    };\n\n    // Initial poll\n    await poll();\n\n    // Set up interval polling\n    return setInterval(poll, interval);\n  }\n\n  /**\n   * Stop polling\n   */\n  stopPolling(intervalId) {\n    if (intervalId) {\n      clearInterval(intervalId);\n    }\n  }\n}\n\n// Create singleton instance\nconst jarvisApi = new JarvisApiService();\n\nexport default jarvisApi;\n\n// Export individual methods for convenience\nexport const {\n  healthCheck,\n  getState,\n  setState,\n  sendMessage,\n  startSpeechRecognition,\n  textToSpeech,\n  getConversationHistory,\n  clearConversationHistory,\n  isServerRunning,\n  pollState,\n  stopPolling,\n} = jarvisApi;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,MAAMA,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGH,YAAY;EAC7B;;EAEA;AACF;AACA;EACE,MAAMI,WAAWA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,GAAGE,QAAQ,EAAE;IACxC,MAAMG,cAAc,GAAG;MACrBC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC;IAED,MAAMC,cAAc,GAAG;MAAE,GAAGF,cAAc;MAAE,GAAGF;IAAQ,CAAC;IAExD,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACL,GAAG,EAAEG,cAAc,CAAC;MAEjD,IAAI,CAACC,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAIC,KAAK,CAACH,SAAS,CAACI,KAAK,IAAI,QAAQP,QAAQ,CAACQ,MAAM,KAAKR,QAAQ,CAACS,UAAU,EAAE,CAAC;MACvF;MAEA,OAAO,MAAMT,QAAQ,CAACI,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,0BAA0Bb,QAAQ,GAAG,EAAEa,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMI,WAAWA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAClB,WAAW,CAAC,SAAS,CAAC;EACpC;;EAEA;AACF;AACA;EACE,MAAMmB,QAAQA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnB,WAAW,CAAC,QAAQ,CAAC;EACnC;;EAEA;AACF;AACA;EACE,MAAMoB,QAAQA,CAACC,KAAK,EAAE;IACpB,OAAO,IAAI,CAACrB,WAAW,CAAC,QAAQ,EAAE;MAChCsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEJ;MAAM,CAAC;IAChC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMK,WAAWA,CAACC,OAAO,EAAE;IACzB,OAAO,IAAI,CAAC3B,WAAW,CAAC,OAAO,EAAE;MAC/BsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEE;MAAQ,CAAC;IAClC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMC,sBAAsBA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAAC5B,WAAW,CAAC,iBAAiB,EAAE;MACzCsB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMO,YAAYA,CAACC,IAAI,EAAE;IACvB,OAAO,IAAI,CAAC9B,WAAW,CAAC,iBAAiB,EAAE;MACzCsB,MAAM,EAAE,MAAM;MACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEK;MAAK,CAAC;IAC/B,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMC,sBAAsBA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAAC/B,WAAW,CAAC,uBAAuB,CAAC;EAClD;;EAEA;AACF;AACA;EACE,MAAMgC,wBAAwBA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAAChC,WAAW,CAAC,uBAAuB,EAAE;MAC/CsB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMW,eAAeA,CAAA,EAAG;IACtB,IAAI;MACF,MAAM,IAAI,CAACf,WAAW,CAAC,CAAC;MACxB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACE,MAAMoB,SAASA,CAACC,QAAQ,EAAEC,QAAQ,GAAG,IAAI,EAAE;IACzC,MAAMC,IAAI,GAAG,MAAAA,CAAA,KAAY;MACvB,IAAI;QACF,MAAMhB,KAAK,GAAG,MAAM,IAAI,CAACF,QAAQ,CAAC,CAAC;QACnCgB,QAAQ,CAACd,KAAK,CAAC;MACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdG,OAAO,CAACH,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF,CAAC;;IAED;IACA,MAAMuB,IAAI,CAAC,CAAC;;IAEZ;IACA,OAAOC,WAAW,CAACD,IAAI,EAAED,QAAQ,CAAC;EACpC;;EAEA;AACF;AACA;EACEG,WAAWA,CAACC,UAAU,EAAE;IACtB,IAAIA,UAAU,EAAE;MACdC,aAAa,CAACD,UAAU,CAAC;IAC3B;EACF;AACF;;AAEA;AACA,MAAME,SAAS,GAAG,IAAI7C,gBAAgB,CAAC,CAAC;AAExC,eAAe6C,SAAS;;AAExB;AACA,OAAO,MAAM;EACXxB,WAAW;EACXC,QAAQ;EACRC,QAAQ;EACRM,WAAW;EACXE,sBAAsB;EACtBC,YAAY;EACZE,sBAAsB;EACtBC,wBAAwB;EACxBC,eAAe;EACfC,SAAS;EACTK;AACF,CAAC,GAAGG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}