{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\components\\\\Jarvis.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport { useJarvis } from '../hooks/useJarvis';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Jarvis = ({\n  onExitToHome,\n  onEnterChat\n}) => {\n  _s();\n  // Local UI state\n  const [micActive, setMicActive] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n\n  // Jarvis API integration\n  const {\n    state,\n    isProcessing,\n    isServerConnected,\n    error,\n    updateState,\n    sendMessage,\n    startSpeechRecognition,\n    speakText,\n    retryConnection\n  } = useJarvis();\n\n  // Handle mic toggle with state switching and speech recognition\n  const handleMicToggle = async checked => {\n    setMicActive(checked);\n    if (checked) {\n      // Turn on mic -> start speech recognition\n      try {\n        await updateState('listening');\n        const response = await startSpeechRecognition();\n\n        // If speech was recognized and processed, navigate to chat\n        if (response && response.response) {\n          onEnterChat(response.response);\n        }\n      } catch (error) {\n        console.error('Speech recognition failed:', error);\n        setMicActive(false);\n        await updateState('rest');\n      }\n    } else {\n      // Turn off mic -> return to rest mode\n      if (state === 'listening') {\n        await updateState('rest');\n      }\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      y: 20,\n      opacity: 0\n    },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  // State content configuration\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      loop: true\n    },\n    rest: {\n      media: 'rest.mp4',\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      loop: true\n    },\n    listening: {\n      media: 'listening.gif',\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      loop: true\n    },\n    thinking: {\n      media: 'thinking.gif',\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      loop: true\n    },\n    speaking: {\n      media: 'speaking.gif',\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      loop: true\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex items-center space-x-2 px-3 py-2 rounded-lg ${isServerConnected ? 'bg-green-900/50 border border-green-500/30' : 'bg-red-900/50 border border-red-500/30'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-2 h-2 rounded-full ${isServerConnected ? 'bg-green-400' : 'bg-red-400'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xs ${isServerConnected ? 'text-green-300' : 'text-red-300'}`,\n          children: isServerConnected ? 'Backend Connected' : 'Backend Disconnected'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), !isServerConnected && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: retryConnection,\n          className: \"text-xs text-red-300 hover:text-red-100 underline ml-2\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-16 right-4 z-20 max-w-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-900/80 border border-red-500/50 rounded-lg p-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 text-red-400 flex-shrink-0 mt-0.5\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-red-300\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",\n      children: /*#__PURE__*/_jsxDEV(ExpandableSidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-center justify-center space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative w-96 h-96 rounded-full overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.95\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 1.05\n            },\n            transition: {\n              duration: 0.6,\n              ease: [0.4, 0, 0.2, 1],\n              opacity: {\n                duration: 0.4\n              }\n            },\n            className: \"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\",\n            children: stateConfig[state].media.endsWith('.mp4') ? /*#__PURE__*/_jsxDEV(\"video\", {\n              src: `/assets/${stateConfig[state].media}`,\n              autoPlay: true,\n              muted: true,\n              loop: true,\n              playsInline: true,\n              className: \"w-full h-full object-cover rounded-full\",\n              style: {\n                clipPath: 'circle(50% at 50% 50%)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `/assets/${stateConfig[state].media}`,\n              alt: state,\n              className: `w-full h-full object-cover rounded-full ${state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''}`,\n              style: {\n                clipPath: 'circle(50% at 50% 50%)',\n                imageRendering: 'auto',\n                filter: 'contrast(1.1) brightness(1.05)',\n                transition: 'all 0.3s ease-in-out'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)\n          }, state, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"absolute inset-0 rounded-full border-4 pointer-events-none\",\n          style: {\n            borderColor: stateConfig[state].color\n          },\n          animate: {\n            opacity: [0.3, 0.8, 0.3],\n            scale: [1, 1.1, 1],\n            boxShadow: `0 0 20px ${stateConfig[state].color}60`\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center space-y-4\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          className: \"text-4xl font-bold tracking-tighter\",\n          style: {\n            color: stateConfig[state].color\n          },\n          variants: itemVariants,\n          children: stateConfig[state].title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          className: \"text-xl text-cyan-200 font-light\",\n          variants: itemVariants,\n          children: stateConfig[state].description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"mt-12\",\n        variants: itemVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"container\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: micActive,\n            onChange: e => handleMicToggle(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"checkmark\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"icon No\",\n              viewBox: \"0 0 24 24\",\n              fill: \"#dc6b6b\",\n              style: {\n                color: '#dc6b6b'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                x1: \"6\",\n                y1: \"4\",\n                x2: \"18\",\n                y2: \"20\",\n                stroke: \"#dc6b6b\",\n                strokeWidth: \"2.5\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"name No\",\n              children: \"Mic Off\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"icon Yes\",\n              viewBox: \"0 0 24 24\",\n              fill: \"currentColor\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"8\",\n                r: \"1\",\n                opacity: \"0.6\",\n                children: [/*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"r\",\n                  values: \"1;2;1\",\n                  dur: \"1s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"opacity\",\n                  values: \"0.6;0.2;0.6\",\n                  dur: \"1s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"12\",\n                cy: \"8\",\n                r: \"3\",\n                opacity: \"0.3\",\n                children: [/*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"r\",\n                  values: \"3;4;3\",\n                  dur: \"1.5s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"animate\", {\n                  attributeName: \"opacity\",\n                  values: \"0.3;0.1;0.3\",\n                  dur: \"1.5s\",\n                  repeatCount: \"indefinite\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"name Yes\",\n              children: \"Listening\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"flex justify-center\",\n        variants: itemVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `form-control ${isInputFocused ? 'focused' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"input\",\n            placeholder: \"Type something intelligent (Press Enter to chat)\",\n            value: inputText,\n            onChange: e => setInputText(e.target.value),\n            onFocus: () => setIsInputFocused(true),\n            onBlur: () => setIsInputFocused(false),\n            onKeyDown: async e => {\n              if (e.key === 'Enter' && inputText.trim()) {\n                // Handle text input submission - send to backend and open chat screen\n                const message = inputText.trim();\n                setInputText('');\n                try {\n                  const response = await sendMessage(message);\n                  onEnterChat(message);\n                } catch (error) {\n                  console.error('Failed to send message:', error);\n                  // Still navigate to chat even if backend fails\n                  onEnterChat(message);\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"input-border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-6 left-6\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"menu\",\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [Object.keys(stateConfig).map(s => /*#__PURE__*/_jsxDEV(motion.button, {\n          className: `link ${state === s ? 'active' : ''}`,\n          style: {\n            backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',\n            border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'\n          },\n          whileHover: {\n            scale: 1.05,\n            boxShadow: `0 0 12px ${stateConfig[s].color}50`\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => updateState(s),\n          variants: itemVariants,\n          title: s.toUpperCase(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"link-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",\n              style: {\n                backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',\n                color: 'white',\n                fontSize: '10px'\n              },\n              children: s.charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"link-title\",\n            children: s.charAt(0).toUpperCase() + s.slice(1)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)]\n        }, s, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)), onExitToHome && /*#__PURE__*/_jsxDEV(motion.button, {\n          className: \"link\",\n          style: {\n            backgroundColor: 'transparent',\n            border: '1px solid rgba(255, 255, 255, 0.3)'\n          },\n          whileHover: {\n            scale: 1.05,\n            boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: onExitToHome,\n          variants: itemVariants,\n          title: \"HOME\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"link-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\",\n              style: {\n                backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                color: 'black',\n                fontSize: '10px'\n              },\n              children: \"H\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"link-title\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n};\n_s(Jarvis, \"Ro+Pz+H3TqpKmsPr0r6rJJxO4Vo=\", false, function () {\n  return [useJarvis];\n});\n_c = Jarvis;\nexport default Jarvis;\nvar _c;\n$RefreshReg$(_c, \"Jarvis\");", "map": {"version": 3, "names": ["useState", "useEffect", "motion", "AnimatePresence", "ExpandableSidebar", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON>", "onExitToHome", "onEnterChat", "_s", "micActive", "setMicActive", "inputText", "setInputText", "isInputFocused", "setIsInputFocused", "state", "isProcessing", "isServerConnected", "error", "updateState", "sendMessage", "startSpeechRecognition", "speakText", "retryConnection", "handleMicToggle", "checked", "response", "console", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "stateConfig", "startup", "media", "title", "description", "color", "loop", "rest", "listening", "thinking", "speaking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "div", "mode", "initial", "scale", "animate", "exit", "ease", "endsWith", "src", "autoPlay", "muted", "playsInline", "style", "clipPath", "alt", "imageRendering", "filter", "borderColor", "boxShadow", "repeat", "Infinity", "variants", "h1", "p", "type", "onChange", "e", "target", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "strokeLinecap", "cx", "cy", "r", "attributeName", "values", "dur", "repeatCount", "placeholder", "value", "onFocus", "onBlur", "onKeyDown", "key", "trim", "message", "Object", "keys", "map", "s", "button", "backgroundColor", "border", "whileHover", "whileTap", "toUpperCase", "fontSize", "char<PERSON>t", "slice", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/Jarvis.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport { useJarvis } from '../hooks/useJarvis';\n\nconst Jarvis = ({ onExitToHome, onEnterChat }) => {\n  // Local UI state\n  const [micActive, setMicActive] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n\n  // Jarvis API integration\n  const {\n    state,\n    isProcessing,\n    isServerConnected,\n    error,\n    updateState,\n    sendMessage,\n    startSpeechRecognition,\n    speakText,\n    retryConnection\n  } = useJarvis();\n\n\n  // Handle mic toggle with state switching and speech recognition\n  const handleMicToggle = async (checked) => {\n    setMicActive(checked);\n\n    if (checked) {\n      // Turn on mic -> start speech recognition\n      try {\n        await updateState('listening');\n        const response = await startSpeechRecognition();\n\n        // If speech was recognized and processed, navigate to chat\n        if (response && response.response) {\n          onEnterChat(response.response);\n        }\n      } catch (error) {\n        console.error('Speech recognition failed:', error);\n        setMicActive(false);\n        await updateState('rest');\n      }\n    } else {\n      // Turn off mic -> return to rest mode\n      if (state === 'listening') {\n        await updateState('rest');\n      }\n    }\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: { staggerChildren: 0.1 }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  // State content configuration\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',\n      loop: true\n    },\n    rest: {\n      media: 'rest.mp4',\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',\n      loop: true\n    },\n    listening: {\n      media: 'listening.gif',\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',\n      loop: true\n    },\n    thinking: {\n      media: 'thinking.gif',\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',\n      loop: true\n    },\n    speaking: {\n      media: 'speaking.gif',\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',\n      loop: true\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-black p-8 relative\">\n\n      {/* Connection Status Indicator */}\n      <div className=\"fixed top-4 right-4 z-20\">\n        <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${isServerConnected\n            ? 'bg-green-900/50 border border-green-500/30'\n            : 'bg-red-900/50 border border-red-500/30'\n          }`}>\n          <div className={`w-2 h-2 rounded-full ${isServerConnected ? 'bg-green-400' : 'bg-red-400'\n            }`} />\n          <span className={`text-xs ${isServerConnected ? 'text-green-300' : 'text-red-300'\n            }`}>\n            {isServerConnected ? 'Backend Connected' : 'Backend Disconnected'}\n          </span>\n          {!isServerConnected && (\n            <button\n              onClick={retryConnection}\n              className=\"text-xs text-red-300 hover:text-red-100 underline ml-2\"\n            >\n              Retry\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"fixed top-16 right-4 z-20 max-w-sm\">\n          <div className=\"bg-red-900/80 border border-red-500/50 rounded-lg p-3\">\n            <div className=\"flex items-start space-x-2\">\n              <svg className=\"w-5 h-5 text-red-400 flex-shrink-0 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n              <div>\n                <p className=\"text-sm text-red-300\">{error}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Centered Media Display */}\n      <div className=\"flex flex-col items-center justify-center space-y-8\">\n        <motion.div\n          className=\"relative w-96 h-96 rounded-full overflow-hidden\"\n        >\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={state}\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 1.05 }}\n              transition={{\n                duration: 0.6,\n                ease: [0.4, 0, 0.2, 1],\n                opacity: { duration: 0.4 }\n              }}\n              className=\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden motion-div\"\n            >\n              {stateConfig[state].media.endsWith('.mp4') ? (\n                <video\n                  src={`/assets/${stateConfig[state].media}`}\n                  autoPlay\n                  muted\n                  loop\n                  playsInline\n                  className=\"w-full h-full object-cover rounded-full\"\n                  style={{ clipPath: 'circle(50% at 50% 50%)' }}\n                />\n              ) : (\n                <img\n                  src={`/assets/${stateConfig[state].media}`}\n                  alt={state}\n                  className={`w-full h-full object-cover rounded-full ${state === 'thinking' ? 'transform translate-x-1 translate-y-1' : ''\n                    }`}\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    imageRendering: 'auto',\n                    filter: 'contrast(1.1) brightness(1.05)',\n                    transition: 'all 0.3s ease-in-out'\n                  }}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Pulsing Halo */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full border-4 pointer-events-none\"\n            style={{ borderColor: stateConfig[state].color }}\n            animate={{\n              opacity: [0.3, 0.8, 0.3],\n              scale: [1, 1.1, 1],\n              boxShadow: `0 0 20px ${stateConfig[state].color}60`\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity\n            }}\n          />\n        </motion.div>\n\n        {/* Status Text */}\n        <motion.div\n          className=\"text-center space-y-4\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <motion.h1\n            className=\"text-4xl font-bold tracking-tighter\"\n            style={{\n              color: stateConfig[state].color\n            }}\n            variants={itemVariants}\n          >\n            {stateConfig[state].title}\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl text-cyan-200 font-light\"\n            variants={itemVariants}\n          >\n            {stateConfig[state].description}\n          </motion.p>\n        </motion.div>\n\n        {/* Mic Button - Center Bottom */}\n        <motion.div\n          className=\"mt-12\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <label className=\"container\">\n            <input\n              type=\"checkbox\"\n              checked={micActive}\n              onChange={(e) => handleMicToggle(e.target.checked)}\n            />\n            <div className=\"checkmark\">\n              {/* Mic Off Icon - with vertical cross line */}\n              <svg className=\"icon No\" viewBox=\"0 0 24 24\" fill=\"#dc6b6b\" style={{ color: '#dc6b6b' }}>\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\" />\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\" />\n                {/* Tilted cross line towards right */}\n                <line\n                  x1=\"6\" y1=\"4\"\n                  x2=\"18\" y2=\"20\"\n                  stroke=\"#dc6b6b\"\n                  strokeWidth=\"2.5\"\n                  strokeLinecap=\"round\"\n                />\n              </svg>\n              <span className=\"name No\">Mic Off</span>\n\n              {/* Mic On Icon - active listening */}\n              <svg className=\"icon Yes\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\" />\n                <path d=\"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z\" />\n                {/* Sound waves indicator */}\n                <circle cx=\"12\" cy=\"8\" r=\"1\" opacity=\"0.6\">\n                  <animate attributeName=\"r\" values=\"1;2;1\" dur=\"1s\" repeatCount=\"indefinite\" />\n                  <animate attributeName=\"opacity\" values=\"0.6;0.2;0.6\" dur=\"1s\" repeatCount=\"indefinite\" />\n                </circle>\n                <circle cx=\"12\" cy=\"8\" r=\"3\" opacity=\"0.3\">\n                  <animate attributeName=\"r\" values=\"3;4;3\" dur=\"1.5s\" repeatCount=\"indefinite\" />\n                  <animate attributeName=\"opacity\" values=\"0.3;0.1;0.3\" dur=\"1.5s\" repeatCount=\"indefinite\" />\n                </circle>\n              </svg>\n              <span className=\"name Yes\">Listening</span>\n            </div>\n          </label>\n        </motion.div>\n\n        {/* Text Input - Below Mic Button */}\n        <motion.div\n          className=\"flex justify-center\"\n          variants={itemVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>\n            <input\n              type=\"text\"\n              className=\"input\"\n              placeholder=\"Type something intelligent (Press Enter to chat)\"\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onFocus={() => setIsInputFocused(true)}\n              onBlur={() => setIsInputFocused(false)}\n              onKeyDown={async (e) => {\n                if (e.key === 'Enter' && inputText.trim()) {\n                  // Handle text input submission - send to backend and open chat screen\n                  const message = inputText.trim();\n                  setInputText('');\n\n                  try {\n                    const response = await sendMessage(message);\n                    onEnterChat(message);\n                  } catch (error) {\n                    console.error('Failed to send message:', error);\n                    // Still navigate to chat even if backend fails\n                    onEnterChat(message);\n                  }\n                }\n              }}\n            />\n            <div className=\"input-border\"></div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* State Control Menu - Bottom Left */}\n      <div className=\"absolute bottom-6 left-6\">\n        <motion.div\n          className=\"menu\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {Object.keys(stateConfig).map((s) => (\n            <motion.button\n              key={s}\n              className={`link ${state === s ? 'active' : ''}`}\n              style={{\n                backgroundColor: state === s ? stateConfig[s].color + '20' : 'transparent',\n                border: state === s ? `1px solid ${stateConfig[s].color}70` : '1px solid transparent'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: `0 0 12px ${stateConfig[s].color}50`\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => updateState(s)}\n              variants={itemVariants}\n              title={s.toUpperCase()}\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: state === s ? stateConfig[s].color : 'rgba(0, 238, 255, 0.6)',\n                    color: 'white',\n                    fontSize: '10px'\n                  }}\n                >\n                  {s.charAt(0).toUpperCase()}\n                </div>\n              </div>\n              <span className=\"link-title\">\n                {s.charAt(0).toUpperCase() + s.slice(1)}\n              </span>\n            </motion.button>\n          ))}\n\n          {/* Home Button */}\n          {onExitToHome && (\n            <motion.button\n              className=\"link\"\n              style={{\n                backgroundColor: 'transparent',\n                border: '1px solid rgba(255, 255, 255, 0.3)'\n              }}\n              whileHover={{\n                scale: 1.05,\n                boxShadow: '0 0 12px rgba(255, 255, 255, 0.5)'\n              }}\n              whileTap={{ scale: 0.95 }}\n              onClick={onExitToHome}\n              variants={itemVariants}\n              title=\"HOME\"\n            >\n              <div className=\"link-icon\">\n                <div\n                  className=\"w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    backgroundColor: 'rgba(255, 255, 255, 0.6)',\n                    color: 'black',\n                    fontSize: '10px'\n                  }}\n                >\n                  H\n                </div>\n              </div>\n              <span className=\"link-title\">Home</span>\n            </motion.button>\n          )}\n        </motion.div>\n      </div>\n\n\n\n    </div>\n  );\n};\n\nexport default Jarvis;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,SAASC,SAAS,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,MAAM,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAChD;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM;IACJkB,KAAK;IACLC,YAAY;IACZC,iBAAiB;IACjBC,KAAK;IACLC,WAAW;IACXC,WAAW;IACXC,sBAAsB;IACtBC,SAAS;IACTC;EACF,CAAC,GAAGrB,SAAS,CAAC,CAAC;;EAGf;EACA,MAAMsB,eAAe,GAAG,MAAOC,OAAO,IAAK;IACzCf,YAAY,CAACe,OAAO,CAAC;IAErB,IAAIA,OAAO,EAAE;MACX;MACA,IAAI;QACF,MAAMN,WAAW,CAAC,WAAW,CAAC;QAC9B,MAAMO,QAAQ,GAAG,MAAML,sBAAsB,CAAC,CAAC;;QAE/C;QACA,IAAIK,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,EAAE;UACjCnB,WAAW,CAACmB,QAAQ,CAACA,QAAQ,CAAC;QAChC;MACF,CAAC,CAAC,OAAOR,KAAK,EAAE;QACdS,OAAO,CAACT,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDR,YAAY,CAAC,KAAK,CAAC;QACnB,MAAMS,WAAW,CAAC,MAAM,CAAC;MAC3B;IACF,CAAC,MAAM;MACL;MACA,IAAIJ,KAAK,KAAK,WAAW,EAAE;QACzB,MAAMI,WAAW,CAAC,MAAM,CAAC;MAC3B;IACF;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IACrC;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBL,MAAM,EAAE;MAAEM,CAAC,EAAE,EAAE;MAAEL,OAAO,EAAE;IAAE,CAAC;IAC7BC,OAAO,EAAE;MACPI,CAAC,EAAE,CAAC;MACJL,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEI,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE;MACPC,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,qBAAqB;MAClCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACJL,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE,kBAAkB;MAC/BC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,SAAS,EAAE;MACTN,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,WAAW;MAClBC,WAAW,EAAE,wBAAwB;MACrCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDG,QAAQ,EAAE;MACRP,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDI,QAAQ,EAAE;MACRR,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,YAAY;MACnBC,WAAW,EAAE,mBAAmB;MAChCC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR;EACF,CAAC;EAED,oBACEvC,OAAA;IAAK4C,SAAS,EAAC,8EAA8E;IAAAC,QAAA,gBAG3F7C,OAAA;MAAK4C,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvC7C,OAAA;QAAK4C,SAAS,EAAE,oDAAoD/B,iBAAiB,GAC/E,4CAA4C,GAC5C,wCAAwC,EACzC;QAAAgC,QAAA,gBACH7C,OAAA;UAAK4C,SAAS,EAAE,wBAAwB/B,iBAAiB,GAAG,cAAc,GAAG,YAAY;QACpF;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACRjD,OAAA;UAAM4C,SAAS,EAAE,WAAW/B,iBAAiB,GAAG,gBAAgB,GAAG,cAAc,EAC5E;UAAAgC,QAAA,EACFhC,iBAAiB,GAAG,mBAAmB,GAAG;QAAsB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,EACN,CAACpC,iBAAiB,iBACjBb,OAAA;UACEkD,OAAO,EAAE/B,eAAgB;UACzByB,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EACnE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnC,KAAK,iBACJd,OAAA;MAAK4C,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjD7C,OAAA;QAAK4C,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpE7C,OAAA;UAAK4C,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC7C,OAAA;YAAK4C,SAAS,EAAC,2CAA2C;YAACO,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAP,QAAA,eAChG7C,OAAA;cAAMqD,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,yNAAyN;cAACC,QAAQ,EAAC;YAAS;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvQ,CAAC,eACNjD,OAAA;YAAA6C,QAAA,eACE7C,OAAA;cAAG4C,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAE/B;YAAK;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDjD,OAAA;MAAK4C,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnE7C,OAAA,CAACH,iBAAiB;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGNjD,OAAA;MAAK4C,SAAS,EAAC,qDAAqD;MAAAC,QAAA,gBAClE7C,OAAA,CAACL,MAAM,CAAC6D,GAAG;QACTZ,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3D7C,OAAA,CAACJ,eAAe;UAAC6D,IAAI,EAAC,MAAM;UAAAZ,QAAA,eAC1B7C,OAAA,CAACL,MAAM,CAAC6D,GAAG;YAETE,OAAO,EAAE;cAAEhC,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAK,CAAE;YACrCC,OAAO,EAAE;cAAElC,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAE,CAAE;YAClCE,IAAI,EAAE;cAAEnC,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAK,CAAE;YAClC/B,UAAU,EAAE;cACVI,QAAQ,EAAE,GAAG;cACb8B,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cACtBpC,OAAO,EAAE;gBAAEM,QAAQ,EAAE;cAAI;YAC3B,CAAE;YACFY,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EAEpGZ,WAAW,CAACtB,KAAK,CAAC,CAACwB,KAAK,CAAC4B,QAAQ,CAAC,MAAM,CAAC,gBACxC/D,OAAA;cACEgE,GAAG,EAAE,WAAW/B,WAAW,CAACtB,KAAK,CAAC,CAACwB,KAAK,EAAG;cAC3C8B,QAAQ;cACRC,KAAK;cACL3B,IAAI;cACJ4B,WAAW;cACXvB,SAAS,EAAC,yCAAyC;cACnDwB,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAyB;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,gBAEFjD,OAAA;cACEgE,GAAG,EAAE,WAAW/B,WAAW,CAACtB,KAAK,CAAC,CAACwB,KAAK,EAAG;cAC3CmC,GAAG,EAAE3D,KAAM;cACXiC,SAAS,EAAE,2CAA2CjC,KAAK,KAAK,UAAU,GAAG,uCAAuC,GAAG,EAAE,EACpH;cACLyD,KAAK,EAAE;gBACLC,QAAQ,EAAE,wBAAwB;gBAClCE,cAAc,EAAE,MAAM;gBACtBC,MAAM,EAAE,gCAAgC;gBACxC5C,UAAU,EAAE;cACd;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACF,GAlCItC,KAAK;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmCA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGlBjD,OAAA,CAACL,MAAM,CAAC6D,GAAG;UACTZ,SAAS,EAAC,4DAA4D;UACtEwB,KAAK,EAAE;YAAEK,WAAW,EAAExC,WAAW,CAACtB,KAAK,CAAC,CAAC2B;UAAM,CAAE;UACjDsB,OAAO,EAAE;YACPlC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YACxBiC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YAClBe,SAAS,EAAE,YAAYzC,WAAW,CAACtB,KAAK,CAAC,CAAC2B,KAAK;UACjD,CAAE;UACFV,UAAU,EAAE;YACVI,QAAQ,EAAE,CAAC;YACX2C,MAAM,EAAEC;UACV;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGbjD,OAAA,CAACL,MAAM,CAAC6D,GAAG;QACTZ,SAAS,EAAC,uBAAuB;QACjCiC,QAAQ,EAAErD,iBAAkB;QAC5BkC,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAf,QAAA,gBAEjB7C,OAAA,CAACL,MAAM,CAACmF,EAAE;UACRlC,SAAS,EAAC,qCAAqC;UAC/CwB,KAAK,EAAE;YACL9B,KAAK,EAAEL,WAAW,CAACtB,KAAK,CAAC,CAAC2B;UAC5B,CAAE;UACFuC,QAAQ,EAAE/C,YAAa;UAAAe,QAAA,EAEtBZ,WAAW,CAACtB,KAAK,CAAC,CAACyB;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEZjD,OAAA,CAACL,MAAM,CAACoF,CAAC;UACPnC,SAAS,EAAC,kCAAkC;UAC5CiC,QAAQ,EAAE/C,YAAa;UAAAe,QAAA,EAEtBZ,WAAW,CAACtB,KAAK,CAAC,CAAC0B;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGbjD,OAAA,CAACL,MAAM,CAAC6D,GAAG;QACTZ,SAAS,EAAC,OAAO;QACjBiC,QAAQ,EAAE/C,YAAa;QACvB4B,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAf,QAAA,eAEjB7C,OAAA;UAAO4C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAC1B7C,OAAA;YACEgF,IAAI,EAAC,UAAU;YACf3D,OAAO,EAAEhB,SAAU;YACnB4E,QAAQ,EAAGC,CAAC,IAAK9D,eAAe,CAAC8D,CAAC,CAACC,MAAM,CAAC9D,OAAO;UAAE;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFjD,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExB7C,OAAA;cAAK4C,SAAS,EAAC,SAAS;cAACQ,OAAO,EAAC,WAAW;cAACD,IAAI,EAAC,SAAS;cAACiB,KAAK,EAAE;gBAAE9B,KAAK,EAAE;cAAU,CAAE;cAAAO,QAAA,gBACtF7C,OAAA;gBAAMsD,CAAC,EAAC;cAA8E;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzFjD,OAAA;gBAAMsD,CAAC,EAAC;cAAsG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEjHjD,OAAA;gBACEoF,EAAE,EAAC,GAAG;gBAACC,EAAE,EAAC,GAAG;gBACbC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBACfC,MAAM,EAAC,SAAS;gBAChBC,WAAW,EAAC,KAAK;gBACjBC,aAAa,EAAC;cAAO;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjD,OAAA;cAAM4C,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGxCjD,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAACQ,OAAO,EAAC,WAAW;cAACD,IAAI,EAAC,cAAc;cAAAN,QAAA,gBAC/D7C,OAAA;gBAAMsD,CAAC,EAAC;cAA8E;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzFjD,OAAA;gBAAMsD,CAAC,EAAC;cAAsG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEjHjD,OAAA;gBAAQ2F,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAACnE,OAAO,EAAC,KAAK;gBAAAmB,QAAA,gBACxC7C,OAAA;kBAAS8F,aAAa,EAAC,GAAG;kBAACC,MAAM,EAAC,OAAO;kBAACC,GAAG,EAAC,IAAI;kBAACC,WAAW,EAAC;gBAAY;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9EjD,OAAA;kBAAS8F,aAAa,EAAC,SAAS;kBAACC,MAAM,EAAC,aAAa;kBAACC,GAAG,EAAC,IAAI;kBAACC,WAAW,EAAC;gBAAY;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACTjD,OAAA;gBAAQ2F,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,GAAG;gBAACC,CAAC,EAAC,GAAG;gBAACnE,OAAO,EAAC,KAAK;gBAAAmB,QAAA,gBACxC7C,OAAA;kBAAS8F,aAAa,EAAC,GAAG;kBAACC,MAAM,EAAC,OAAO;kBAACC,GAAG,EAAC,MAAM;kBAACC,WAAW,EAAC;gBAAY;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChFjD,OAAA;kBAAS8F,aAAa,EAAC,SAAS;kBAACC,MAAM,EAAC,aAAa;kBAACC,GAAG,EAAC,MAAM;kBAACC,WAAW,EAAC;gBAAY;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNjD,OAAA;cAAM4C,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGbjD,OAAA,CAACL,MAAM,CAAC6D,GAAG;QACTZ,SAAS,EAAC,qBAAqB;QAC/BiC,QAAQ,EAAE/C,YAAa;QACvB4B,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAf,QAAA,eAEjB7C,OAAA;UAAK4C,SAAS,EAAE,gBAAgBnC,cAAc,GAAG,SAAS,GAAG,EAAE,EAAG;UAAAoC,QAAA,gBAChE7C,OAAA;YACEgF,IAAI,EAAC,MAAM;YACXpC,SAAS,EAAC,OAAO;YACjBsD,WAAW,EAAC,kDAAkD;YAC9DC,KAAK,EAAE5F,SAAU;YACjB0E,QAAQ,EAAGC,CAAC,IAAK1E,YAAY,CAAC0E,CAAC,CAACC,MAAM,CAACgB,KAAK,CAAE;YAC9CC,OAAO,EAAEA,CAAA,KAAM1F,iBAAiB,CAAC,IAAI,CAAE;YACvC2F,MAAM,EAAEA,CAAA,KAAM3F,iBAAiB,CAAC,KAAK,CAAE;YACvC4F,SAAS,EAAE,MAAOpB,CAAC,IAAK;cACtB,IAAIA,CAAC,CAACqB,GAAG,KAAK,OAAO,IAAIhG,SAAS,CAACiG,IAAI,CAAC,CAAC,EAAE;gBACzC;gBACA,MAAMC,OAAO,GAAGlG,SAAS,CAACiG,IAAI,CAAC,CAAC;gBAChChG,YAAY,CAAC,EAAE,CAAC;gBAEhB,IAAI;kBACF,MAAMc,QAAQ,GAAG,MAAMN,WAAW,CAACyF,OAAO,CAAC;kBAC3CtG,WAAW,CAACsG,OAAO,CAAC;gBACtB,CAAC,CAAC,OAAO3F,KAAK,EAAE;kBACdS,OAAO,CAACT,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;kBAC/C;kBACAX,WAAW,CAACsG,OAAO,CAAC;gBACtB;cACF;YACF;UAAE;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFjD,OAAA;YAAK4C,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNjD,OAAA;MAAK4C,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvC7C,OAAA,CAACL,MAAM,CAAC6D,GAAG;QACTZ,SAAS,EAAC,MAAM;QAChBiC,QAAQ,EAAErD,iBAAkB;QAC5BkC,OAAO,EAAC,QAAQ;QAChBE,OAAO,EAAC,SAAS;QAAAf,QAAA,GAEhB6D,MAAM,CAACC,IAAI,CAAC1E,WAAW,CAAC,CAAC2E,GAAG,CAAEC,CAAC,iBAC9B7G,OAAA,CAACL,MAAM,CAACmH,MAAM;UAEZlE,SAAS,EAAE,QAAQjC,KAAK,KAAKkG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjDzC,KAAK,EAAE;YACL2C,eAAe,EAAEpG,KAAK,KAAKkG,CAAC,GAAG5E,WAAW,CAAC4E,CAAC,CAAC,CAACvE,KAAK,GAAG,IAAI,GAAG,aAAa;YAC1E0E,MAAM,EAAErG,KAAK,KAAKkG,CAAC,GAAG,aAAa5E,WAAW,CAAC4E,CAAC,CAAC,CAACvE,KAAK,IAAI,GAAG;UAChE,CAAE;UACF2E,UAAU,EAAE;YACVtD,KAAK,EAAE,IAAI;YACXe,SAAS,EAAE,YAAYzC,WAAW,CAAC4E,CAAC,CAAC,CAACvE,KAAK;UAC7C,CAAE;UACF4E,QAAQ,EAAE;YAAEvD,KAAK,EAAE;UAAK,CAAE;UAC1BT,OAAO,EAAEA,CAAA,KAAMnC,WAAW,CAAC8F,CAAC,CAAE;UAC9BhC,QAAQ,EAAE/C,YAAa;UACvBM,KAAK,EAAEyE,CAAC,CAACM,WAAW,CAAC,CAAE;UAAAtE,QAAA,gBAEvB7C,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB7C,OAAA;cACE4C,SAAS,EAAC,yEAAyE;cACnFwB,KAAK,EAAE;gBACL2C,eAAe,EAAEpG,KAAK,KAAKkG,CAAC,GAAG5E,WAAW,CAAC4E,CAAC,CAAC,CAACvE,KAAK,GAAG,wBAAwB;gBAC9EA,KAAK,EAAE,OAAO;gBACd8E,QAAQ,EAAE;cACZ,CAAE;cAAAvE,QAAA,EAEDgE,CAAC,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC;YAAC;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjD,OAAA;YAAM4C,SAAS,EAAC,YAAY;YAAAC,QAAA,EACzBgE,CAAC,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC,GAAGN,CAAC,CAACS,KAAK,CAAC,CAAC;UAAC;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA,GA7BF4D,CAAC;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BO,CAChB,CAAC,EAGD/C,YAAY,iBACXF,OAAA,CAACL,MAAM,CAACmH,MAAM;UACZlE,SAAS,EAAC,MAAM;UAChBwB,KAAK,EAAE;YACL2C,eAAe,EAAE,aAAa;YAC9BC,MAAM,EAAE;UACV,CAAE;UACFC,UAAU,EAAE;YACVtD,KAAK,EAAE,IAAI;YACXe,SAAS,EAAE;UACb,CAAE;UACFwC,QAAQ,EAAE;YAAEvD,KAAK,EAAE;UAAK,CAAE;UAC1BT,OAAO,EAAEhD,YAAa;UACtB2E,QAAQ,EAAE/C,YAAa;UACvBM,KAAK,EAAC,MAAM;UAAAS,QAAA,gBAEZ7C,OAAA;YAAK4C,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxB7C,OAAA;cACE4C,SAAS,EAAC,yEAAyE;cACnFwB,KAAK,EAAE;gBACL2C,eAAe,EAAE,0BAA0B;gBAC3CzE,KAAK,EAAE,OAAO;gBACd8E,QAAQ,EAAE;cACZ,CAAE;cAAAvE,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjD,OAAA;YAAM4C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAChB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAIH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CArZIH,MAAM;EAAA,QAiBNH,SAAS;AAAA;AAAAyH,EAAA,GAjBTtH,MAAM;AAuZZ,eAAeA,MAAM;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}