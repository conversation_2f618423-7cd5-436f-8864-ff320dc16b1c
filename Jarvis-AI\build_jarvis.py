#!/usr/bin/env python3
"""
Jarvis AI Build Script
Creates a standalone executable package for Jarvis AI
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path

# Get the directory where this script is located
SCRIPT_DIR = Path(__file__).parent.absolute()
BACKEND_DIR = SCRIPT_DIR / "Backend"
UI_DIR = SCRIPT_DIR / "ui"
DIST_DIR = SCRIPT_DIR / "dist"

class JarvisBuilder:
    def __init__(self):
        self.success = True

    def log(self, message, level="INFO"):
        """Log messages with formatting"""
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "ERROR": "❌",
            "WARNING": "⚠️"
        }
        print(f"{prefix.get(level, 'ℹ️')} {message}")

    def check_prerequisites(self):
        """Check if all required tools are installed"""
        self.log("Checking prerequisites...")
        
        # Check Python
        try:
            result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
            self.log(f"Python: {result.stdout.strip()}", "SUCCESS")
        except Exception as e:
            self.log(f"Python not found: {e}", "ERROR")
            return False

        # Check Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            self.log(f"Node.js: {result.stdout.strip()}", "SUCCESS")
        except Exception as e:
            self.log(f"Node.js not found: {e}", "ERROR")
            return False

        # Check npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            self.log(f"npm: {result.stdout.strip()}", "SUCCESS")
        except Exception as e:
            self.log(f"npm not found: {e}", "ERROR")
            return False

        return True

    def install_dependencies(self):
        """Install all required dependencies"""
        self.log("Installing dependencies...")
        
        # Install Python dependencies
        try:
            self.log("Installing Python dependencies...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", 
                str(BACKEND_DIR / "requirements.txt")
            ], check=True, cwd=BACKEND_DIR)
            self.log("Python dependencies installed", "SUCCESS")
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to install Python dependencies: {e}", "ERROR")
            return False

        # Install Node.js dependencies
        try:
            self.log("Installing Node.js dependencies...")
            subprocess.run(["npm", "install"], check=True, cwd=UI_DIR)
            self.log("Node.js dependencies installed", "SUCCESS")
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to install Node.js dependencies: {e}", "ERROR")
            return False

        # Install electron-builder if not present
        try:
            self.log("Installing electron-builder...")
            subprocess.run(["npm", "install", "--save-dev", "electron-builder"], 
                         check=True, cwd=UI_DIR)
            self.log("electron-builder installed", "SUCCESS")
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to install electron-builder: {e}", "WARNING")

        return True

    def build_react_app(self):
        """Build the React application"""
        self.log("Building React application...")
        
        try:
            subprocess.run(["npm", "run", "build"], check=True, cwd=UI_DIR)
            self.log("React application built successfully", "SUCCESS")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to build React application: {e}", "ERROR")
            return False

    def package_electron_app(self):
        """Package the Electron application"""
        self.log("Packaging Electron application...")
        
        try:
            # Run electron-builder
            subprocess.run(["npm", "run", "electron-dist"], check=True, cwd=UI_DIR)
            self.log("Electron application packaged successfully", "SUCCESS")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"Failed to package Electron application: {e}", "ERROR")
            return False

    def create_portable_package(self):
        """Create a portable package with all dependencies"""
        self.log("Creating portable package...")
        
        try:
            # Create dist directory
            if DIST_DIR.exists():
                shutil.rmtree(DIST_DIR)
            DIST_DIR.mkdir()

            # Copy backend files
            backend_dist = DIST_DIR / "Backend"
            shutil.copytree(BACKEND_DIR, backend_dist, 
                          ignore=shutil.ignore_patterns('__pycache__', '*.pyc', '.git'))

            # Copy startup scripts
            shutil.copy2(SCRIPT_DIR / "start_jarvis.py", DIST_DIR)
            shutil.copy2(SCRIPT_DIR / "start_jarvis.bat", DIST_DIR)
            shutil.copy2(SCRIPT_DIR / "README.md", DIST_DIR)

            # Copy UI build if electron packaging failed
            ui_build = UI_DIR / "build"
            if ui_build.exists():
                shutil.copytree(ui_build, DIST_DIR / "ui" / "build")

            self.log("Portable package created successfully", "SUCCESS")
            return True
        except Exception as e:
            self.log(f"Failed to create portable package: {e}", "ERROR")
            return False

    def create_installer_info(self):
        """Create installation and usage information"""
        info_content = """
# Jarvis AI - Installation Package

## Quick Start
1. Double-click `start_jarvis.bat` (Windows) or run `python start_jarvis.py`
2. Wait for both backend and frontend to start
3. Your browser will open automatically to the Jarvis interface

## Manual Installation
1. Install Python 3.8+ and Node.js 16+
2. Run: `pip install -r Backend/requirements.txt`
3. Run: `cd ui && npm install`
4. Start backend: `cd Backend && python api_server.py`
5. Start frontend: `cd ui && npm start`

## Troubleshooting
- Ensure ports 3000 and 5000 are available
- Check firewall settings if connection fails
- Install Visual C++ Redistributable if needed on Windows

## Support
For issues and updates, visit: https://github.com/your-repo/jarvis-ai
"""
        
        try:
            with open(DIST_DIR / "INSTALLATION.md", "w") as f:
                f.write(info_content)
            self.log("Installation info created", "SUCCESS")
        except Exception as e:
            self.log(f"Failed to create installation info: {e}", "WARNING")

    def build(self):
        """Main build process"""
        self.log("Starting Jarvis AI build process...")
        self.log("=" * 50)

        # Check prerequisites
        if not self.check_prerequisites():
            self.log("Prerequisites check failed", "ERROR")
            return False

        # Install dependencies
        if not self.install_dependencies():
            self.log("Dependency installation failed", "ERROR")
            return False

        # Build React app
        if not self.build_react_app():
            self.log("React build failed", "ERROR")
            return False

        # Try to package with Electron
        electron_success = self.package_electron_app()
        if not electron_success:
            self.log("Electron packaging failed, creating portable package instead", "WARNING")

        # Create portable package
        if not self.create_portable_package():
            self.log("Portable package creation failed", "ERROR")
            return False

        # Create installation info
        self.create_installer_info()

        self.log("=" * 50)
        if electron_success:
            self.log("Jarvis AI build completed successfully!", "SUCCESS")
            self.log(f"Executable package: {UI_DIR / 'dist'}", "INFO")
        else:
            self.log("Jarvis AI portable package created!", "SUCCESS")
        
        self.log(f"Portable package: {DIST_DIR}", "INFO")
        self.log("Run start_jarvis.bat or start_jarvis.py to launch", "INFO")
        
        return True

if __name__ == "__main__":
    builder = JarvisBuilder()
    success = builder.build()
    sys.exit(0 if success else 1)
