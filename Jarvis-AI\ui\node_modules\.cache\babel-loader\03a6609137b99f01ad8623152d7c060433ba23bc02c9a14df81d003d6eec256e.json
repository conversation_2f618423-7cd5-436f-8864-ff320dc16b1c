{"ast": null, "code": "import { motionComponentSymbol } from './symbol.mjs';\n\n/**\n * Checks if a component is a `motion` component.\n */\nfunction isMotionComponent(component) {\n  return component !== null && typeof component === \"object\" && motionComponentSymbol in component;\n}\nexport { isMotionComponent };", "map": {"version": 3, "names": ["motionComponentSymbol", "isMotionComponent", "component"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/motion/utils/is-motion-component.mjs"], "sourcesContent": ["import { motionComponentSymbol } from './symbol.mjs';\n\n/**\n * Checks if a component is a `motion` component.\n */\nfunction isMotionComponent(component) {\n    return (component !== null &&\n        typeof component === \"object\" &&\n        motionComponentSymbol in component);\n}\n\nexport { isMotionComponent };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,cAAc;;AAEpD;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EAClC,OAAQA,SAAS,KAAK,IAAI,IACtB,OAAOA,SAAS,KAAK,QAAQ,IAC7BF,qBAAqB,IAAIE,SAAS;AAC1C;AAEA,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}