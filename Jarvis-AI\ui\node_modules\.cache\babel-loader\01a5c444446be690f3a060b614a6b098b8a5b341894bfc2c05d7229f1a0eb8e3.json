{"ast": null, "code": "import { resizeElement } from './handle-element.mjs';\nimport { resizeWindow } from './handle-window.mjs';\nfunction resize(a, b) {\n  return typeof a === \"function\" ? resizeWindow(a) : resizeElement(a, b);\n}\nexport { resize };", "map": {"version": 3, "names": ["resizeElement", "resizeWindow", "resize", "a", "b"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/dom/resize/index.mjs"], "sourcesContent": ["import { resizeElement } from './handle-element.mjs';\nimport { resizeWindow } from './handle-window.mjs';\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? resizeWindow(a) : resizeElement(a, b);\n}\n\nexport { resize };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,sBAAsB;AACpD,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClB,OAAO,OAAOD,CAAC,KAAK,UAAU,GAAGF,YAAY,CAACE,CAAC,CAAC,GAAGH,aAAa,CAACG,CAAC,EAAEC,CAAC,CAAC;AAC1E;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}