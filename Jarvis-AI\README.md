# Jarvis AI - Integrated Desktop Application

A sophisticated AI assistant with a modern React frontend and Python backend, featuring speech recognition, text-to-speech, chatbot functionality, and real-time search capabilities.

## 🚀 Features

- **Modern React UI**: Beautiful, animated interface with Framer Motion
- **Speech Recognition**: Real-time voice input processing
- **Text-to-Speech**: Natural voice responses
- **AI Chatbot**: Intelligent conversation using Groq API
- **Real-time Search**: Web search integration
- **Desktop Application**: Electron-based for native desktop experience
- **State Management**: Real-time synchronization between frontend and backend

## 📋 Prerequisites

Before running Jarvis AI, ensure you have the following installed:

- **Python 3.8+**: [Download Python](https://www.python.org/downloads/)
- **Node.js 16+**: [Download Node.js](https://nodejs.org/)
- **npm**: Comes with Node.js

## 🛠️ Installation

### Option 1: Quick Start (Recommended)

1. **Clone or download** this repository
2. **Double-click** `start_jarvis.bat` (Windows) or run `python start_jarvis.py`
3. The script will automatically:
   - Check dependencies
   - Install missing packages
   - Start both backend and frontend
   - Open your browser

### Option 2: Manual Setup

1. **Install Python dependencies**:
   ```bash
   cd Backend
   pip install -r requirements.txt
   ```

2. **Install React dependencies**:
   ```bash
   cd ui
   npm install
   ```

3. **Start the backend API server**:
   ```bash
   cd Backend
   python api_server.py
   ```

4. **Start the React frontend** (in a new terminal):
   ```bash
   cd ui
   npm start
   ```

5. **Open your browser** to `http://localhost:3000`

## 🎯 Usage

### Starting Jarvis

1. **Launch**: Run `start_jarvis.bat` or `python start_jarvis.py`
2. **Wait**: The system will start both backend and frontend
3. **Access**: Your browser will automatically open to the Jarvis interface

### Using the Interface

1. **Home Screen**: Click "Enter Jarvis" to access the main interface
2. **Voice Input**: Toggle the microphone to speak to Jarvis
3. **Text Input**: Type messages and press Enter
4. **Chat Mode**: Extended conversations with full history
5. **State Control**: Manual state switching (startup, rest, listening, thinking, speaking)

### API Endpoints

The backend provides the following REST API endpoints:

- `GET /api/health` - Health check
- `GET /api/state` - Get current Jarvis state
- `POST /api/state` - Set Jarvis state
- `POST /api/chat` - Send chat message
- `POST /api/speech-to-text` - Start speech recognition
- `POST /api/text-to-speech` - Convert text to speech
- `GET /api/conversation-history` - Get chat history
- `DELETE /api/conversation-history` - Clear chat history

## 🏗️ Architecture

### Frontend (React)
- **Location**: `ui/`
- **Framework**: React 18 with Framer Motion
- **Styling**: Tailwind CSS
- **Desktop**: Electron integration
- **State Management**: Custom hooks with API integration

### Backend (Python)
- **Location**: `Backend/`
- **Framework**: Flask with CORS support
- **AI Services**: Groq API, OpenAI integration
- **Speech**: SpeechRecognition, pyttsx3
- **Search**: Real-time web search engine

### Communication
- **Protocol**: HTTP REST API
- **Port**: Backend runs on `localhost:5000`
- **Frontend**: Runs on `localhost:3000`
- **Real-time**: Polling-based state synchronization

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the `Backend/` directory:

```env
GROQ_API_KEY=your_groq_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
```

### Backend Configuration

Edit `Backend/api_server.py` to modify:
- Server port (default: 5000)
- CORS settings
- Logging level
- State polling interval

### Frontend Configuration

Edit `ui/src/services/jarvisApi.js` to modify:
- API base URL
- Request timeouts
- Polling intervals

## 🚨 Troubleshooting

### Common Issues

1. **Backend won't start**:
   - Check Python version: `python --version`
   - Install dependencies: `pip install -r Backend/requirements.txt`
   - Check port 5000 availability

2. **Frontend won't start**:
   - Check Node.js version: `node --version`
   - Install dependencies: `cd ui && npm install`
   - Check port 3000 availability

3. **Connection issues**:
   - Ensure backend is running on port 5000
   - Check firewall settings
   - Verify CORS configuration

4. **Speech recognition not working**:
   - Check microphone permissions
   - Install audio dependencies
   - Verify PyAudio installation

### Logs and Debugging

- **Backend logs**: Console output from `api_server.py`
- **Frontend logs**: Browser developer console
- **Network issues**: Check browser Network tab

## 📁 Project Structure

```
Jarvis-AI/
├── Backend/                 # Python backend
│   ├── api_server.py       # Flask API server
│   ├── Main.py             # Core backend logic
│   ├── SpeechToText.py     # Speech recognition
│   ├── TextToSpeech.py     # Text-to-speech
│   ├── Chatbot.py          # AI chatbot
│   ├── RealtimeSearchEngine.py # Web search
│   └── requirements.txt    # Python dependencies
├── ui/                     # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── hooks/          # Custom hooks
│   │   └── services/       # API services
│   ├── public/             # Static assets
│   └── package.json        # Node.js dependencies
├── start_jarvis.py         # Main launcher script
├── start_jarvis.bat        # Windows batch file
└── README.md               # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- React and Framer Motion for the beautiful UI
- Flask for the robust backend framework
- Groq API for AI capabilities
- All open-source contributors
