/**
 * Jarvis API Service
 * Handles communication between React frontend and Python backend
 */

const API_BASE_URL = 'http://localhost:5000/api';

class JarvisApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  /**
   * Make HTTP request to API
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const requestOptions = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, requestOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    return this.makeRequest('/health');
  }

  /**
   * Get current Jarvis state
   */
  async getState() {
    return this.makeRequest('/state');
  }

  /**
   * Set Jarvis state
   */
  async setState(state) {
    return this.makeRequest('/state', {
      method: 'POST',
      body: JSON.stringify({ state }),
    });
  }

  /**
   * Send chat message and get response
   */
  async sendMessage(message) {
    return this.makeRequest('/chat', {
      method: 'POST',
      body: JSON.stringify({ message }),
    });
  }

  /**
   * Start speech recognition
   */
  async startSpeechRecognition() {
    return this.makeRequest('/speech-to-text', {
      method: 'POST',
    });
  }

  /**
   * Convert text to speech
   */
  async textToSpeech(text) {
    return this.makeRequest('/text-to-speech', {
      method: 'POST',
      body: JSON.stringify({ text }),
    });
  }

  /**
   * Get conversation history
   */
  async getConversationHistory() {
    return this.makeRequest('/conversation-history');
  }

  /**
   * Clear conversation history
   */
  async clearConversationHistory() {
    return this.makeRequest('/conversation-history', {
      method: 'DELETE',
    });
  }

  /**
   * Check if API server is running
   */
  async isServerRunning() {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Poll state changes (for real-time updates)
   */
  async pollState(callback, interval = 1000) {
    const poll = async () => {
      try {
        const state = await this.getState();
        callback(state);
      } catch (error) {
        console.error('State polling error:', error);
      }
    };

    // Initial poll
    await poll();

    // Set up interval polling
    return setInterval(poll, interval);
  }

  /**
   * Stop polling
   */
  stopPolling(intervalId) {
    if (intervalId) {
      clearInterval(intervalId);
    }
  }
}

// Create singleton instance
const jarvisApi = new JarvisApiService();

export default jarvisApi;

// Export individual methods for convenience
export const {
  healthCheck,
  getState,
  setState,
  sendMessage,
  startSpeechRecognition,
  textToSpeech,
  getConversationHistory,
  clearConversationHistory,
  isServerRunning,
  pollState,
  stopPolling,
} = jarvisApi;
