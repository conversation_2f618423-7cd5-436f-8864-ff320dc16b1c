{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"container\", \"target\", \"layoutEffect\"];\nimport { motionValue } from './index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useEffect } from 'react';\nimport { warning } from '../utils/errors.mjs';\nimport { scrollInfo } from '../render/dom/scroll/track.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction refWarning(name, ref) {\n  warning(Boolean(!ref || ref.current), \"You have defined a \".concat(name, \" options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its `layoutEffect: false` option.\"));\n}\nconst createScrollMotionValues = () => ({\n  scrollX: motionValue(0),\n  scrollY: motionValue(0),\n  scrollXProgress: motionValue(0),\n  scrollYProgress: motionValue(0)\n});\nfunction useScroll() {\n  let _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n    {\n      container,\n      target,\n      layoutEffect = true\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded);\n  const values = useConstant(createScrollMotionValues);\n  const useLifecycleEffect = layoutEffect ? useIsomorphicLayoutEffect : useEffect;\n  useLifecycleEffect(() => {\n    refWarning(\"target\", target);\n    refWarning(\"container\", container);\n    return scrollInfo(_ref2 => {\n      let {\n        x,\n        y\n      } = _ref2;\n      values.scrollX.set(x.current);\n      values.scrollXProgress.set(x.progress);\n      values.scrollY.set(y.current);\n      values.scrollYProgress.set(y.progress);\n    }, _objectSpread(_objectSpread({}, options), {}, {\n      container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n      target: (target === null || target === void 0 ? void 0 : target.current) || undefined\n    }));\n  }, [container, target, JSON.stringify(options.offset)]);\n  return values;\n}\nexport { useScroll };", "map": {"version": 3, "names": ["motionValue", "useConstant", "useEffect", "warning", "scrollInfo", "useIsomorphicLayoutEffect", "refWarning", "name", "ref", "Boolean", "current", "concat", "createScrollMotionValues", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "useScroll", "_ref", "arguments", "length", "undefined", "container", "target", "layoutEffect", "options", "_objectWithoutProperties", "_excluded", "values", "useLifecycleEffect", "_ref2", "x", "y", "set", "progress", "_objectSpread", "JSON", "stringify", "offset"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/value/use-scroll.mjs"], "sourcesContent": ["import { motionValue } from './index.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { useEffect } from 'react';\nimport { warning } from '../utils/errors.mjs';\nimport { scrollInfo } from '../render/dom/scroll/track.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\n\nfunction refWarning(name, ref) {\n    warning(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: motionValue(0),\n    scrollY: motionValue(0),\n    scrollXProgress: motionValue(0),\n    scrollYProgress: motionValue(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = useConstant(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? useIsomorphicLayoutEffect\n        : useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return scrollInfo(({ x, y }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n            target: (target === null || target === void 0 ? void 0 : target.current) || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\nexport { useScroll };\n"], "mappings": ";;;AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,UAAUA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3BL,OAAO,CAACM,OAAO,CAAC,CAACD,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,wBAAAC,MAAA,CAAwBJ,IAAI,gNAA+M,CAAC;AACpR;AACA,MAAMK,wBAAwB,GAAGA,CAAA,MAAO;EACpCC,OAAO,EAAEb,WAAW,CAAC,CAAC,CAAC;EACvBc,OAAO,EAAEd,WAAW,CAAC,CAAC,CAAC;EACvBe,eAAe,EAAEf,WAAW,CAAC,CAAC,CAAC;EAC/BgB,eAAe,EAAEhB,WAAW,CAAC,CAAC;AAClC,CAAC,CAAC;AACF,SAASiB,SAASA,CAAA,EAA8D;EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;IAA3D;MAAEG,SAAS;MAAEC,MAAM;MAAEC,YAAY,GAAG;IAAiB,CAAC,GAAAN,IAAA;IAATO,OAAO,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EACnE,MAAMC,MAAM,GAAG3B,WAAW,CAACW,wBAAwB,CAAC;EACpD,MAAMiB,kBAAkB,GAAGL,YAAY,GACjCnB,yBAAyB,GACzBH,SAAS;EACf2B,kBAAkB,CAAC,MAAM;IACrBvB,UAAU,CAAC,QAAQ,EAAEiB,MAAM,CAAC;IAC5BjB,UAAU,CAAC,WAAW,EAAEgB,SAAS,CAAC;IAClC,OAAOlB,UAAU,CAAC0B,KAAA,IAAc;MAAA,IAAb;QAAEC,CAAC;QAAEC;MAAE,CAAC,GAAAF,KAAA;MACvBF,MAAM,CAACf,OAAO,CAACoB,GAAG,CAACF,CAAC,CAACrB,OAAO,CAAC;MAC7BkB,MAAM,CAACb,eAAe,CAACkB,GAAG,CAACF,CAAC,CAACG,QAAQ,CAAC;MACtCN,MAAM,CAACd,OAAO,CAACmB,GAAG,CAACD,CAAC,CAACtB,OAAO,CAAC;MAC7BkB,MAAM,CAACZ,eAAe,CAACiB,GAAG,CAACD,CAAC,CAACE,QAAQ,CAAC;IAC1C,CAAC,EAAAC,aAAA,CAAAA,aAAA,KACMV,OAAO;MACVH,SAAS,EAAE,CAACA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACZ,OAAO,KAAKW,SAAS;MACjGE,MAAM,EAAE,CAACA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACb,OAAO,KAAKW;IAAS,EACxF,CAAC;EACN,CAAC,EAAE,CAACC,SAAS,EAAEC,MAAM,EAAEa,IAAI,CAACC,SAAS,CAACZ,OAAO,CAACa,MAAM,CAAC,CAAC,CAAC;EACvD,OAAOV,MAAM;AACjB;AAEA,SAASX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}