import React, { useState, useEffect, useRef } from 'react';


const CuteMenuIcon = ({ onMenuToggle }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [eyePosition, setEyePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const iconRef = useRef(null);

  // Track mouse position across entire screen for eye movement
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (iconRef.current) {
        const rect = iconRef.current.getBoundingClientRect();
        const iconCenterX = rect.left + rect.width / 2;
        const iconCenterY = rect.top + rect.height / 2;

        // Calculate eye movement based on mouse position relative to icon center
        const maxDistance = 6; // Maximum eye movement distance
        const distance = Math.min(
          Math.sqrt(
            Math.pow(e.clientX - iconCenterX, 2) +
            Math.pow(e.clientY - iconCenterY, 2)
          ) / 100,
          maxDistance
        );

        const angle = Math.atan2(e.clientY - iconCenterY, e.clientX - iconCenterX);
        const eyeX = Math.cos(angle) * Math.min(distance, maxDistance);
        const eyeY = Math.sin(angle) * Math.min(distance, maxDistance);

        setEyePosition({ x: eyeX, y: eyeY });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const toggleMenu = () => {
    const newMenuState = !isMenuOpen;
    setIsMenuOpen(newMenuState);
    if (onMenuToggle) {
      onMenuToggle(newMenuState);
    }
  };

  return (
    <div className="cute-menu-wrapper">
      <div
        ref={iconRef}
        className="cute-menu-icon-container"
        onClick={toggleMenu}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="cute-icon-background">
          <div className="cute-eyes">
            {/* Normal Eyes */}
            <div
              className={`cute-eye left-eye ${isHovered ? 'hidden' : ''}`}
              style={{
                transform: `translate(${eyePosition.x}px, ${eyePosition.y}px)`
              }}
            />
            <div
              className={`cute-eye right-eye ${isHovered ? 'hidden' : ''}`}
              style={{
                transform: `translate(${eyePosition.x}px, ${eyePosition.y}px)`
              }}
            />

            {/* Happy Eyes (on hover) */}
            <div className={`cute-happy-eyes ${isHovered ? 'visible' : ''}`}>
              <svg fill="none" viewBox="0 0 24 24" className="happy-eye">
                <path
                  fill="white"
                  d="M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z"
                />
              </svg>
              <svg fill="none" viewBox="0 0 24 24" className="happy-eye">
                <path
                  fill="white"
                  d="M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="click-me-label">Click Me!</div>

      {isMenuOpen && (
        <div className="cute-dropdown-menu">
          <div className="menu-option">
            <div className="menu-icon notifications-icon">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0" />
              </svg>
            </div>
            <span>Notifications</span>
          </div>

          <div className="menu-option">
            <div className="menu-icon settings-icon">
              <svg stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24" fill="none">
                <path d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" strokeLinejoin="round" strokeLinecap="round" />
                <path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" strokeLinejoin="round" strokeLinecap="round" />
              </svg>
            </div>
            <span>Settings</span>
          </div>

          <div className="menu-option">
            <div className="menu-icon chat-icon">
              <svg stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24" fill="none">
                <path d="M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" strokeLinejoin="round" strokeLinecap="round" />
              </svg>
            </div>
            <span>Chat</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CuteMenuIcon;
