#!/usr/bin/env python3
"""
Jarvis AI Test Suite
Comprehensive testing of all Jarvis AI components
"""

import os
import sys
import time
import requests
import subprocess
import threading
from pathlib import Path

# Get the directory where this script is located
SCRIPT_DIR = Path(__file__).parent.absolute()
BACKEND_DIR = SCRIPT_DIR / "Backend"
UI_DIR = SCRIPT_DIR / "ui"

class JarvisTestSuite:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.test_results = []

    def log(self, message, level="INFO"):
        """Log test messages"""
        prefix = {
            "INFO": "ℹ️",
            "PASS": "✅",
            "FAIL": "❌",
            "WARNING": "⚠️"
        }
        print(f"{prefix.get(level, 'ℹ️')} {message}")

    def test_dependencies(self):
        """Test if all dependencies are installed"""
        self.log("Testing dependencies...")
        
        tests = [
            ("Python", [sys.executable, "--version"]),
            ("Node.js", ["node", "--version"]),
            ("npm", ["npm", "--version"])
        ]
        
        for name, cmd in tests:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.log(f"{name}: {result.stdout.strip()}", "PASS")
                else:
                    self.log(f"{name}: Failed", "FAIL")
                    return False
            except Exception as e:
                self.log(f"{name}: Error - {e}", "FAIL")
                return False
        
        return True

    def test_python_imports(self):
        """Test Python backend imports"""
        self.log("Testing Python imports...")
        
        imports = [
            "flask",
            "flask_cors",
            "requests"
        ]
        
        for module in imports:
            try:
                __import__(module)
                self.log(f"Import {module}: OK", "PASS")
            except ImportError:
                self.log(f"Import {module}: Failed", "FAIL")
                return False
        
        return True

    def start_backend_for_testing(self):
        """Start backend process for testing"""
        self.log("Starting backend for testing...")
        
        try:
            self.backend_process = subprocess.Popen(
                [sys.executable, "api_server.py"],
                cwd=BACKEND_DIR,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for backend to start
            time.sleep(5)
            
            if self.backend_process.poll() is None:
                self.log("Backend started successfully", "PASS")
                return True
            else:
                self.log("Backend failed to start", "FAIL")
                return False
                
        except Exception as e:
            self.log(f"Failed to start backend: {e}", "FAIL")
            return False

    def test_api_endpoints(self):
        """Test all API endpoints"""
        self.log("Testing API endpoints...")
        
        base_url = "http://localhost:5000/api"
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=10)
            if response.status_code == 200:
                self.log("Health endpoint: OK", "PASS")
            else:
                self.log(f"Health endpoint: Failed ({response.status_code})", "FAIL")
                return False
        except Exception as e:
            self.log(f"Health endpoint: Error - {e}", "FAIL")
            return False

        # Test state endpoint
        try:
            response = requests.get(f"{base_url}/state", timeout=10)
            if response.status_code == 200:
                self.log("State endpoint: OK", "PASS")
            else:
                self.log(f"State endpoint: Failed ({response.status_code})", "FAIL")
                return False
        except Exception as e:
            self.log(f"State endpoint: Error - {e}", "FAIL")
            return False

        # Test chat endpoint
        try:
            response = requests.post(
                f"{base_url}/chat",
                json={"message": "Hello, this is a test"},
                timeout=30
            )
            if response.status_code == 200:
                data = response.json()
                if "response" in data:
                    self.log("Chat endpoint: OK", "PASS")
                else:
                    self.log("Chat endpoint: Invalid response format", "FAIL")
                    return False
            else:
                self.log(f"Chat endpoint: Failed ({response.status_code})", "FAIL")
                return False
        except Exception as e:
            self.log(f"Chat endpoint: Error - {e}", "FAIL")
            return False

        return True

    def test_frontend_build(self):
        """Test if frontend can be built"""
        self.log("Testing frontend build...")
        
        try:
            # Check if node_modules exists
            if not (UI_DIR / "node_modules").exists():
                self.log("Installing frontend dependencies...")
                subprocess.run(["npm", "install"], check=True, cwd=UI_DIR, timeout=300)
            
            # Test build
            result = subprocess.run(
                ["npm", "run", "build"],
                cwd=UI_DIR,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                self.log("Frontend build: OK", "PASS")
                return True
            else:
                self.log(f"Frontend build: Failed - {result.stderr}", "FAIL")
                return False
                
        except Exception as e:
            self.log(f"Frontend build: Error - {e}", "FAIL")
            return False

    def test_file_structure(self):
        """Test if all required files exist"""
        self.log("Testing file structure...")
        
        required_files = [
            BACKEND_DIR / "api_server.py",
            BACKEND_DIR / "Main.py",
            BACKEND_DIR / "requirements.txt",
            UI_DIR / "package.json",
            UI_DIR / "public" / "electron.js",
            UI_DIR / "src" / "App.js",
            UI_DIR / "src" / "services" / "jarvisApi.js",
            UI_DIR / "src" / "hooks" / "useJarvis.js",
            SCRIPT_DIR / "start_jarvis.py",
            SCRIPT_DIR / "build_jarvis.py"
        ]
        
        for file_path in required_files:
            if file_path.exists():
                self.log(f"File {file_path.name}: OK", "PASS")
            else:
                self.log(f"File {file_path}: Missing", "FAIL")
                return False
        
        return True

    def cleanup(self):
        """Clean up test processes"""
        if self.backend_process:
            self.log("Stopping backend process...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()

    def run_tests(self):
        """Run all tests"""
        self.log("Starting Jarvis AI Test Suite...")
        self.log("=" * 50)
        
        tests = [
            ("Dependencies", self.test_dependencies),
            ("Python Imports", self.test_python_imports),
            ("File Structure", self.test_file_structure),
            ("Frontend Build", self.test_frontend_build),
            ("Backend Startup", self.start_backend_for_testing),
            ("API Endpoints", self.test_api_endpoints)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            self.log(f"Running {test_name} test...")
            try:
                if test_func():
                    passed += 1
                    self.log(f"{test_name}: PASSED", "PASS")
                else:
                    self.log(f"{test_name}: FAILED", "FAIL")
            except Exception as e:
                self.log(f"{test_name}: ERROR - {e}", "FAIL")
            
            self.log("-" * 30)
        
        # Cleanup
        self.cleanup()
        
        # Results
        self.log("=" * 50)
        self.log(f"Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            self.log("All tests passed! Jarvis AI is ready to use.", "PASS")
            return True
        else:
            self.log(f"{total - passed} tests failed. Please check the issues above.", "FAIL")
            return False

if __name__ == "__main__":
    tester = JarvisTestSuite()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
