/**
 * use<PERSON><PERSON><PERSON> Hook
 * Custom React hook for managing Jarvis state and API interactions
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import jarvisApi from '../services/jarvisApi';

export const useJarvis = () => {
  // State management
  const [state, setState] = useState('startup');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isServerConnected, setIsServerConnected] = useState(false);
  const [lastResponse, setLastResponse] = useState('');
  const [conversationHistory, setConversationHistory] = useState([]);
  const [error, setError] = useState(null);

  // Refs for cleanup
  const pollingIntervalRef = useRef(null);
  const mountedRef = useRef(true);

  /**
   * Check server connection
   */
  const checkServerConnection = useCallback(async () => {
    try {
      const isRunning = await jarvisApi.isServerRunning();
      if (mountedRef.current) {
        setIsServerConnected(isRunning);
        if (isRunning) {
          setError(null);
        }
      }
      return isRunning;
    } catch (err) {
      if (mountedRef.current) {
        setIsServerConnected(false);
        setError('Failed to connect to Jarvis backend');
      }
      return false;
    }
  }, []);

  /**
   * Update Jarvis state
   */
  const updateState = useCallback(async (newState) => {
    try {
      await jarvisApi.setState(newState);
      if (mountedRef.current) {
        setState(newState);
        setError(null);
      }
    } catch (err) {
      console.error('Failed to update state:', err);
      if (mountedRef.current) {
        setError(`Failed to update state: ${err.message}`);
      }
    }
  }, []);

  /**
   * Send message to Jarvis
   */
  const sendMessage = useCallback(async (message) => {
    if (!isServerConnected) {
      throw new Error('Not connected to Jarvis backend');
    }

    try {
      setIsProcessing(true);
      setError(null);

      const response = await jarvisApi.sendMessage(message);

      if (mountedRef.current) {
        setLastResponse(response.response);
        setState(response.current_state || 'rest');

        // Update conversation history
        setConversationHistory(prev => [...prev, {
          id: Date.now(),
          user: message,
          jarvis: response.response,
          timestamp: response.timestamp
        }]);

        // Automatically speak the response
        if (response.response) {
          try {
            await jarvisApi.textToSpeech(response.response);
          } catch (speechError) {
            console.warn('Text-to-speech failed:', speechError);
            // Don't throw error for TTS failure, just log it
          }
        }
      }

      return response;
    } catch (err) {
      console.error('Failed to send message:', err);
      if (mountedRef.current) {
        setError(`Failed to send message: ${err.message}`);
      }
      throw err;
    } finally {
      if (mountedRef.current) {
        setIsProcessing(false);
      }
    }
  }, [isServerConnected]);

  /**
   * Start speech recognition
   */
  const startSpeechRecognition = useCallback(async () => {
    if (!isServerConnected) {
      throw new Error('Not connected to Jarvis backend');
    }

    try {
      setIsProcessing(true);
      setError(null);

      const response = await jarvisApi.startSpeechRecognition();

      if (mountedRef.current && response.text) {
        // Automatically send the recognized text as a message
        // The sendMessage function will automatically speak the response
        return await sendMessage(response.text);
      }

      return response;
    } catch (err) {
      console.error('Speech recognition failed:', err);
      if (mountedRef.current) {
        setError(`Speech recognition failed: ${err.message}`);
      }
      throw err;
    } finally {
      if (mountedRef.current) {
        setIsProcessing(false);
      }
    }
  }, [isServerConnected, sendMessage]);

  /**
   * Convert text to speech
   */
  const speakText = useCallback(async (text) => {
    if (!isServerConnected) {
      throw new Error('Not connected to Jarvis backend');
    }

    try {
      setError(null);
      const response = await jarvisApi.textToSpeech(text);
      return response;
    } catch (err) {
      console.error('Text-to-speech failed:', err);
      if (mountedRef.current) {
        setError(`Text-to-speech failed: ${err.message}`);
      }
      throw err;
    }
  }, [isServerConnected]);

  /**
   * Clear conversation history
   */
  const clearHistory = useCallback(async () => {
    try {
      await jarvisApi.clearConversationHistory();
      if (mountedRef.current) {
        setConversationHistory([]);
        setError(null);
      }
    } catch (err) {
      console.error('Failed to clear history:', err);
      if (mountedRef.current) {
        setError(`Failed to clear history: ${err.message}`);
      }
    }
  }, []);

  /**
   * Initialize connection and start polling
   */
  useEffect(() => {
    let mounted = true;
    mountedRef.current = true;

    const initialize = async () => {
      // Check initial connection
      await checkServerConnection();

      // Start polling for state updates
      if (mounted) {
        pollingIntervalRef.current = await jarvisApi.pollState(
          (stateData) => {
            if (mountedRef.current) {
              setState(stateData.current_state);
              setIsProcessing(stateData.is_processing);
              setLastResponse(stateData.last_response);
            }
          },
          2000 // Poll every 2 seconds
        );
      }
    };

    initialize();

    // Cleanup function
    return () => {
      mounted = false;
      mountedRef.current = false;
      if (pollingIntervalRef.current) {
        jarvisApi.stopPolling(pollingIntervalRef.current);
      }
    };
  }, [checkServerConnection]);

  /**
   * Retry connection
   */
  const retryConnection = useCallback(async () => {
    setError(null);
    return await checkServerConnection();
  }, [checkServerConnection]);

  return {
    // State
    state,
    isProcessing,
    isServerConnected,
    lastResponse,
    conversationHistory,
    error,

    // Actions
    updateState,
    sendMessage,
    startSpeechRecognition,
    speakText,
    clearHistory,
    retryConnection,
  };
};
