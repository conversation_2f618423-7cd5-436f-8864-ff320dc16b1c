{"ast": null, "code": "import React,{useState,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const MinimizedJarvis=_ref=>{var _stateConfig$state3;let{onExpand,isProcessing=false}=_ref;const[state,setState]=useState('rest');const[isVisible,setIsVisible]=useState(true);const[position,setPosition]=useState({x:0,y:0});const[isDragging,setIsDragging]=useState(false);const[showControls,setShowControls]=useState(false);// ========================================\n// JARVIS ACTIVITY MEDIA CONFIGURATION\n// ========================================\n//\n// TO CHANGE ACTIVITY IMAGES/VIDEOS IN THE ICON SPHERE:\n//\n// 1. Replace the 'media' filename with your desired file\n// 2. Place your media files in the 'public/assets/' folder\n// 3. Supported formats: .gif, .mp4, .webm\n// 4. You can also change the colors and text for each state\n//\n// Current media files used:\n// - startup.gif    (System initialization animation)\n// - rest.mp4       (Idle/standby state - main resting animation)\n// - listening.gif  (When JARVIS is listening for input)\n// - thinking.gif   (When JARVIS is processing/analyzing)\n// - speaking.gif   (When JARVIS is responding/speaking)\n//\n// ========================================\n// ACTIVITY-SPECIFIC POSITIONING & SIZING\n// ========================================\n//\n// Each activity can have different position and size:\n//\n// POSITION CONTROLS:\n// - translateX: Horizontal ('-50%' = center, '-40%' = right, '-60%' = left)\n// - translateY: Vertical ('-50%' = center, '-60%' = up, '-40%' = down)\n//\n// SIZE CONTROLS:\n// - scale: Size multiplier (1.0 = normal, 1.5 = bigger, 0.8 = smaller)\n//\n// EXAMPLES:\n// - Bigger thinking icon: scale: 1.6, translateY: '-60%'\n// - Smaller speaking icon: scale: 1.1, translateY: '-45%'\n// - Move listening right: translateX: '-40%'\n//\n// ========================================\nconst stateConfig={startup:{media:'startup.gif',// CHANGE: Replace with your startup animation file\ntitle:'SYSTEM INITIALIZATION',description:'J.A.R.V.I.S. ONLINE',color:'#00eeff',// CHANGE: Startup glow color (cyan)\nloop:true,// POSITION & SIZE SETTINGS:\nscale:1,// CHANGE: Size multiplier (1.0 = normal, 1.5 = 50% bigger)\ntranslateX:'-50%',// CHANGE: Horizontal position (-50% = centered)\ntranslateY:'-50%'// CHANGE: Vertical position (-50% = centered, -60% = higher up)\n},rest:{media:'rest.mp4',// CHANGE: Replace with your idle/resting animation file\ntitle:'STANDBY MODE',description:'AWAITING COMMAND',color:'#00eeff',// CHANGE: Rest glow color (cyan)\nloop:true,// POSITION & SIZE SETTINGS:\nscale:1.1,// CHANGE: Size multiplier\ntranslateX:'-45%',// CHANGE: Horizontal position\ntranslateY:'-45%'// CHANGE: Vertical position\n},listening:{media:'listening.gif',// CHANGE: Replace with your listening animation file\ntitle:'LISTENING',description:'PROCESSING AUDIO INPUT',color:'#ff00aa',// CHANGE: Listening glow color (pink)\nloop:true,// POSITION & SIZE SETTINGS:\nscale:1,// CHANGE: Size multiplier (slightly bigger for listening)\ntranslateX:'-50%',// CHANGE: Horizontal position\ntranslateY:'-50%'// CHANGE: Vertical position (slightly higher)\n},thinking:{media:'thinking.gif',// CHANGE: Replace with your thinking/processing animation file\ntitle:'PROCESSING',description:'ANALYZING REQUEST',color:'#ff9900',// CHANGE: Thinking glow color (orange)\nloop:true,// POSITION & SIZE SETTINGS:\nscale:1.1,// CHANGE: Size multiplier (bigger for emphasis)\ntranslateX:'-45%',// CHANGE: Horizontal position\ntranslateY:'-48%'// CHANGE: Vertical position (moved up to center better)\n},speaking:{media:'speaking.gif',// CHANGE: Replace with your speaking/responding animation file\ntitle:'RESPONDING',description:'OUTPUT GENERATION',color:'#00ff88',// CHANGE: Speaking glow color (green)\nloop:true,// POSITION & SIZE SETTINGS:\nscale:1,// CHANGE: Size multiplier (medium size)\ntranslateX:'-50%',// CHANGE: Horizontal position\ntranslateY:'-50%'// CHANGE: Vertical position (slightly lower)\n}};// Enhanced magnetized snap to optimized positions\nconst snapToCorner=(x,y)=>{const windowWidth=window.innerWidth;const windowHeight=window.innerHeight;const componentWidth=280;const componentHeight=360;// Optimized margins for different screen areas\nconst topMargin=12;// Close to top edge\nconst sideMargin=12;// Close to side edges\nconst bottomMargin=12;// Close to bottom edge\nconst headerOffset=80;// Below typical headers/navbars\nconst chatInputOffset=100;// Above chat input areas\n// Define magnetized snap zones with priorities\nconst snapZones=[// Top corners (below header area)\n{x:windowWidth-componentWidth-sideMargin,y:headerOffset,corner:'top-right',priority:1,magnetRadius:150},{x:sideMargin,y:headerOffset,corner:'top-left',priority:2,magnetRadius:150},// Middle right/left (vertical center)\n{x:windowWidth-componentWidth-sideMargin,y:(windowHeight-componentHeight)/2,corner:'middle-right',priority:3,magnetRadius:120},{x:sideMargin,y:(windowHeight-componentHeight)/2,corner:'middle-left',priority:4,magnetRadius:120},// Bottom corners (above chat input)\n{x:windowWidth-componentWidth-sideMargin,y:windowHeight-componentHeight-chatInputOffset,corner:'bottom-right',priority:5,magnetRadius:130},{x:sideMargin,y:windowHeight-componentHeight-chatInputOffset,corner:'bottom-left',priority:6,magnetRadius:130},// Extreme corners (very edge positions)\n{x:windowWidth-componentWidth-topMargin,y:topMargin,corner:'extreme-top-right',priority:7,magnetRadius:100},{x:windowWidth-componentWidth-sideMargin,y:windowHeight-componentHeight-bottomMargin,corner:'extreme-bottom-right',priority:8,magnetRadius:100}];// Find the best snap position with magnetism\nlet bestSnap=snapZones[0];let minWeightedDistance=Infinity;snapZones.forEach(zone=>{const distance=Math.sqrt(Math.pow(x-zone.x,2)+Math.pow(y-zone.y,2));// Apply magnetism - closer zones within magnet radius get priority boost\nconst isInMagnetZone=distance<=zone.magnetRadius;const priorityWeight=zone.priority*0.1;// Lower priority number = better\nconst magnetBoost=isInMagnetZone?0.5:1;// 50% boost if in magnet zone\nconst weightedDistance=distance*magnetBoost+priorityWeight;if(weightedDistance<minWeightedDistance){minWeightedDistance=weightedDistance;bestSnap=zone;}});return{x:bestSnap.x,y:bestSnap.y,corner:bestSnap.corner};};// Handle drag events\nconst handleDragStart=()=>{setIsDragging(true);};const handleDrag=(event,info)=>{// Keep drag functionality without visual preview\n// The snap will happen on drag end\n};const handleDragEnd=(event,info)=>{setIsDragging(false);const newPos=snapToCorner(info.point.x-140,info.point.y-180);// Smooth animation to magnetized position\nsetTimeout(()=>{setPosition(newPos);},50);};// Double-tap functionality\nconst[tapCount,setTapCount]=useState(0);const[tapTimer,setTapTimer]=useState(null);const handleDoubleTap=()=>{setTapCount(prev=>prev+1);if(tapTimer){clearTimeout(tapTimer);}const timer=setTimeout(()=>{if(tapCount+1>=2){// Double tap detected - show controls\nsetShowControls(true);// Auto-hide controls after 3 seconds\nsetTimeout(()=>{setShowControls(false);},3000);}setTapCount(0);},300);// 300ms window for double tap\nsetTapTimer(timer);};// Simulate JARVIS states based on processing\nuseEffect(()=>{if(isProcessing){setState('thinking');const timer=setTimeout(()=>{setState('speaking');setTimeout(()=>setState('rest'),2000);},1500);return()=>clearTimeout(timer);}else{setState('rest');}},[isProcessing]);// Auto-cycle through states for demo\nuseEffect(()=>{const interval=setInterval(()=>{if(!isProcessing){setState(prev=>{switch(prev){case'rest':return'listening';case'listening':return'thinking';case'thinking':return'speaking';case'speaking':return'rest';default:return'rest';}});}},3000);return()=>clearInterval(interval);},[isProcessing]);const getStateColor=()=>{var _stateConfig$state;return((_stateConfig$state=stateConfig[state])===null||_stateConfig$state===void 0?void 0:_stateConfig$state.color)||'#00eeff';};const getStateText=()=>{var _stateConfig$state2;return((_stateConfig$state2=stateConfig[state])===null||_stateConfig$state2===void 0?void 0:_stateConfig$state2.title)||'STANDBY MODE';};if(!isVisible)return null;return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(motion.div,{drag:true,dragMomentum:false,dragElastic:0.1,dragConstraints:{left:0,right:window.innerWidth-280,top:0,bottom:window.innerHeight-360},onDragStart:handleDragStart,onDrag:handleDrag,onDragEnd:handleDragEnd,onClick:handleDoubleTap,initial:{scale:0,opacity:0,x:window.innerWidth-196,y:76}// Start at top-right, lower position\n,animate:{scale:1,opacity:1,x:position.x||window.innerWidth-296,y:position.y||76,transition:{type:\"spring\",damping:25,stiffness:200,duration:0.6}},exit:{scale:0,opacity:0},className:\"fixed z-30 select-none \".concat(isDragging?'cursor-grabbing':'cursor-grab'),style:{width:'280px',height:'360px'},whileHover:{scale:1.02},whileDrag:{scale:1.05,zIndex:50},children:[/*#__PURE__*/_jsx(AnimatePresence,{children:showControls&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(motion.button,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{type:\"spring\",damping:15,stiffness:300},onClick:()=>setIsVisible(false),className:\"absolute -top-2 -right-2 w-10 h-10 rounded-full bg-red-500/30 hover:bg-red-500/50 border-2 border-red-500/70 flex items-center justify-center text-red-300 hover:text-red-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-red-500/30\",onMouseDown:e=>e.stopPropagation()// Prevent drag when clicking button\n,style:{boxShadow:'0 0 20px rgba(239, 68, 68, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'},children:\"\\xD7\"}),/*#__PURE__*/_jsx(motion.button,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{type:\"spring\",damping:15,stiffness:300,delay:0.1},onClick:onExpand,className:\"absolute -top-2 -left-2 w-10 h-10 rounded-full bg-cyan-500/30 hover:bg-cyan-500/50 border-2 border-cyan-500/70 flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-cyan-500/30\",title:\"Expand JARVIS\",onMouseDown:e=>e.stopPropagation()// Prevent drag when clicking button\n,style:{boxShadow:'0 0 20px rgba(6, 182, 212, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'},children:\"\\u2197\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center h-full\",children:[/*#__PURE__*/_jsxs(motion.div,{className:\"relative w-56 h-56 rounded-full overflow-hidden mb-3\",children:[/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:1.05},transition:{duration:0.6,ease:[0.4,0,0.2,1],opacity:{duration:0.4}},className:\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden\",children:stateConfig[state].media.endsWith('.mp4')?/*#__PURE__*/_jsx(\"video\",{src:\"/assets/\".concat(stateConfig[state].media),autoPlay:true,muted:true,loop:true,playsInline:true,className:\"rounded-full\",style:{clipPath:'circle(50% at 50% 50%)',width:'120%',height:'120%',objectFit:'cover',position:'absolute',top:'50%',left:'50%',transform:\"scale(\".concat(stateConfig[state].scale,\") translate(\").concat(stateConfig[state].translateX,\", \").concat(stateConfig[state].translateY,\")\"),pointerEvents:'none',userSelect:'none',transition:'all 0.3s ease-in-out'},onError:e=>{// Fallback to SVG if video fails to load\ne.target.style.display='none';}}):/*#__PURE__*/_jsx(\"img\",{src:\"/assets/\".concat(stateConfig[state].media),alt:state,draggable:false,className:\"rounded-full\",style:{clipPath:'circle(50% at 50% 50%)',width:'120%',height:'120%',objectFit:'cover',imageRendering:'auto',filter:'contrast(1.1) brightness(1.05)',position:'absolute',top:'50%',left:'50%',transform:\"scale(\".concat(stateConfig[state].scale,\") translate(\").concat(stateConfig[state].translateX,\", \").concat(stateConfig[state].translateY,\")\"),transition:'all 0.3s ease-in-out',pointerEvents:'none',userSelect:'none'},onError:e=>{// Fallback to SVG if GIF fails to load\ne.target.style.display='none';e.target.parentElement.innerHTML=\"\\n                      <div class=\\\"w-full h-full rounded-full border-2 flex items-center justify-center\\\"\\n                           style=\\\"border-color: \".concat(getStateColor(),\"; box-shadow: 0 0 20px \").concat(getStateColor(),\"40;\\\">\\n                        <svg viewBox=\\\"0 0 100 100\\\" class=\\\"w-16 h-16\\\">\\n                          <circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"30\\\" fill=\\\"none\\\" stroke=\\\"\").concat(getStateColor(),\"\\\" stroke-width=\\\"2\\\"/>\\n                          <circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"20\\\" fill=\\\"none\\\" stroke=\\\"\").concat(getStateColor(),\"\\\" stroke-width=\\\"1.5\\\" opacity=\\\"0.7\\\"/>\\n                          <circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"8\\\" fill=\\\"\").concat(getStateColor(),\"\\\"/>\\n                          <path d=\\\"M30 50 L70 50 M50 30 L50 70\\\" stroke=\\\"\").concat(getStateColor(),\"\\\" stroke-width=\\\"1.5\\\" opacity=\\\"0.8\\\"/>\\n                        </svg>\\n                      </div>\\n                    \");}})},state)}),/*#__PURE__*/_jsx(motion.div,{className:\"absolute inset-0 rounded-full pointer-events-none border-2\",style:{borderColor:getStateColor(),animation:\"blinkingCircle 2s ease-in-out infinite\",boxShadow:\"0 0 20px \".concat(getStateColor(),\"60\")}})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-base font-bold transition-colors duration-300 mb-1\",style:{color:getStateColor()},children:getStateText()}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-300 font-medium tracking-wider\",children:\"JARVIS\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-400\",children:(_stateConfig$state3=stateConfig[state])===null||_stateConfig$state3===void 0?void 0:_stateConfig$state3.description})]})]})]})});};export default MinimizedJarvis;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "Mini<PERSON><PERSON><PERSON><PERSON>", "_ref", "_stateConfig$state3", "onExpand", "isProcessing", "state", "setState", "isVisible", "setIsVisible", "position", "setPosition", "x", "y", "isDragging", "setIsDragging", "showControls", "setShowControls", "stateConfig", "startup", "media", "title", "description", "color", "loop", "scale", "translateX", "translateY", "rest", "listening", "thinking", "speaking", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "windowWidth", "window", "innerWidth", "windowHeight", "innerHeight", "componentWidth", "componentHeight", "<PERSON><PERSON><PERSON><PERSON>", "sideMargin", "bottom<PERSON>argin", "headerOffset", "chatInputOffset", "snapZones", "corner", "priority", "magnetRadius", "bestSnap", "minWeightedDistance", "Infinity", "for<PERSON>ach", "zone", "distance", "Math", "sqrt", "pow", "isInMagnetZone", "priorityWeight", "magnetBoost", "weightedDistance", "handleDragStart", "handleDrag", "event", "info", "handleDragEnd", "newPos", "point", "setTimeout", "tapCount", "setTapCount", "tapTimer", "setTapTimer", "handleDoubleTap", "prev", "clearTimeout", "timer", "interval", "setInterval", "clearInterval", "getStateColor", "_stateConfig$state", "getStateText", "_stateConfig$state2", "children", "div", "drag", "dragMomentum", "dragElastic", "dragConstraints", "left", "right", "top", "bottom", "onDragStart", "onDrag", "onDragEnd", "onClick", "initial", "opacity", "animate", "transition", "type", "damping", "stiffness", "duration", "exit", "className", "concat", "style", "width", "height", "whileHover", "whileDrag", "zIndex", "button", "onMouseDown", "e", "stopPropagation", "boxShadow", "delay", "mode", "ease", "endsWith", "src", "autoPlay", "muted", "playsInline", "clipPath", "objectFit", "transform", "pointerEvents", "userSelect", "onError", "target", "display", "alt", "draggable", "imageRendering", "filter", "parentElement", "innerHTML", "borderColor", "animation"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ui/MinimizedJarvis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst MinimizedJarvis = ({ onExpand, isProcessing = false }) => {\n  const [state, setState] = useState('rest');\n  const [isVisible, setIsVisible] = useState(true);\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [showControls, setShowControls] = useState(false);\n\n\n\n  // ========================================\n  // JARVIS ACTIVITY MEDIA CONFIGURATION\n  // ========================================\n  //\n  // TO CHANGE ACTIVITY IMAGES/VIDEOS IN THE ICON SPHERE:\n  //\n  // 1. Replace the 'media' filename with your desired file\n  // 2. Place your media files in the 'public/assets/' folder\n  // 3. Supported formats: .gif, .mp4, .webm\n  // 4. You can also change the colors and text for each state\n  //\n  // Current media files used:\n  // - startup.gif    (System initialization animation)\n  // - rest.mp4       (Idle/standby state - main resting animation)\n  // - listening.gif  (When JARVIS is listening for input)\n  // - thinking.gif   (When JARVIS is processing/analyzing)\n  // - speaking.gif   (When JARVIS is responding/speaking)\n  //\n  // ========================================\n  // ACTIVITY-SPECIFIC POSITIONING & SIZING\n  // ========================================\n  //\n  // Each activity can have different position and size:\n  //\n  // POSITION CONTROLS:\n  // - translateX: Horizontal ('-50%' = center, '-40%' = right, '-60%' = left)\n  // - translateY: Vertical ('-50%' = center, '-60%' = up, '-40%' = down)\n  //\n  // SIZE CONTROLS:\n  // - scale: Size multiplier (1.0 = normal, 1.5 = bigger, 0.8 = smaller)\n  //\n  // EXAMPLES:\n  // - Bigger thinking icon: scale: 1.6, translateY: '-60%'\n  // - Smaller speaking icon: scale: 1.1, translateY: '-45%'\n  // - Move listening right: translateX: '-40%'\n  //\n  // ========================================\n\n  const stateConfig = {\n    startup: {\n      media: 'startup.gif',        // CHANGE: Replace with your startup animation file\n      title: 'SYSTEM INITIALIZATION',\n      description: 'J.A.R.V.I.S. ONLINE',\n      color: '#00eeff',            // CHANGE: Startup glow color (cyan)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,                  // CHANGE: Size multiplier (1.0 = normal, 1.5 = 50% bigger)\n      translateX: '-50%',          // CHANGE: Horizontal position (-50% = centered)\n      translateY: '-50%'           // CHANGE: Vertical position (-50% = centered, -60% = higher up)\n    },\n    rest: {\n      media: 'rest.mp4',           // CHANGE: Replace with your idle/resting animation file\n      title: 'STANDBY MODE',\n      description: 'AWAITING COMMAND',\n      color: '#00eeff',            // CHANGE: Rest glow color (cyan)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1.1,                  // CHANGE: Size multiplier\n      translateX: '-45%',          // CHANGE: Horizontal position\n      translateY: '-45%'           // CHANGE: Vertical position\n    },\n    listening: {\n      media: 'listening.gif',      // CHANGE: Replace with your listening animation file\n      title: 'LISTENING',\n      description: 'PROCESSING AUDIO INPUT',\n      color: '#ff00aa',            // CHANGE: Listening glow color (pink)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,                  // CHANGE: Size multiplier (slightly bigger for listening)\n      translateX: '-50%',          // CHANGE: Horizontal position\n      translateY: '-50%'           // CHANGE: Vertical position (slightly higher)\n    },\n    thinking: {\n      media: 'thinking.gif',       // CHANGE: Replace with your thinking/processing animation file\n      title: 'PROCESSING',\n      description: 'ANALYZING REQUEST',\n      color: '#ff9900',            // CHANGE: Thinking glow color (orange)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1.1,                  // CHANGE: Size multiplier (bigger for emphasis)\n      translateX: '-45%',          // CHANGE: Horizontal position\n      translateY: '-48%'           // CHANGE: Vertical position (moved up to center better)\n    },\n    speaking: {\n      media: 'speaking.gif',       // CHANGE: Replace with your speaking/responding animation file\n      title: 'RESPONDING',\n      description: 'OUTPUT GENERATION',\n      color: '#00ff88',            // CHANGE: Speaking glow color (green)\n      loop: true,\n      // POSITION & SIZE SETTINGS:\n      scale: 1,                 // CHANGE: Size multiplier (medium size)\n      translateX: '-50%',          // CHANGE: Horizontal position\n      translateY: '-50%'           // CHANGE: Vertical position (slightly lower)\n    }\n  };\n\n\n  // Enhanced magnetized snap to optimized positions\n  const snapToCorner = (x, y) => {\n    const windowWidth = window.innerWidth;\n    const windowHeight = window.innerHeight;\n    const componentWidth = 280;\n    const componentHeight = 360;\n\n    // Optimized margins for different screen areas\n    const topMargin = 12;           // Close to top edge\n    const sideMargin = 12;          // Close to side edges\n    const bottomMargin = 12;        // Close to bottom edge\n    const headerOffset = 80;       // Below typical headers/navbars\n    const chatInputOffset = 100;   // Above chat input areas\n\n    // Define magnetized snap zones with priorities\n    const snapZones = [\n      // Top corners (below header area)\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: headerOffset,\n        corner: 'top-right',\n        priority: 1,\n        magnetRadius: 150\n      },\n      {\n        x: sideMargin,\n        y: headerOffset,\n        corner: 'top-left',\n        priority: 2,\n        magnetRadius: 150\n      },\n\n      // Middle right/left (vertical center)\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: (windowHeight - componentHeight) / 2,\n        corner: 'middle-right',\n        priority: 3,\n        magnetRadius: 120\n      },\n      {\n        x: sideMargin,\n        y: (windowHeight - componentHeight) / 2,\n        corner: 'middle-left',\n        priority: 4,\n        magnetRadius: 120\n      },\n\n      // Bottom corners (above chat input)\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: windowHeight - componentHeight - chatInputOffset,\n        corner: 'bottom-right',\n        priority: 5,\n        magnetRadius: 130\n      },\n      {\n        x: sideMargin,\n        y: windowHeight - componentHeight - chatInputOffset,\n        corner: 'bottom-left',\n        priority: 6,\n        magnetRadius: 130\n      },\n\n      // Extreme corners (very edge positions)\n      {\n        x: windowWidth - componentWidth - topMargin,\n        y: topMargin,\n        corner: 'extreme-top-right',\n        priority: 7,\n        magnetRadius: 100\n      },\n      {\n        x: windowWidth - componentWidth - sideMargin,\n        y: windowHeight - componentHeight - bottomMargin,\n        corner: 'extreme-bottom-right',\n        priority: 8,\n        magnetRadius: 100\n      }\n    ];\n\n    // Find the best snap position with magnetism\n    let bestSnap = snapZones[0];\n    let minWeightedDistance = Infinity;\n\n    snapZones.forEach(zone => {\n      const distance = Math.sqrt(Math.pow(x - zone.x, 2) + Math.pow(y - zone.y, 2));\n\n      // Apply magnetism - closer zones within magnet radius get priority boost\n      const isInMagnetZone = distance <= zone.magnetRadius;\n      const priorityWeight = zone.priority * 0.1; // Lower priority number = better\n      const magnetBoost = isInMagnetZone ? 0.5 : 1; // 50% boost if in magnet zone\n\n      const weightedDistance = (distance * magnetBoost) + priorityWeight;\n\n      if (weightedDistance < minWeightedDistance) {\n        minWeightedDistance = weightedDistance;\n        bestSnap = zone;\n      }\n    });\n\n    return { x: bestSnap.x, y: bestSnap.y, corner: bestSnap.corner };\n  };\n\n  // Handle drag events\n  const handleDragStart = () => {\n    setIsDragging(true);\n  };\n\n  const handleDrag = (event, info) => {\n    // Keep drag functionality without visual preview\n    // The snap will happen on drag end\n  };\n\n  const handleDragEnd = (event, info) => {\n    setIsDragging(false);\n\n    const newPos = snapToCorner(info.point.x - 140, info.point.y - 180);\n\n    // Smooth animation to magnetized position\n    setTimeout(() => {\n      setPosition(newPos);\n    }, 50);\n  };\n\n  // Double-tap functionality\n  const [tapCount, setTapCount] = useState(0);\n  const [tapTimer, setTapTimer] = useState(null);\n\n  const handleDoubleTap = () => {\n    setTapCount(prev => prev + 1);\n\n    if (tapTimer) {\n      clearTimeout(tapTimer);\n    }\n\n    const timer = setTimeout(() => {\n      if (tapCount + 1 >= 2) {\n        // Double tap detected - show controls\n        setShowControls(true);\n\n        // Auto-hide controls after 3 seconds\n        setTimeout(() => {\n          setShowControls(false);\n        }, 3000);\n      }\n      setTapCount(0);\n    }, 300); // 300ms window for double tap\n\n    setTapTimer(timer);\n  };\n\n  // Simulate JARVIS states based on processing\n  useEffect(() => {\n    if (isProcessing) {\n      setState('thinking');\n      const timer = setTimeout(() => {\n        setState('speaking');\n        setTimeout(() => setState('rest'), 2000);\n      }, 1500);\n      return () => clearTimeout(timer);\n    } else {\n      setState('rest');\n    }\n  }, [isProcessing]);\n\n  // Auto-cycle through states for demo\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (!isProcessing) {\n        setState(prev => {\n          switch (prev) {\n            case 'rest': return 'listening';\n            case 'listening': return 'thinking';\n            case 'thinking': return 'speaking';\n            case 'speaking': return 'rest';\n            default: return 'rest';\n          }\n        });\n      }\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, [isProcessing]);\n\n  const getStateColor = () => {\n    return stateConfig[state]?.color || '#00eeff';\n  };\n\n  const getStateText = () => {\n    return stateConfig[state]?.title || 'STANDBY MODE';\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <>\n\n\n      {/* Main Draggable Window */}\n    <motion.div\n      drag\n      dragMomentum={false}\n      dragElastic={0.1}\n      dragConstraints={{\n        left: 0,\n        right: window.innerWidth - 280,\n        top: 0,\n        bottom: window.innerHeight - 360\n      }}\n      onDragStart={handleDragStart}\n      onDrag={handleDrag}\n      onDragEnd={handleDragEnd}\n      onClick={handleDoubleTap}\n      initial={{ scale: 0, opacity: 0, x: window.innerWidth - 196, y: 76 }} // Start at top-right, lower position\n      animate={{\n        scale: 1,\n        opacity: 1,\n        x: position.x || window.innerWidth - 296,\n        y: position.y || 76,\n        transition: {\n          type: \"spring\",\n          damping: 25,\n          stiffness: 200,\n          duration: 0.6\n        }\n      }}\n      exit={{ scale: 0, opacity: 0 }}\n      className={`fixed z-30 select-none ${\n        isDragging ? 'cursor-grabbing' : 'cursor-grab'\n      }`}\n      style={{ width: '280px', height: '360px' }}\n      whileHover={{ scale: 1.02 }}\n      whileDrag={{ scale: 1.05, zIndex: 50 }}\n    >\n\n\n      {/* Control Buttons - Only visible when double-tapped */}\n      <AnimatePresence>\n        {showControls && (\n          <>\n            {/* Close/Minimize Button */}\n            <motion.button\n              initial={{ scale: 0, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0, opacity: 0 }}\n              transition={{ type: \"spring\", damping: 15, stiffness: 300 }}\n              onClick={() => setIsVisible(false)}\n              className=\"absolute -top-2 -right-2 w-10 h-10 rounded-full bg-red-500/30 hover:bg-red-500/50 border-2 border-red-500/70 flex items-center justify-center text-red-300 hover:text-red-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-red-500/30\"\n              onMouseDown={(e) => e.stopPropagation()} // Prevent drag when clicking button\n              style={{\n                boxShadow: '0 0 20px rgba(239, 68, 68, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'\n              }}\n            >\n              ×\n            </motion.button>\n\n            {/* Expand Button */}\n            <motion.button\n              initial={{ scale: 0, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0, opacity: 0 }}\n              transition={{ type: \"spring\", damping: 15, stiffness: 300, delay: 0.1 }}\n              onClick={onExpand}\n              className=\"absolute -top-2 -left-2 w-10 h-10 rounded-full bg-cyan-500/30 hover:bg-cyan-500/50 border-2 border-cyan-500/70 flex items-center justify-center text-cyan-300 hover:text-cyan-100 transition-colors text-lg font-bold z-20 shadow-lg shadow-cyan-500/30\"\n              title=\"Expand JARVIS\"\n              onMouseDown={(e) => e.stopPropagation()} // Prevent drag when clicking button\n              style={{\n                boxShadow: '0 0 20px rgba(6, 182, 212, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3)'\n              }}\n            >\n              ↗\n            </motion.button>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* JARVIS Media Display - Larger and more circular with Audio Reactivity */}\n      <div className=\"flex flex-col items-center justify-center h-full\">\n        {/* Media Container */}\n        <motion.div\n          className=\"relative w-56 h-56 rounded-full overflow-hidden mb-3\"\n        >\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={state}\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 1.05 }}\n              transition={{\n                duration: 0.6,\n                ease: [0.4, 0, 0.2, 1],\n                opacity: { duration: 0.4 }\n              }}\n              className=\"absolute inset-0 flex items-center justify-center rounded-full overflow-hidden\"\n            >\n              {stateConfig[state].media.endsWith('.mp4') ? (\n                <video\n                  src={`/assets/${stateConfig[state].media}`}\n                  autoPlay\n                  muted\n                  loop\n                  playsInline\n                  className=\"rounded-full\"\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    width: '120%',\n                    height: '120%',\n                    objectFit: 'cover',\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,\n                    pointerEvents: 'none',\n                    userSelect: 'none',\n                    transition: 'all 0.3s ease-in-out'\n                  }}\n                  onError={(e) => {\n                    // Fallback to SVG if video fails to load\n                    e.target.style.display = 'none';\n                  }}\n                />\n              ) : (\n                <img\n                  src={`/assets/${stateConfig[state].media}`}\n                  alt={state}\n                  draggable={false}\n                  className=\"rounded-full\"\n                  style={{\n                    clipPath: 'circle(50% at 50% 50%)',\n                    width: '120%',\n                    height: '120%',\n                    objectFit: 'cover',\n                    imageRendering: 'auto',\n                    filter: 'contrast(1.1) brightness(1.05)',\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: `scale(${stateConfig[state].scale}) translate(${stateConfig[state].translateX}, ${stateConfig[state].translateY})`,\n                    transition: 'all 0.3s ease-in-out',\n                    pointerEvents: 'none',\n                    userSelect: 'none'\n                  }}\n                  onError={(e) => {\n                    // Fallback to SVG if GIF fails to load\n                    e.target.style.display = 'none';\n                    e.target.parentElement.innerHTML = `\n                      <div class=\"w-full h-full rounded-full border-2 flex items-center justify-center\"\n                           style=\"border-color: ${getStateColor()}; box-shadow: 0 0 20px ${getStateColor()}40;\">\n                        <svg viewBox=\"0 0 100 100\" class=\"w-16 h-16\">\n                          <circle cx=\"50\" cy=\"50\" r=\"30\" fill=\"none\" stroke=\"${getStateColor()}\" stroke-width=\"2\"/>\n                          <circle cx=\"50\" cy=\"50\" r=\"20\" fill=\"none\" stroke=\"${getStateColor()}\" stroke-width=\"1.5\" opacity=\"0.7\"/>\n                          <circle cx=\"50\" cy=\"50\" r=\"8\" fill=\"${getStateColor()}\"/>\n                          <path d=\"M30 50 L70 50 M50 30 L50 70\" stroke=\"${getStateColor()}\" stroke-width=\"1.5\" opacity=\"0.8\"/>\n                        </svg>\n                      </div>\n                    `;\n                  }}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          {/* Blinking Circle Animation */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full pointer-events-none border-2\"\n            style={{\n              borderColor: getStateColor(),\n              animation: `blinkingCircle 2s ease-in-out infinite`,\n              boxShadow: `0 0 20px ${getStateColor()}60`\n            }}\n          />\n        </motion.div>\n\n        {/* Status Text */}\n        <div className=\"text-center\">\n          <div\n            className=\"text-base font-bold transition-colors duration-300 mb-1\"\n            style={{\n              color: getStateColor()\n            }}\n          >\n            {getStateText()}\n          </div>\n          <div className=\"text-sm text-gray-300 font-medium tracking-wider\">\n            JARVIS\n          </div>\n          <div className=\"text-xs text-gray-400\">\n            {stateConfig[state]?.description}\n          </div>\n        </div>\n      </div>\n\n\n    </motion.div>\n    </>\n  );\n};\n\nexport default MinimizedJarvis;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAwC,KAAAC,mBAAA,IAAvC,CAAEC,QAAQ,CAAEC,YAAY,CAAG,KAAM,CAAC,CAAAH,IAAA,CACzD,KAAM,CAACI,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAC,MAAM,CAAC,CAC1C,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACmB,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,CAAEqB,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CACxD,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACyB,YAAY,CAAEC,eAAe,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAIvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAA2B,WAAW,CAAG,CAClBC,OAAO,CAAE,CACPC,KAAK,CAAE,aAAa,CAAS;AAC7BC,KAAK,CAAE,uBAAuB,CAC9BC,WAAW,CAAE,qBAAqB,CAClCC,KAAK,CAAE,SAAS,CAAa;AAC7BC,IAAI,CAAE,IAAI,CACV;AACAC,KAAK,CAAE,CAAC,CAAmB;AAC3BC,UAAU,CAAE,MAAM,CAAW;AAC7BC,UAAU,CAAE,MAAiB;AAC/B,CAAC,CACDC,IAAI,CAAE,CACJR,KAAK,CAAE,UAAU,CAAY;AAC7BC,KAAK,CAAE,cAAc,CACrBC,WAAW,CAAE,kBAAkB,CAC/BC,KAAK,CAAE,SAAS,CAAa;AAC7BC,IAAI,CAAE,IAAI,CACV;AACAC,KAAK,CAAE,GAAG,CAAmB;AAC7BC,UAAU,CAAE,MAAM,CAAW;AAC7BC,UAAU,CAAE,MAAiB;AAC/B,CAAC,CACDE,SAAS,CAAE,CACTT,KAAK,CAAE,eAAe,CAAO;AAC7BC,KAAK,CAAE,WAAW,CAClBC,WAAW,CAAE,wBAAwB,CACrCC,KAAK,CAAE,SAAS,CAAa;AAC7BC,IAAI,CAAE,IAAI,CACV;AACAC,KAAK,CAAE,CAAC,CAAmB;AAC3BC,UAAU,CAAE,MAAM,CAAW;AAC7BC,UAAU,CAAE,MAAiB;AAC/B,CAAC,CACDG,QAAQ,CAAE,CACRV,KAAK,CAAE,cAAc,CAAQ;AAC7BC,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,mBAAmB,CAChCC,KAAK,CAAE,SAAS,CAAa;AAC7BC,IAAI,CAAE,IAAI,CACV;AACAC,KAAK,CAAE,GAAG,CAAmB;AAC7BC,UAAU,CAAE,MAAM,CAAW;AAC7BC,UAAU,CAAE,MAAiB;AAC/B,CAAC,CACDI,QAAQ,CAAE,CACRX,KAAK,CAAE,cAAc,CAAQ;AAC7BC,KAAK,CAAE,YAAY,CACnBC,WAAW,CAAE,mBAAmB,CAChCC,KAAK,CAAE,SAAS,CAAa;AAC7BC,IAAI,CAAE,IAAI,CACV;AACAC,KAAK,CAAE,CAAC,CAAkB;AAC1BC,UAAU,CAAE,MAAM,CAAW;AAC7BC,UAAU,CAAE,MAAiB;AAC/B,CACF,CAAC,CAGD;AACA,KAAM,CAAAK,YAAY,CAAGA,CAACpB,CAAC,CAAEC,CAAC,GAAK,CAC7B,KAAM,CAAAoB,WAAW,CAAGC,MAAM,CAACC,UAAU,CACrC,KAAM,CAAAC,YAAY,CAAGF,MAAM,CAACG,WAAW,CACvC,KAAM,CAAAC,cAAc,CAAG,GAAG,CAC1B,KAAM,CAAAC,eAAe,CAAG,GAAG,CAE3B;AACA,KAAM,CAAAC,SAAS,CAAG,EAAE,CAAY;AAChC,KAAM,CAAAC,UAAU,CAAG,EAAE,CAAW;AAChC,KAAM,CAAAC,YAAY,CAAG,EAAE,CAAS;AAChC,KAAM,CAAAC,YAAY,CAAG,EAAE,CAAQ;AAC/B,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAI;AAE/B;AACA,KAAM,CAAAC,SAAS,CAAG,CAChB;AACA,CACEjC,CAAC,CAAEqB,WAAW,CAAGK,cAAc,CAAGG,UAAU,CAC5C5B,CAAC,CAAE8B,YAAY,CACfG,MAAM,CAAE,WAAW,CACnBC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CACD,CACEpC,CAAC,CAAE6B,UAAU,CACb5B,CAAC,CAAE8B,YAAY,CACfG,MAAM,CAAE,UAAU,CAClBC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CAED;AACA,CACEpC,CAAC,CAAEqB,WAAW,CAAGK,cAAc,CAAGG,UAAU,CAC5C5B,CAAC,CAAE,CAACuB,YAAY,CAAGG,eAAe,EAAI,CAAC,CACvCO,MAAM,CAAE,cAAc,CACtBC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CACD,CACEpC,CAAC,CAAE6B,UAAU,CACb5B,CAAC,CAAE,CAACuB,YAAY,CAAGG,eAAe,EAAI,CAAC,CACvCO,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CAED;AACA,CACEpC,CAAC,CAAEqB,WAAW,CAAGK,cAAc,CAAGG,UAAU,CAC5C5B,CAAC,CAAEuB,YAAY,CAAGG,eAAe,CAAGK,eAAe,CACnDE,MAAM,CAAE,cAAc,CACtBC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CACD,CACEpC,CAAC,CAAE6B,UAAU,CACb5B,CAAC,CAAEuB,YAAY,CAAGG,eAAe,CAAGK,eAAe,CACnDE,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CAED;AACA,CACEpC,CAAC,CAAEqB,WAAW,CAAGK,cAAc,CAAGE,SAAS,CAC3C3B,CAAC,CAAE2B,SAAS,CACZM,MAAM,CAAE,mBAAmB,CAC3BC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CACD,CACEpC,CAAC,CAAEqB,WAAW,CAAGK,cAAc,CAAGG,UAAU,CAC5C5B,CAAC,CAAEuB,YAAY,CAAGG,eAAe,CAAGG,YAAY,CAChDI,MAAM,CAAE,sBAAsB,CAC9BC,QAAQ,CAAE,CAAC,CACXC,YAAY,CAAE,GAChB,CAAC,CACF,CAED;AACA,GAAI,CAAAC,QAAQ,CAAGJ,SAAS,CAAC,CAAC,CAAC,CAC3B,GAAI,CAAAK,mBAAmB,CAAGC,QAAQ,CAElCN,SAAS,CAACO,OAAO,CAACC,IAAI,EAAI,CACxB,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC7C,CAAC,CAAGyC,IAAI,CAACzC,CAAC,CAAE,CAAC,CAAC,CAAG2C,IAAI,CAACE,GAAG,CAAC5C,CAAC,CAAGwC,IAAI,CAACxC,CAAC,CAAE,CAAC,CAAC,CAAC,CAE7E;AACA,KAAM,CAAA6C,cAAc,CAAGJ,QAAQ,EAAID,IAAI,CAACL,YAAY,CACpD,KAAM,CAAAW,cAAc,CAAGN,IAAI,CAACN,QAAQ,CAAG,GAAG,CAAE;AAC5C,KAAM,CAAAa,WAAW,CAAGF,cAAc,CAAG,GAAG,CAAG,CAAC,CAAE;AAE9C,KAAM,CAAAG,gBAAgB,CAAIP,QAAQ,CAAGM,WAAW,CAAID,cAAc,CAElE,GAAIE,gBAAgB,CAAGX,mBAAmB,CAAE,CAC1CA,mBAAmB,CAAGW,gBAAgB,CACtCZ,QAAQ,CAAGI,IAAI,CACjB,CACF,CAAC,CAAC,CAEF,MAAO,CAAEzC,CAAC,CAAEqC,QAAQ,CAACrC,CAAC,CAAEC,CAAC,CAAEoC,QAAQ,CAACpC,CAAC,CAAEiC,MAAM,CAAEG,QAAQ,CAACH,MAAO,CAAC,CAClE,CAAC,CAED;AACA,KAAM,CAAAgB,eAAe,CAAGA,CAAA,GAAM,CAC5B/C,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAAgD,UAAU,CAAGA,CAACC,KAAK,CAAEC,IAAI,GAAK,CAClC;AACA;AAAA,CACD,CAED,KAAM,CAAAC,aAAa,CAAGA,CAACF,KAAK,CAAEC,IAAI,GAAK,CACrClD,aAAa,CAAC,KAAK,CAAC,CAEpB,KAAM,CAAAoD,MAAM,CAAGnC,YAAY,CAACiC,IAAI,CAACG,KAAK,CAACxD,CAAC,CAAG,GAAG,CAAEqD,IAAI,CAACG,KAAK,CAACvD,CAAC,CAAG,GAAG,CAAC,CAEnE;AACAwD,UAAU,CAAC,IAAM,CACf1D,WAAW,CAACwD,MAAM,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CACR,CAAC,CAED;AACA,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGhF,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACiF,QAAQ,CAAEC,WAAW,CAAC,CAAGlF,QAAQ,CAAC,IAAI,CAAC,CAE9C,KAAM,CAAAmF,eAAe,CAAGA,CAAA,GAAM,CAC5BH,WAAW,CAACI,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAE7B,GAAIH,QAAQ,CAAE,CACZI,YAAY,CAACJ,QAAQ,CAAC,CACxB,CAEA,KAAM,CAAAK,KAAK,CAAGR,UAAU,CAAC,IAAM,CAC7B,GAAIC,QAAQ,CAAG,CAAC,EAAI,CAAC,CAAE,CACrB;AACArD,eAAe,CAAC,IAAI,CAAC,CAErB;AACAoD,UAAU,CAAC,IAAM,CACfpD,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAAE,IAAI,CAAC,CACV,CACAsD,WAAW,CAAC,CAAC,CAAC,CAChB,CAAC,CAAE,GAAG,CAAC,CAAE;AAETE,WAAW,CAACI,KAAK,CAAC,CACpB,CAAC,CAED;AACArF,SAAS,CAAC,IAAM,CACd,GAAIa,YAAY,CAAE,CAChBE,QAAQ,CAAC,UAAU,CAAC,CACpB,KAAM,CAAAsE,KAAK,CAAGR,UAAU,CAAC,IAAM,CAC7B9D,QAAQ,CAAC,UAAU,CAAC,CACpB8D,UAAU,CAAC,IAAM9D,QAAQ,CAAC,MAAM,CAAC,CAAE,IAAI,CAAC,CAC1C,CAAC,CAAE,IAAI,CAAC,CACR,MAAO,IAAMqE,YAAY,CAACC,KAAK,CAAC,CAClC,CAAC,IAAM,CACLtE,QAAQ,CAAC,MAAM,CAAC,CAClB,CACF,CAAC,CAAE,CAACF,YAAY,CAAC,CAAC,CAElB;AACAb,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsF,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjC,GAAI,CAAC1E,YAAY,CAAE,CACjBE,QAAQ,CAACoE,IAAI,EAAI,CACf,OAAQA,IAAI,EACV,IAAK,MAAM,CAAE,MAAO,WAAW,CAC/B,IAAK,WAAW,CAAE,MAAO,UAAU,CACnC,IAAK,UAAU,CAAE,MAAO,UAAU,CAClC,IAAK,UAAU,CAAE,MAAO,MAAM,CAC9B,QAAS,MAAO,MAAM,CACxB,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMK,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACzE,YAAY,CAAC,CAAC,CAElB,KAAM,CAAA4E,aAAa,CAAGA,CAAA,GAAM,KAAAC,kBAAA,CAC1B,MAAO,EAAAA,kBAAA,CAAAhE,WAAW,CAACZ,KAAK,CAAC,UAAA4E,kBAAA,iBAAlBA,kBAAA,CAAoB3D,KAAK,GAAI,SAAS,CAC/C,CAAC,CAED,KAAM,CAAA4D,YAAY,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CACzB,MAAO,EAAAA,mBAAA,CAAAlE,WAAW,CAACZ,KAAK,CAAC,UAAA8E,mBAAA,iBAAlBA,mBAAA,CAAoB/D,KAAK,GAAI,cAAc,CACpD,CAAC,CAED,GAAI,CAACb,SAAS,CAAE,MAAO,KAAI,CAE3B,mBACEZ,IAAA,CAAAE,SAAA,EAAAuF,QAAA,cAIArF,KAAA,CAACP,MAAM,CAAC6F,GAAG,EACTC,IAAI,MACJC,YAAY,CAAE,KAAM,CACpBC,WAAW,CAAE,GAAI,CACjBC,eAAe,CAAE,CACfC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE1D,MAAM,CAACC,UAAU,CAAG,GAAG,CAC9B0D,GAAG,CAAE,CAAC,CACNC,MAAM,CAAE5D,MAAM,CAACG,WAAW,CAAG,GAC/B,CAAE,CACF0D,WAAW,CAAEjC,eAAgB,CAC7BkC,MAAM,CAAEjC,UAAW,CACnBkC,SAAS,CAAE/B,aAAc,CACzBgC,OAAO,CAAExB,eAAgB,CACzByB,OAAO,CAAE,CAAE1E,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAC,CAAExF,CAAC,CAAEsB,MAAM,CAACC,UAAU,CAAG,GAAG,CAAEtB,CAAC,CAAE,EAAG,CAAG;AAAA,CACtEwF,OAAO,CAAE,CACP5E,KAAK,CAAE,CAAC,CACR2E,OAAO,CAAE,CAAC,CACVxF,CAAC,CAAEF,QAAQ,CAACE,CAAC,EAAIsB,MAAM,CAACC,UAAU,CAAG,GAAG,CACxCtB,CAAC,CAAEH,QAAQ,CAACG,CAAC,EAAI,EAAE,CACnByF,UAAU,CAAE,CACVC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,GACZ,CACF,CAAE,CACFC,IAAI,CAAE,CAAElF,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAE,CAAE,CAC/BQ,SAAS,2BAAAC,MAAA,CACP/F,UAAU,CAAG,iBAAiB,CAAG,aAAa,CAC7C,CACHgG,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAO,CAAEC,MAAM,CAAE,OAAQ,CAAE,CAC3CC,UAAU,CAAE,CAAExF,KAAK,CAAE,IAAK,CAAE,CAC5ByF,SAAS,CAAE,CAAEzF,KAAK,CAAE,IAAI,CAAE0F,MAAM,CAAE,EAAG,CAAE,CAAA9B,QAAA,eAKvCzF,IAAA,CAACF,eAAe,EAAA2F,QAAA,CACbrE,YAAY,eACXhB,KAAA,CAAAF,SAAA,EAAAuF,QAAA,eAEEzF,IAAA,CAACH,MAAM,CAAC2H,MAAM,EACZjB,OAAO,CAAE,CAAE1E,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAE,CAAE,CAClCC,OAAO,CAAE,CAAE5E,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAE,CAAE,CAClCO,IAAI,CAAE,CAAElF,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAE,CAAE,CAC/BE,UAAU,CAAE,CAAEC,IAAI,CAAE,QAAQ,CAAEC,OAAO,CAAE,EAAE,CAAEC,SAAS,CAAE,GAAI,CAAE,CAC5DP,OAAO,CAAEA,CAAA,GAAMzF,YAAY,CAAC,KAAK,CAAE,CACnCmG,SAAS,CAAC,oPAAoP,CAC9PS,WAAW,CAAGC,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAG;AAAA,CACzCT,KAAK,CAAE,CACLU,SAAS,CAAE,gEACb,CAAE,CAAAnC,QAAA,CACH,MAED,CAAe,CAAC,cAGhBzF,IAAA,CAACH,MAAM,CAAC2H,MAAM,EACZjB,OAAO,CAAE,CAAE1E,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAE,CAAE,CAClCC,OAAO,CAAE,CAAE5E,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAE,CAAE,CAClCO,IAAI,CAAE,CAAElF,KAAK,CAAE,CAAC,CAAE2E,OAAO,CAAE,CAAE,CAAE,CAC/BE,UAAU,CAAE,CAAEC,IAAI,CAAE,QAAQ,CAAEC,OAAO,CAAE,EAAE,CAAEC,SAAS,CAAE,GAAG,CAAEgB,KAAK,CAAE,GAAI,CAAE,CACxEvB,OAAO,CAAE9F,QAAS,CAClBwG,SAAS,CAAC,yPAAyP,CACnQvF,KAAK,CAAC,eAAe,CACrBgG,WAAW,CAAGC,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAG;AAAA,CACzCT,KAAK,CAAE,CACLU,SAAS,CAAE,gEACb,CAAE,CAAAnC,QAAA,CACH,QAED,CAAe,CAAC,EAChB,CACH,CACc,CAAC,cAGlBrF,KAAA,QAAK4G,SAAS,CAAC,kDAAkD,CAAAvB,QAAA,eAE/DrF,KAAA,CAACP,MAAM,CAAC6F,GAAG,EACTsB,SAAS,CAAC,sDAAsD,CAAAvB,QAAA,eAEhEzF,IAAA,CAACF,eAAe,EAACgI,IAAI,CAAC,MAAM,CAAArC,QAAA,cAC1BzF,IAAA,CAACH,MAAM,CAAC6F,GAAG,EAETa,OAAO,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAE3E,KAAK,CAAE,IAAK,CAAE,CACrC4E,OAAO,CAAE,CAAED,OAAO,CAAE,CAAC,CAAE3E,KAAK,CAAE,CAAE,CAAE,CAClCkF,IAAI,CAAE,CAAEP,OAAO,CAAE,CAAC,CAAE3E,KAAK,CAAE,IAAK,CAAE,CAClC6E,UAAU,CAAE,CACVI,QAAQ,CAAE,GAAG,CACbiB,IAAI,CAAE,CAAC,GAAG,CAAE,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACtBvB,OAAO,CAAE,CAAEM,QAAQ,CAAE,GAAI,CAC3B,CAAE,CACFE,SAAS,CAAC,gFAAgF,CAAAvB,QAAA,CAEzFnE,WAAW,CAACZ,KAAK,CAAC,CAACc,KAAK,CAACwG,QAAQ,CAAC,MAAM,CAAC,cACxChI,IAAA,UACEiI,GAAG,YAAAhB,MAAA,CAAa3F,WAAW,CAACZ,KAAK,CAAC,CAACc,KAAK,CAAG,CAC3C0G,QAAQ,MACRC,KAAK,MACLvG,IAAI,MACJwG,WAAW,MACXpB,SAAS,CAAC,cAAc,CACxBE,KAAK,CAAE,CACLmB,QAAQ,CAAE,wBAAwB,CAClClB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdkB,SAAS,CAAE,OAAO,CAClBxH,QAAQ,CAAE,UAAU,CACpBmF,GAAG,CAAE,KAAK,CACVF,IAAI,CAAE,KAAK,CACXwC,SAAS,UAAAtB,MAAA,CAAW3F,WAAW,CAACZ,KAAK,CAAC,CAACmB,KAAK,iBAAAoF,MAAA,CAAe3F,WAAW,CAACZ,KAAK,CAAC,CAACoB,UAAU,OAAAmF,MAAA,CAAK3F,WAAW,CAACZ,KAAK,CAAC,CAACqB,UAAU,KAAG,CAC7HyG,aAAa,CAAE,MAAM,CACrBC,UAAU,CAAE,MAAM,CAClB/B,UAAU,CAAE,sBACd,CAAE,CACFgC,OAAO,CAAGhB,CAAC,EAAK,CACd;AACAA,CAAC,CAACiB,MAAM,CAACzB,KAAK,CAAC0B,OAAO,CAAG,MAAM,CACjC,CAAE,CACH,CAAC,cAEF5I,IAAA,QACEiI,GAAG,YAAAhB,MAAA,CAAa3F,WAAW,CAACZ,KAAK,CAAC,CAACc,KAAK,CAAG,CAC3CqH,GAAG,CAAEnI,KAAM,CACXoI,SAAS,CAAE,KAAM,CACjB9B,SAAS,CAAC,cAAc,CACxBE,KAAK,CAAE,CACLmB,QAAQ,CAAE,wBAAwB,CAClClB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdkB,SAAS,CAAE,OAAO,CAClBS,cAAc,CAAE,MAAM,CACtBC,MAAM,CAAE,gCAAgC,CACxClI,QAAQ,CAAE,UAAU,CACpBmF,GAAG,CAAE,KAAK,CACVF,IAAI,CAAE,KAAK,CACXwC,SAAS,UAAAtB,MAAA,CAAW3F,WAAW,CAACZ,KAAK,CAAC,CAACmB,KAAK,iBAAAoF,MAAA,CAAe3F,WAAW,CAACZ,KAAK,CAAC,CAACoB,UAAU,OAAAmF,MAAA,CAAK3F,WAAW,CAACZ,KAAK,CAAC,CAACqB,UAAU,KAAG,CAC7H2E,UAAU,CAAE,sBAAsB,CAClC8B,aAAa,CAAE,MAAM,CACrBC,UAAU,CAAE,MACd,CAAE,CACFC,OAAO,CAAGhB,CAAC,EAAK,CACd;AACAA,CAAC,CAACiB,MAAM,CAACzB,KAAK,CAAC0B,OAAO,CAAG,MAAM,CAC/BlB,CAAC,CAACiB,MAAM,CAACM,aAAa,CAACC,SAAS,kKAAAjC,MAAA,CAEF5B,aAAa,CAAC,CAAC,4BAAA4B,MAAA,CAA0B5B,aAAa,CAAC,CAAC,8KAAA4B,MAAA,CAE3B5B,aAAa,CAAC,CAAC,oHAAA4B,MAAA,CACf5B,aAAa,CAAC,CAAC,qHAAA4B,MAAA,CAC9B5B,aAAa,CAAC,CAAC,sFAAA4B,MAAA,CACL5B,aAAa,CAAC,CAAC,iIAGpE,CACH,CAAE,CACH,CACF,EA1EI3E,KA2EK,CAAC,CACE,CAAC,cAGlBV,IAAA,CAACH,MAAM,CAAC6F,GAAG,EACTsB,SAAS,CAAC,4DAA4D,CACtEE,KAAK,CAAE,CACLiC,WAAW,CAAE9D,aAAa,CAAC,CAAC,CAC5B+D,SAAS,yCAA0C,CACnDxB,SAAS,aAAAX,MAAA,CAAc5B,aAAa,CAAC,CAAC,MACxC,CAAE,CACH,CAAC,EACQ,CAAC,cAGbjF,KAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAvB,QAAA,eAC1BzF,IAAA,QACEgH,SAAS,CAAC,yDAAyD,CACnEE,KAAK,CAAE,CACLvF,KAAK,CAAE0D,aAAa,CAAC,CACvB,CAAE,CAAAI,QAAA,CAEDF,YAAY,CAAC,CAAC,CACZ,CAAC,cACNvF,IAAA,QAAKgH,SAAS,CAAC,kDAAkD,CAAAvB,QAAA,CAAC,QAElE,CAAK,CAAC,cACNzF,IAAA,QAAKgH,SAAS,CAAC,uBAAuB,CAAAvB,QAAA,EAAAlF,mBAAA,CACnCe,WAAW,CAACZ,KAAK,CAAC,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAoBmB,WAAW,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,EAGI,CAAC,CACX,CAAC,CAEP,CAAC,CAED,cAAe,CAAArB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}