{"ast": null, "code": "import { Feature } from '../Feature.mjs';\nlet id = 0;\nclass ExitAnimationFeature extends Feature {\n  constructor() {\n    super(...arguments);\n    this.id = id++;\n  }\n  update() {\n    if (!this.node.presenceContext) return;\n    const {\n      isPresent,\n      onExitComplete,\n      custom\n    } = this.node.presenceContext;\n    const {\n      isPresent: prevIsPresent\n    } = this.node.prevPresenceContext || {};\n    if (!this.node.animationState || isPresent === prevIsPresent) {\n      return;\n    }\n    const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent, {\n      custom: custom !== null && custom !== void 0 ? custom : this.node.getProps().custom\n    });\n    if (onExitComplete && !isPresent) {\n      exitAnimation.then(() => onExitComplete(this.id));\n    }\n  }\n  mount() {\n    const {\n      register\n    } = this.node.presenceContext || {};\n    if (register) {\n      this.unmount = register(this.id);\n    }\n  }\n  unmount() {}\n}\nexport { ExitAnimationFeature };", "map": {"version": 3, "names": ["Feature", "id", "ExitAnimationFeature", "constructor", "arguments", "update", "node", "presenceContext", "isPresent", "onExitComplete", "custom", "prevIsPresent", "prevPresenceContext", "animationState", "exitAnimation", "setActive", "getProps", "then", "mount", "register", "unmount"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs"], "sourcesContent": ["import { Feature } from '../Feature.mjs';\n\nlet id = 0;\nclass ExitAnimationFeature extends Feature {\n    constructor() {\n        super(...arguments);\n        this.id = id++;\n    }\n    update() {\n        if (!this.node.presenceContext)\n            return;\n        const { isPresent, onExitComplete, custom } = this.node.presenceContext;\n        const { isPresent: prevIsPresent } = this.node.prevPresenceContext || {};\n        if (!this.node.animationState || isPresent === prevIsPresent) {\n            return;\n        }\n        const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent, { custom: custom !== null && custom !== void 0 ? custom : this.node.getProps().custom });\n        if (onExitComplete && !isPresent) {\n            exitAnimation.then(() => onExitComplete(this.id));\n        }\n    }\n    mount() {\n        const { register } = this.node.presenceContext || {};\n        if (register) {\n            this.unmount = register(this.id);\n        }\n    }\n    unmount() { }\n}\n\nexport { ExitAnimationFeature };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AAExC,IAAIC,EAAE,GAAG,CAAC;AACV,MAAMC,oBAAoB,SAASF,OAAO,CAAC;EACvCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACH,EAAE,GAAGA,EAAE,EAAE;EAClB;EACAI,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACC,IAAI,CAACC,eAAe,EAC1B;IACJ,MAAM;MAAEC,SAAS;MAAEC,cAAc;MAAEC;IAAO,CAAC,GAAG,IAAI,CAACJ,IAAI,CAACC,eAAe;IACvE,MAAM;MAAEC,SAAS,EAAEG;IAAc,CAAC,GAAG,IAAI,CAACL,IAAI,CAACM,mBAAmB,IAAI,CAAC,CAAC;IACxE,IAAI,CAAC,IAAI,CAACN,IAAI,CAACO,cAAc,IAAIL,SAAS,KAAKG,aAAa,EAAE;MAC1D;IACJ;IACA,MAAMG,aAAa,GAAG,IAAI,CAACR,IAAI,CAACO,cAAc,CAACE,SAAS,CAAC,MAAM,EAAE,CAACP,SAAS,EAAE;MAAEE,MAAM,EAAEA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,IAAI,CAACJ,IAAI,CAACU,QAAQ,CAAC,CAAC,CAACN;IAAO,CAAC,CAAC;IACrK,IAAID,cAAc,IAAI,CAACD,SAAS,EAAE;MAC9BM,aAAa,CAACG,IAAI,CAAC,MAAMR,cAAc,CAAC,IAAI,CAACR,EAAE,CAAC,CAAC;IACrD;EACJ;EACAiB,KAAKA,CAAA,EAAG;IACJ,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACb,IAAI,CAACC,eAAe,IAAI,CAAC,CAAC;IACpD,IAAIY,QAAQ,EAAE;MACV,IAAI,CAACC,OAAO,GAAGD,QAAQ,CAAC,IAAI,CAAClB,EAAE,CAAC;IACpC;EACJ;EACAmB,OAAOA,CAAA,EAAG,CAAE;AAChB;AAEA,SAASlB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}