{"ast": null, "code": "import { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst svgMotionConfig = {\n  useVisualState: makeUseVisualState({\n    scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n    createRenderState: createSvgRenderState,\n    onMount: (props, instance, {\n      renderState,\n      latestValues\n    }) => {\n      frame.read(() => {\n        try {\n          renderState.dimensions = typeof instance.getBBox === \"function\" ? instance.getBBox() : instance.getBoundingClientRect();\n        } catch (e) {\n          // Most likely trying to measure an unrendered element under Firefox\n          renderState.dimensions = {\n            x: 0,\n            y: 0,\n            width: 0,\n            height: 0\n          };\n        }\n      });\n      frame.render(() => {\n        buildSVGAttrs(renderState, latestValues, {\n          enableHardwareAcceleration: false\n        }, isSVGTag(instance.tagName), props.transformTemplate);\n        renderSVG(instance, renderState);\n      });\n    }\n  })\n};\nexport { svgMotionConfig };", "map": {"version": 3, "names": ["renderSVG", "scrapeMotionValuesFromProps", "makeUseVisualState", "createSvgRenderState", "buildSVGAttrs", "isSVGTag", "frame", "svgMotionConfig", "useVisualState", "createRenderState", "onMount", "props", "instance", "renderState", "latestValues", "read", "dimensions", "getBBox", "getBoundingClientRect", "e", "x", "y", "width", "height", "render", "enableHardwareAcceleration", "tagName", "transformTemplate"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs"], "sourcesContent": ["import { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst svgMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n        createRenderState: createSvgRenderState,\n        onMount: (props, instance, { renderState, latestValues }) => {\n            frame.read(() => {\n                try {\n                    renderState.dimensions =\n                        typeof instance.getBBox ===\n                            \"function\"\n                            ? instance.getBBox()\n                            : instance.getBoundingClientRect();\n                }\n                catch (e) {\n                    // Most likely trying to measure an unrendered element under Firefox\n                    renderState.dimensions = {\n                        x: 0,\n                        y: 0,\n                        width: 0,\n                        height: 0,\n                    };\n                }\n            });\n            frame.render(() => {\n                buildSVGAttrs(renderState, latestValues, { enableHardwareAcceleration: false }, isSVGTag(instance.tagName), props.transformTemplate);\n                renderSVG(instance, renderState);\n            });\n        },\n    }),\n};\n\nexport { svgMotionConfig };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,eAAe,GAAG;EACpBC,cAAc,EAAEN,kBAAkB,CAAC;IAC/BD,2BAA2B,EAAEA,2BAA2B;IACxDQ,iBAAiB,EAAEN,oBAAoB;IACvCO,OAAO,EAAEA,CAACC,KAAK,EAAEC,QAAQ,EAAE;MAAEC,WAAW;MAAEC;IAAa,CAAC,KAAK;MACzDR,KAAK,CAACS,IAAI,CAAC,MAAM;QACb,IAAI;UACAF,WAAW,CAACG,UAAU,GAClB,OAAOJ,QAAQ,CAACK,OAAO,KACnB,UAAU,GACRL,QAAQ,CAACK,OAAO,CAAC,CAAC,GAClBL,QAAQ,CAACM,qBAAqB,CAAC,CAAC;QAC9C,CAAC,CACD,OAAOC,CAAC,EAAE;UACN;UACAN,WAAW,CAACG,UAAU,GAAG;YACrBI,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE;UACZ,CAAC;QACL;MACJ,CAAC,CAAC;MACFjB,KAAK,CAACkB,MAAM,CAAC,MAAM;QACfpB,aAAa,CAACS,WAAW,EAAEC,YAAY,EAAE;UAAEW,0BAA0B,EAAE;QAAM,CAAC,EAAEpB,QAAQ,CAACO,QAAQ,CAACc,OAAO,CAAC,EAAEf,KAAK,CAACgB,iBAAiB,CAAC;QACpI3B,SAAS,CAACY,QAAQ,EAAEC,WAAW,CAAC;MACpC,CAAC,CAAC;IACN;EACJ,CAAC;AACL,CAAC;AAED,SAASN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}