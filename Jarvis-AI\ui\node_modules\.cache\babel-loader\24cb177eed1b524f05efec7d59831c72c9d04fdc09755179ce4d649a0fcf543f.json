{"ast": null, "code": "import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\nconst hsla = {\n  test: isColorString(\"hsl\", \"hue\"),\n  parse: splitColor(\"hue\", \"saturation\", \"lightness\"),\n  transform: ({\n    hue,\n    saturation,\n    lightness,\n    alpha: alpha$1 = 1\n  }) => {\n    return \"hsla(\" + Math.round(hue) + \", \" + percent.transform(sanitize(saturation)) + \", \" + percent.transform(sanitize(lightness)) + \", \" + sanitize(alpha.transform(alpha$1)) + \")\";\n  }\n};\nexport { hsla };", "map": {"version": 3, "names": ["alpha", "percent", "sanitize", "isColorString", "splitColor", "hsla", "test", "parse", "transform", "hue", "saturation", "lightness", "alpha$1", "Math", "round"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/value/types/color/hsla.mjs"], "sourcesContent": ["import { alpha } from '../numbers/index.mjs';\nimport { percent } from '../numbers/units.mjs';\nimport { sanitize } from '../utils.mjs';\nimport { isColorString, splitColor } from './utils.mjs';\n\nconst hsla = {\n    test: isColorString(\"hsl\", \"hue\"),\n    parse: splitColor(\"hue\", \"saturation\", \"lightness\"),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return (\"hsla(\" +\n            Math.round(hue) +\n            \", \" +\n            percent.transform(sanitize(saturation)) +\n            \", \" +\n            percent.transform(sanitize(lightness)) +\n            \", \" +\n            sanitize(alpha.transform(alpha$1)) +\n            \")\");\n    },\n};\n\nexport { hsla };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,aAAa,EAAEC,UAAU,QAAQ,aAAa;AAEvD,MAAMC,IAAI,GAAG;EACTC,IAAI,EAAEH,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC;EACjCI,KAAK,EAAEH,UAAU,CAAC,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC;EACnDI,SAAS,EAAEA,CAAC;IAAEC,GAAG;IAAEC,UAAU;IAAEC,SAAS;IAAEX,KAAK,EAAEY,OAAO,GAAG;EAAE,CAAC,KAAK;IAC/D,OAAQ,OAAO,GACXC,IAAI,CAACC,KAAK,CAACL,GAAG,CAAC,GACf,IAAI,GACJR,OAAO,CAACO,SAAS,CAACN,QAAQ,CAACQ,UAAU,CAAC,CAAC,GACvC,IAAI,GACJT,OAAO,CAACO,SAAS,CAACN,QAAQ,CAACS,SAAS,CAAC,CAAC,GACtC,IAAI,GACJT,QAAQ,CAACF,KAAK,CAACQ,SAAS,CAACI,OAAO,CAAC,CAAC,GAClC,GAAG;EACX;AACJ,CAAC;AAED,SAASP,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}