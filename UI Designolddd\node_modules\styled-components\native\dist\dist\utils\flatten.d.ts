import StyleSheet from '../sheet';
import { Dict, ExecutionContext, Interpolation, RuleSet, Stringifier } from '../types';
export declare const objToCssArray: (obj: Dict<any>) => string[];
export default function flatten<Props extends object>(chunk: Interpolation<object>, executionContext?: (ExecutionContext & Props) | undefined, styleSheet?: StyleSheet | undefined, stylisInstance?: Stringifier | undefined): RuleSet<Props>;
