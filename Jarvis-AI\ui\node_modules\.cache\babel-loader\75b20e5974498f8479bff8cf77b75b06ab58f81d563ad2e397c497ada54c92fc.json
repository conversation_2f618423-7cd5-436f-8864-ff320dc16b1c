{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnimatedButton=_ref=>{let{children,onClick,variant='primary',size='medium',disabled=false,loading=false,className=''}=_ref;const variants={primary:'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',secondary:'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800',success:'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700',danger:'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700'};const sizes={small:'px-4 py-2 text-sm',medium:'px-6 py-3 text-base',large:'px-8 py-4 text-lg'};return/*#__PURE__*/_jsx(\"button\",{onClick:onClick,disabled:disabled||loading,className:\"\\n        \".concat(variants[variant],\"\\n        \").concat(sizes[size],\"\\n        text-white font-semibold rounded-lg\\n        transform transition-all duration-200\\n        hover:scale-105 hover:shadow-lg\\n        active:scale-95\\n        disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\\n        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\\n        \").concat(className,\"\\n      \"),children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Loading...\"})]}):children});};export default AnimatedButton;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "AnimatedButton", "_ref", "children", "onClick", "variant", "size", "disabled", "loading", "className", "variants", "primary", "secondary", "success", "danger", "sizes", "small", "medium", "large", "concat"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ui/AnimatedButton.js"], "sourcesContent": ["import React from 'react';\n\nconst AnimatedButton = ({ \n  children, \n  onClick, \n  variant = 'primary', \n  size = 'medium',\n  disabled = false,\n  loading = false,\n  className = ''\n}) => {\n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',\n    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800',\n    success: 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700',\n    danger: 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700'\n  };\n\n  const sizes = {\n    small: 'px-4 py-2 text-sm',\n    medium: 'px-6 py-3 text-base',\n    large: 'px-8 py-4 text-lg'\n  };\n\n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled || loading}\n      className={`\n        ${variants[variant]}\n        ${sizes[size]}\n        text-white font-semibold rounded-lg\n        transform transition-all duration-200\n        hover:scale-105 hover:shadow-lg\n        active:scale-95\n        disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\n        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\n        ${className}\n      `}\n    >\n      {loading ? (\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n          <span>Loading...</span>\n        </div>\n      ) : (\n        children\n      )}\n    </button>\n  );\n};\n\nexport default AnimatedButton;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAQjB,IARkB,CACtBC,QAAQ,CACRC,OAAO,CACPC,OAAO,CAAG,SAAS,CACnBC,IAAI,CAAG,QAAQ,CACfC,QAAQ,CAAG,KAAK,CAChBC,OAAO,CAAG,KAAK,CACfC,SAAS,CAAG,EACd,CAAC,CAAAP,IAAA,CACC,KAAM,CAAAQ,QAAQ,CAAG,CACfC,OAAO,CAAE,sFAAsF,CAC/FC,SAAS,CAAE,kFAAkF,CAC7FC,OAAO,CAAE,0FAA0F,CACnGC,MAAM,CAAE,gFACV,CAAC,CAED,KAAM,CAAAC,KAAK,CAAG,CACZC,KAAK,CAAE,mBAAmB,CAC1BC,MAAM,CAAE,qBAAqB,CAC7BC,KAAK,CAAE,mBACT,CAAC,CAED,mBACEpB,IAAA,WACEM,OAAO,CAAEA,OAAQ,CACjBG,QAAQ,CAAEA,QAAQ,EAAIC,OAAQ,CAC9BC,SAAS,cAAAU,MAAA,CACLT,QAAQ,CAACL,OAAO,CAAC,eAAAc,MAAA,CACjBJ,KAAK,CAACT,IAAI,CAAC,iVAAAa,MAAA,CAOXV,SAAS,YACX,CAAAN,QAAA,CAEDK,OAAO,cACNR,KAAA,QAAKS,SAAS,CAAC,6BAA6B,CAAAN,QAAA,eAC1CL,IAAA,QAAKW,SAAS,CAAC,8EAA8E,CAAM,CAAC,cACpGX,IAAA,SAAAK,QAAA,CAAM,YAAU,CAAM,CAAC,EACpB,CAAC,CAENA,QACD,CACK,CAAC,CAEb,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}