{"ast": null, "code": "import { frame, cancelFrame } from '../frameloop/frame.mjs';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n  const start = performance.now();\n  const checkElapsed = _ref => {\n    let {\n      timestamp\n    } = _ref;\n    const elapsed = timestamp - start;\n    if (elapsed >= timeout) {\n      cancelFrame(checkElapsed);\n      callback(elapsed - timeout);\n    }\n  };\n  frame.read(checkElapsed, true);\n  return () => cancelFrame(checkElapsed);\n}\nexport { delay };", "map": {"version": 3, "names": ["frame", "cancelFrame", "delay", "callback", "timeout", "start", "performance", "now", "checkElapsed", "_ref", "timestamp", "elapsed", "read"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/utils/delay.mjs"], "sourcesContent": ["import { frame, cancelFrame } from '../frameloop/frame.mjs';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = performance.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            cancelFrame(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    frame.read(checkElapsed, true);\n    return () => cancelFrame(checkElapsed);\n}\n\nexport { delay };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;;AAE3D;AACA;AACA;AACA,SAASC,KAAKA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC9B,MAAMC,KAAK,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;EAC/B,MAAMC,YAAY,GAAGC,IAAA,IAAmB;IAAA,IAAlB;MAAEC;IAAU,CAAC,GAAAD,IAAA;IAC/B,MAAME,OAAO,GAAGD,SAAS,GAAGL,KAAK;IACjC,IAAIM,OAAO,IAAIP,OAAO,EAAE;MACpBH,WAAW,CAACO,YAAY,CAAC;MACzBL,QAAQ,CAACQ,OAAO,GAAGP,OAAO,CAAC;IAC/B;EACJ,CAAC;EACDJ,KAAK,CAACY,IAAI,CAACJ,YAAY,EAAE,IAAI,CAAC;EAC9B,OAAO,MAAMP,WAAW,CAACO,YAAY,CAAC;AAC1C;AAEA,SAASN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}