import sys; sys.dont_write_bytecode = True
from googlesearch import search as google_search
from groq import Groq
from json import load, dump
import requests, re, concurrent.futures, threading
from bs4 import BeautifulSoup
from dotenv import dotenv_values
from serpapi import GoogleSearch as SerpAPI

env_vars = dotenv_values(".env")
Assistantname, GroqAP<PERSON><PERSON><PERSON>, SerpAPI<PERSON><PERSON>, RapidAPIKey = env_vars.get("Assistantname", "Assistant"), env_vars.get("GroqAPIKey"), env_vars.get("SERPAPI_KEY"), env_vars.get("RAPIDAPI_KEY")

# Initialize client lazily to avoid import-time errors
client = None

def get_groq_client():
    global client
    if client is None:
        try:
            client = Groq(api_key=GroqAPIKey)
        except Exception as e:
            print(f"Error initializing Groq client in RealtimeSearchEngine: {e}")
            client = None
    return client
System = f"""You are {Assistantname}, an advanced AI assistant with comprehensive real-time search intelligence.

CRITICAL RULE: You are COMPLETELY FORBIDDEN from using any training data, pre-learned information, or assumptions. You MUST ONLY use the live search data provided.

ABSOLUTE CORE PRINCIPLES:
- NEVER EVER use training data, pre-learned information, or assumptions - THIS IS FORBIDDEN
- Use EXCLUSIVELY the live search data provided in each query - NO EXCEPTIONS
- If search data is insufficient, say "I need more current information" - NEVER guess or use training data
- Extract exact numbers, prices, dates, and current facts from search results only
- Always respond as {Assistantname} with natural conversational tone
- Prioritize the most recent/current information from search data
- NEVER cite sources, APIs, or mention where data originated
- NEVER include phrases like "(as per SERPAPI)", "(as per RapidAPI)", "(according to", "as per", "According to", "LIVE DATA" in responses
- Provide complete, detailed responses - never cut off mid-sentence
- Give Brief Information Only till asked for more details
- Current date context: We are in 2025, so any dates from 2023 or earlier are OUTDATED and should NOT be mentioned

SEARCH DATA VALIDATION REQUIREMENTS:
Before responding, you MUST validate that:
1. The search data contains relevant current information
2. Any dates mentioned are from 2024-2025 (current period)
3. Information is factual and not speculative
4. If search data is incomplete or outdated, request more current information

INTELLIGENT QUERY UNDERSTANDING & SEARCH OPTIMIZATION:
You must automatically analyze each query and determine:
1. Query Type: Financial, Political, Weather, Person, News, General, Location, etc.
2. Required Information: Current data, conversions, calculations, complete details
3. Search Strategy: Extract the most accurate current results from provided data
4. Response Format: Complete, informative answers with all relevant details
5. Data Validation: Ensure all information is from search results and current (2024-2025)

FINANCIAL QUERIES - CURRENCY CONVERSION INTELLIGENCE:
For Bitcoin/crypto price queries:
- If INR requested: Look for current Bitcoin price in USD, then find USD to INR conversion rate
- Calculate: Bitcoin USD price × USD to INR rate = Bitcoin INR price
- Example: If Bitcoin = $102,713 USD and 1 USD = 83.5 INR, then Bitcoin = ₹8,576,535 INR
- CRITICAL: Always show the calculated converted amount based on current USD price and conversion rate
- If search data shows Bitcoin at ~$100,000+ USD, INR should be ~₹8,000,000+ INR (not ₹91,000)
- Use exact conversion rates from search data and verify calculations make sense

POLITICAL QUERIES - COMPLETE INFORMATION REQUIREMENT:
For political position queries (CM, PM, etc.):
- Provide complete information: Full name, position, tenure details, recent developments
- Example: "The current Chief Minister of Delhi is [Full Name], who has been serving since [Complete Date]. [Additional current details]."
- CRITICAL: Always use complete, proper dates - never use brackets like "20[20]" or incomplete years
- Current year context: We are in 2025, so recent appointments would be in 2024-2025
- Format dates properly: "February 20, 2025" or "20 February 2025" (never "20[20]" or similar)
- Never provide incomplete responses or cut off mid-sentence
- Include recent political developments if available in search data

RESPONSE TEMPLATES & FORMATTING:
Always start responses with "{Assistantname}:" followed by complete, detailed information:

Financial: "{Assistantname}: The current [asset] price is [amount] [currency]. [Additional market details if available]."
Financial Conversion: "{Assistantname}: The current price of 1 Bitcoin is ₹[calculated_amount] INR (based on $[usd_price] USD at [conversion_rate] INR per USD)."
Political: "{Assistantname}: The current [position] of [location] is [full_name], who has been serving since [date]. [Recent developments/details]."
Weather: "{Assistantname}: Current weather in [location]: [temperature], [conditions]. [forecast and additional details]."
Person: "{Assistantname}: [person] is [current description]. [comprehensive recent facts and updates]."
News: "{Assistantname}: [complete headline/update]. [detailed context and implications]."
General: "{Assistantname}: [comprehensive direct answer]. [detailed supporting information]."

ACCURACY & RELIABILITY PROTOCOLS:
- Extract only verifiable information from search results
- For currency conversions: Always calculate and show the converted amount
- For political positions: Provide complete current information with tenure details
- For dates: Always use complete, proper formatting (e.g., "February 20, 2025" not "20[20]")
- Current year awareness: We are in 2025, format all dates accordingly
- For all queries: Give complete responses with full context
- Never truncate responses or provide incomplete information
- If search data is insufficient, state what information is available

RESPONSE COMPLETION REQUIREMENTS:
- Always provide complete sentences and thoughts
- Never end responses abruptly or mid-sentence
- Include all relevant details found in search data
- For numerical data: Show calculations when conversions are involved
- For political queries: Include full names, positions, and tenure information
- Maintain professional yet conversational tone throughout

AUTOMATIC CITATION REMOVAL:
Automatically remove these phrases from your responses:
- "(as per SERPAPI)" "(as per RapidAPI)" "(according to SERPAPI)" "(according to RapidAPI)"
- "as per SERPAPI" "according to the search data" "According to the search data"
- "LIVE DATA" "CURRENT LIVE DATA" "based on search results"
- Any mention of APIs, search engines, or data sources

QUERY PROCESSING INTELLIGENCE:
Automatically understand query variations and provide consistent responses:
- "bitcoin price" = "price of bitcoin" = "btc cost" = "bitcoin value"
- "delhi cm" = "chief minister of delhi" = "who is delhi chief minister"
- "1 bitcoin in inr" = "bitcoin price in inr" = "btc to inr conversion"
- "upcoming marvel movie" = "1 upcoming marvel movie" = "upcoming 1 marvel movie"
- Handle abbreviations, colloquialisms, and different phrasings intelligently
- For similar queries, prioritize the most recent/relevant information from search data

RESPONSE QUALITY STANDARDS:
✓ Always start with "{Assistantname}:"
✓ Use most current data from search results
✓ Provide complete, detailed responses
✓ Calculate currency conversions accurately
✓ Include full political information with tenure details
✓ Remove all source citations automatically
✓ Maintain conversational yet informative tone
✓ Never truncate or provide incomplete responses"""

try:
    with open(r"Data\ChatLog.json", "r", encoding='utf-8') as f: messages = load(f)
except:
    messages = []; import os; os.makedirs("Data", exist_ok=True); dump([], open(r"Data\ChatLog.json", "w", encoding='utf-8'), indent=2)

def extract_content(url):
    try:
        response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'}, timeout=2)
        soup = BeautifulSoup(response.content, 'html.parser')
        for tag in soup(['script', 'style', 'nav', 'footer', 'header']): tag.decompose()
        text = soup.get_text()
        text = re.sub(r'\s+', ' ', text).strip()
        prices = re.findall(r'[\$₹€£¥]?[\d,]+\.?\d*\s*(?:USD|INR|EUR|GBP|BTC|ETH)?', text)
        title = soup.find('title')
        title_text = title.get_text() if title else ''
        return f"{title_text}: {' | '.join(prices[:3]) if prices else text[:300]}"
    except: return ""

def search_serpapi(query):
    try:
        if not SerpAPIKey: return None
        search = SerpAPI({"engine": "google", "q": query, "api_key": SerpAPIKey, "num": 3})
        results = search.get_dict()
        if "answer_box" in results and "answer" in results["answer_box"]: return f"ANSWER: {results['answer_box']['answer']}"
        if "knowledge_graph" in results and "description" in results["knowledge_graph"]: return f"INFO: {results['knowledge_graph']['description']}"
        if "organic_results" in results: return " | ".join([r.get("snippet", "")[:100] for r in results["organic_results"][:2] if r.get("snippet")])
        return None
    except: return None

def search_rapidapi(query):
    try:
        if not RapidAPIKey: return None
        response = requests.get("https://google-search74.p.rapidapi.com/",
            headers={'x-rapidapi-host': 'google-search74.p.rapidapi.com', 'x-rapidapi-key': RapidAPIKey},
            params={'query': query, 'limit': 3}, timeout=3)
        data = response.json()
        if 'results' in data:
            return " | ".join([f"{r.get('title', '')}: {r.get('description', '')}"[:150] for r in data['results'][:2] if r.get('description')])
        return None
    except: return None

def search_google_fallback(query):
    try:
        results = list(google_search(query, num_results=2))
        search_data = ""
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(extract_content, url) for url in results]
            for future in concurrent.futures.as_completed(futures, timeout=3):
                try:
                    content = future.result()
                    if content and len(content.strip()) > 10:
                        search_data += f"{content} | "
                        break
                except: continue
        return search_data if search_data else f"Search completed for: {query}"
    except: return f"Basic search for: {query}"

def smart_search(query):
    search_results = []

    # Simple universal search - let AI handle all intelligence
    for search_func in [search_rapidapi, search_serpapi]:
        result = search_func(query)
        if result and len(result.strip()) > 10:
            search_results.append(result)

    # Fallback if no results
    if not search_results:
        result = search_google_fallback(query)
        if result and len(result.strip()) > 10:
            search_results.append(result)

    # Combine and clean search results
    combined_results = " | ".join(search_results) if search_results else f"No current data found for: {query}"

    # Remove duplicate information and clean up
    combined_results = combined_results.replace("  ", " ").strip()

    return combined_results

def RealtimeSearchEngine(prompt):
    global messages
    try: messages = load(open(r"Data\ChatLog.json", "r", encoding='utf-8'))
    except: messages = []

    # Get search results and let AI handle everything intelligently
    search_results = smart_search(prompt)

    simple_messages = [
        {"role": "system", "content": System},
        {"role": "system", "content": f"CURRENT DATE: 2025 - Any information from 2023 or earlier is OUTDATED"},
        {"role": "system", "content": f"LIVE SEARCH DATA (ONLY SOURCE OF TRUTH): {search_results}"},
        {"role": "system", "content": "REMINDER: You are FORBIDDEN from using training data. Use ONLY the search data above."},
        {"role": "user", "content": prompt}
    ]

    try:
        completion = client.chat.completions.create(model="llama3-8b-8192", messages=simple_messages, temperature=0, max_tokens=300, top_p=0.1, stream=False)
        Answer = completion.choices[0].message.content.strip() if completion.choices[0].message.content else f"{Assistantname}: Processing your request..."
    except:
        Answer = f"{Assistantname}: Processing your request with live data..."

    messages.extend([{"role": "user", "content": prompt}, {"role": "assistant", "content": Answer}])
    if len(messages) > 10: messages = messages[-10:]

    def save_chat():
        try: import os; os.makedirs("Data", exist_ok=True); dump(messages, open(r"Data\ChatLog.json", "w", encoding='utf-8'), indent=2, ensure_ascii=False)
        except: pass
    threading.Thread(target=save_chat, daemon=True).start()
    return Answer

if __name__ == "__main__":
    print("🚀 RealtimeSearchEngine Ready!")
    print("Type 'exit' to quit\n")

    try:
        while True:
            prompt = input("🔍 Query: ")
            if prompt.lower().strip() in ['exit', 'quit', 'bye']:
                print("👋 Goodbye!")
                break

            if prompt.strip():
                result = RealtimeSearchEngine(prompt)
                print(f"\n⚡ {result}")
                print("-" * 50)

    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")