{"ast": null, "code": "import { scrollInfo } from './track.mjs';\nimport { observeTimeline } from './observe.mjs';\nimport { supportsScrollTimeline } from './supports.mjs';\nfunction scrollTimelineFallback(_ref) {\n  let {\n    source,\n    axis = \"y\"\n  } = _ref;\n  // ScrollTimeline records progress as a percentage CSSUnitValue\n  const currentTime = {\n    value: 0\n  };\n  const cancel = scrollInfo(info => {\n    currentTime.value = info[axis].progress * 100;\n  }, {\n    container: source,\n    axis\n  });\n  return {\n    currentTime,\n    cancel\n  };\n}\nconst timelineCache = new Map();\nfunction getTimeline() {\n  let {\n    source = document.documentElement,\n    axis = \"y\"\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  if (!timelineCache.has(source)) {\n    timelineCache.set(source, {});\n  }\n  const elementCache = timelineCache.get(source);\n  if (!elementCache[axis]) {\n    elementCache[axis] = supportsScrollTimeline() ? new ScrollTimeline({\n      source,\n      axis\n    }) : scrollTimelineFallback({\n      source,\n      axis\n    });\n  }\n  return elementCache[axis];\n}\nfunction scroll(onScroll, options) {\n  const timeline = getTimeline(options);\n  if (typeof onScroll === \"function\") {\n    return observeTimeline(onScroll, timeline);\n  } else {\n    return onScroll.attachTimeline(timeline);\n  }\n}\nexport { scroll };", "map": {"version": 3, "names": ["scrollInfo", "observeTimeline", "supportsScrollTimeline", "scrollTimelineFallback", "_ref", "source", "axis", "currentTime", "value", "cancel", "info", "progress", "container", "timelineCache", "Map", "getTimeline", "document", "documentElement", "arguments", "length", "undefined", "has", "set", "elementCache", "get", "ScrollTimeline", "scroll", "onScroll", "options", "timeline", "attachTimeline"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs"], "sourcesContent": ["import { scrollInfo } from './track.mjs';\nimport { observeTimeline } from './observe.mjs';\nimport { supportsScrollTimeline } from './supports.mjs';\n\nfunction scrollTimelineFallback({ source, axis = \"y\" }) {\n    // ScrollTimeline records progress as a percentage CSSUnitValue\n    const currentTime = { value: 0 };\n    const cancel = scrollInfo((info) => {\n        currentTime.value = info[axis].progress * 100;\n    }, { container: source, axis });\n    return { currentTime, cancel };\n}\nconst timelineCache = new Map();\nfunction getTimeline({ source = document.documentElement, axis = \"y\", } = {}) {\n    if (!timelineCache.has(source)) {\n        timelineCache.set(source, {});\n    }\n    const elementCache = timelineCache.get(source);\n    if (!elementCache[axis]) {\n        elementCache[axis] = supportsScrollTimeline()\n            ? new ScrollTimeline({ source, axis })\n            : scrollTimelineFallback({ source, axis });\n    }\n    return elementCache[axis];\n}\nfunction scroll(onScroll, options) {\n    const timeline = getTimeline(options);\n    if (typeof onScroll === \"function\") {\n        return observeTimeline(onScroll, timeline);\n    }\n    else {\n        return onScroll.attachTimeline(timeline);\n    }\n}\n\nexport { scroll };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,SAASC,eAAe,QAAQ,eAAe;AAC/C,SAASC,sBAAsB,QAAQ,gBAAgB;AAEvD,SAASC,sBAAsBA,CAAAC,IAAA,EAAyB;EAAA,IAAxB;IAAEC,MAAM;IAAEC,IAAI,GAAG;EAAI,CAAC,GAAAF,IAAA;EAClD;EACA,MAAMG,WAAW,GAAG;IAAEC,KAAK,EAAE;EAAE,CAAC;EAChC,MAAMC,MAAM,GAAGT,UAAU,CAAEU,IAAI,IAAK;IAChCH,WAAW,CAACC,KAAK,GAAGE,IAAI,CAACJ,IAAI,CAAC,CAACK,QAAQ,GAAG,GAAG;EACjD,CAAC,EAAE;IAAEC,SAAS,EAAEP,MAAM;IAAEC;EAAK,CAAC,CAAC;EAC/B,OAAO;IAAEC,WAAW;IAAEE;EAAO,CAAC;AAClC;AACA,MAAMI,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,SAASC,WAAWA,CAAA,EAA0D;EAAA,IAAzD;IAAEV,MAAM,GAAGW,QAAQ,CAACC,eAAe;IAAEX,IAAI,GAAG;EAAK,CAAC,GAAAY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACxE,IAAI,CAACL,aAAa,CAACQ,GAAG,CAAChB,MAAM,CAAC,EAAE;IAC5BQ,aAAa,CAACS,GAAG,CAACjB,MAAM,EAAE,CAAC,CAAC,CAAC;EACjC;EACA,MAAMkB,YAAY,GAAGV,aAAa,CAACW,GAAG,CAACnB,MAAM,CAAC;EAC9C,IAAI,CAACkB,YAAY,CAACjB,IAAI,CAAC,EAAE;IACrBiB,YAAY,CAACjB,IAAI,CAAC,GAAGJ,sBAAsB,CAAC,CAAC,GACvC,IAAIuB,cAAc,CAAC;MAAEpB,MAAM;MAAEC;IAAK,CAAC,CAAC,GACpCH,sBAAsB,CAAC;MAAEE,MAAM;MAAEC;IAAK,CAAC,CAAC;EAClD;EACA,OAAOiB,YAAY,CAACjB,IAAI,CAAC;AAC7B;AACA,SAASoB,MAAMA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC/B,MAAMC,QAAQ,GAAGd,WAAW,CAACa,OAAO,CAAC;EACrC,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;IAChC,OAAO1B,eAAe,CAAC0B,QAAQ,EAAEE,QAAQ,CAAC;EAC9C,CAAC,MACI;IACD,OAAOF,QAAQ,CAACG,cAAc,CAACD,QAAQ,CAAC;EAC5C;AACJ;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}