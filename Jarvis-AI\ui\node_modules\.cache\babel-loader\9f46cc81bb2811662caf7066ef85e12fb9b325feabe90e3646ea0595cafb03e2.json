{"ast": null, "code": "/**\n * use<PERSON><PERSON><PERSON> Hook\n * Custom React hook for managing Jarvis state and API interactions\n */import{useState,useEffect,useCallback,useRef}from'react';import jarvisApi from'../services/jarvisApi';export const useJarvis=()=>{// State management\nconst[state,setState]=useState('startup');const[isProcessing,setIsProcessing]=useState(false);const[isServerConnected,setIsServerConnected]=useState(false);const[lastResponse,setLastResponse]=useState('');const[conversationHistory,setConversationHistory]=useState([]);const[error,setError]=useState(null);// Refs for cleanup\nconst pollingIntervalRef=useRef(null);const mountedRef=useRef(true);/**\n   * Check server connection\n   */const checkServerConnection=useCallback(async()=>{try{const isRunning=await jarvisApi.isServerRunning();if(mountedRef.current){setIsServerConnected(isRunning);if(isRunning){setError(null);}}return isRunning;}catch(err){if(mountedRef.current){setIsServerConnected(false);setError('Failed to connect to Jarvis backend');}return false;}},[]);/**\n   * Update Jarvis state\n   */const updateState=useCallback(async newState=>{try{await jarvisApi.setState(newState);if(mountedRef.current){setState(newState);setError(null);}}catch(err){console.error('Failed to update state:',err);if(mountedRef.current){setError(\"Failed to update state: \".concat(err.message));}}},[]);/**\n   * Send message to Jarvis\n   */const sendMessage=useCallback(async message=>{if(!isServerConnected){throw new Error('Not connected to Jarvis backend');}try{setIsProcessing(true);setError(null);const response=await jarvisApi.sendMessage(message);if(mountedRef.current){setLastResponse(response.response);setState(response.current_state||'rest');// Update conversation history\nsetConversationHistory(prev=>[...prev,{id:Date.now(),user:message,jarvis:response.response,timestamp:response.timestamp}]);// Automatically speak the response\nif(response.response){try{await jarvisApi.textToSpeech(response.response);}catch(speechError){console.warn('Text-to-speech failed:',speechError);// Don't throw error for TTS failure, just log it\n}}}return response;}catch(err){console.error('Failed to send message:',err);if(mountedRef.current){setError(\"Failed to send message: \".concat(err.message));}throw err;}finally{if(mountedRef.current){setIsProcessing(false);}}},[isServerConnected]);/**\n   * Start speech recognition\n   */const startSpeechRecognition=useCallback(async()=>{if(!isServerConnected){throw new Error('Not connected to Jarvis backend');}try{setIsProcessing(true);setError(null);const response=await jarvisApi.startSpeechRecognition();if(mountedRef.current&&response.text){// Automatically send the recognized text as a message\n// The sendMessage function will automatically speak the response\nreturn await sendMessage(response.text);}return response;}catch(err){console.error('Speech recognition failed:',err);if(mountedRef.current){setError(\"Speech recognition failed: \".concat(err.message));}throw err;}finally{if(mountedRef.current){setIsProcessing(false);}}},[isServerConnected,sendMessage]);/**\n   * Convert text to speech\n   */const speakText=useCallback(async text=>{if(!isServerConnected){throw new Error('Not connected to Jarvis backend');}try{setError(null);const response=await jarvisApi.textToSpeech(text);return response;}catch(err){console.error('Text-to-speech failed:',err);if(mountedRef.current){setError(\"Text-to-speech failed: \".concat(err.message));}throw err;}},[isServerConnected]);/**\n   * Clear conversation history\n   */const clearHistory=useCallback(async()=>{try{await jarvisApi.clearConversationHistory();if(mountedRef.current){setConversationHistory([]);setError(null);}}catch(err){console.error('Failed to clear history:',err);if(mountedRef.current){setError(\"Failed to clear history: \".concat(err.message));}}},[]);/**\n   * Initialize connection and start polling\n   */useEffect(()=>{let mounted=true;mountedRef.current=true;const initialize=async()=>{// Check initial connection\nawait checkServerConnection();// Start polling for state updates\nif(mounted){pollingIntervalRef.current=await jarvisApi.pollState(stateData=>{if(mountedRef.current){setState(stateData.current_state);setIsProcessing(stateData.is_processing);setLastResponse(stateData.last_response);}},2000// Poll every 2 seconds\n);}};initialize();// Cleanup function\nreturn()=>{mounted=false;mountedRef.current=false;if(pollingIntervalRef.current){jarvisApi.stopPolling(pollingIntervalRef.current);}};},[checkServerConnection]);/**\n   * Retry connection\n   */const retryConnection=useCallback(async()=>{setError(null);return await checkServerConnection();},[checkServerConnection]);return{// State\nstate,isProcessing,isServerConnected,lastResponse,conversationHistory,error,// Actions\nupdateState,sendMessage,startSpeechRecognition,speakText,clearHistory,retryConnection};};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "state", "setState", "isProcessing", "setIsProcessing", "isServerConnected", "setIsServerConnected", "lastResponse", "setLastResponse", "conversationHistory", "setConversationHistory", "error", "setError", "pollingIntervalRef", "mountedRef", "checkServerConnection", "isRunning", "isServerRunning", "current", "err", "updateState", "newState", "console", "concat", "message", "sendMessage", "Error", "response", "current_state", "prev", "id", "Date", "now", "user", "jarvis", "timestamp", "textToSpeech", "speechError", "warn", "startSpeechRecognition", "text", "speakText", "clearHistory", "clearConversationHistory", "mounted", "initialize", "pollState", "stateData", "is_processing", "last_response", "stopPolling", "retryConnection"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/hooks/useJarvis.js"], "sourcesContent": ["/**\n * use<PERSON><PERSON><PERSON> Hook\n * Custom React hook for managing Jarvis state and API interactions\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport jarvisApi from '../services/jarvisApi';\n\nexport const useJarvis = () => {\n  // State management\n  const [state, setState] = useState('startup');\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isServerConnected, setIsServerConnected] = useState(false);\n  const [lastResponse, setLastResponse] = useState('');\n  const [conversationHistory, setConversationHistory] = useState([]);\n  const [error, setError] = useState(null);\n\n  // Refs for cleanup\n  const pollingIntervalRef = useRef(null);\n  const mountedRef = useRef(true);\n\n  /**\n   * Check server connection\n   */\n  const checkServerConnection = useCallback(async () => {\n    try {\n      const isRunning = await jarvisApi.isServerRunning();\n      if (mountedRef.current) {\n        setIsServerConnected(isRunning);\n        if (isRunning) {\n          setError(null);\n        }\n      }\n      return isRunning;\n    } catch (err) {\n      if (mountedRef.current) {\n        setIsServerConnected(false);\n        setError('Failed to connect to Jarvis backend');\n      }\n      return false;\n    }\n  }, []);\n\n  /**\n   * Update Jarvis state\n   */\n  const updateState = useCallback(async (newState) => {\n    try {\n      await jarvisApi.setState(newState);\n      if (mountedRef.current) {\n        setState(newState);\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Failed to update state:', err);\n      if (mountedRef.current) {\n        setError(`Failed to update state: ${err.message}`);\n      }\n    }\n  }, []);\n\n  /**\n   * Send message to Jarvis\n   */\n  const sendMessage = useCallback(async (message) => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n\n    try {\n      setIsProcessing(true);\n      setError(null);\n\n      const response = await jarvisApi.sendMessage(message);\n\n      if (mountedRef.current) {\n        setLastResponse(response.response);\n        setState(response.current_state || 'rest');\n\n        // Update conversation history\n        setConversationHistory(prev => [...prev, {\n          id: Date.now(),\n          user: message,\n          jarvis: response.response,\n          timestamp: response.timestamp\n        }]);\n\n        // Automatically speak the response\n        if (response.response) {\n          try {\n            await jarvisApi.textToSpeech(response.response);\n          } catch (speechError) {\n            console.warn('Text-to-speech failed:', speechError);\n            // Don't throw error for TTS failure, just log it\n          }\n        }\n      }\n\n      return response;\n    } catch (err) {\n      console.error('Failed to send message:', err);\n      if (mountedRef.current) {\n        setError(`Failed to send message: ${err.message}`);\n      }\n      throw err;\n    } finally {\n      if (mountedRef.current) {\n        setIsProcessing(false);\n      }\n    }\n  }, [isServerConnected]);\n\n  /**\n   * Start speech recognition\n   */\n  const startSpeechRecognition = useCallback(async () => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n\n    try {\n      setIsProcessing(true);\n      setError(null);\n\n      const response = await jarvisApi.startSpeechRecognition();\n\n      if (mountedRef.current && response.text) {\n        // Automatically send the recognized text as a message\n        // The sendMessage function will automatically speak the response\n        return await sendMessage(response.text);\n      }\n\n      return response;\n    } catch (err) {\n      console.error('Speech recognition failed:', err);\n      if (mountedRef.current) {\n        setError(`Speech recognition failed: ${err.message}`);\n      }\n      throw err;\n    } finally {\n      if (mountedRef.current) {\n        setIsProcessing(false);\n      }\n    }\n  }, [isServerConnected, sendMessage]);\n\n  /**\n   * Convert text to speech\n   */\n  const speakText = useCallback(async (text) => {\n    if (!isServerConnected) {\n      throw new Error('Not connected to Jarvis backend');\n    }\n\n    try {\n      setError(null);\n      const response = await jarvisApi.textToSpeech(text);\n      return response;\n    } catch (err) {\n      console.error('Text-to-speech failed:', err);\n      if (mountedRef.current) {\n        setError(`Text-to-speech failed: ${err.message}`);\n      }\n      throw err;\n    }\n  }, [isServerConnected]);\n\n  /**\n   * Clear conversation history\n   */\n  const clearHistory = useCallback(async () => {\n    try {\n      await jarvisApi.clearConversationHistory();\n      if (mountedRef.current) {\n        setConversationHistory([]);\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Failed to clear history:', err);\n      if (mountedRef.current) {\n        setError(`Failed to clear history: ${err.message}`);\n      }\n    }\n  }, []);\n\n  /**\n   * Initialize connection and start polling\n   */\n  useEffect(() => {\n    let mounted = true;\n    mountedRef.current = true;\n\n    const initialize = async () => {\n      // Check initial connection\n      await checkServerConnection();\n\n      // Start polling for state updates\n      if (mounted) {\n        pollingIntervalRef.current = await jarvisApi.pollState(\n          (stateData) => {\n            if (mountedRef.current) {\n              setState(stateData.current_state);\n              setIsProcessing(stateData.is_processing);\n              setLastResponse(stateData.last_response);\n            }\n          },\n          2000 // Poll every 2 seconds\n        );\n      }\n    };\n\n    initialize();\n\n    // Cleanup function\n    return () => {\n      mounted = false;\n      mountedRef.current = false;\n      if (pollingIntervalRef.current) {\n        jarvisApi.stopPolling(pollingIntervalRef.current);\n      }\n    };\n  }, [checkServerConnection]);\n\n  /**\n   * Retry connection\n   */\n  const retryConnection = useCallback(async () => {\n    setError(null);\n    return await checkServerConnection();\n  }, [checkServerConnection]);\n\n  return {\n    // State\n    state,\n    isProcessing,\n    isServerConnected,\n    lastResponse,\n    conversationHistory,\n    error,\n\n    // Actions\n    updateState,\n    sendMessage,\n    startSpeechRecognition,\n    speakText,\n    clearHistory,\n    retryConnection,\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CAChE,MAAO,CAAAC,SAAS,KAAM,uBAAuB,CAE7C,MAAO,MAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CAC7B;AACA,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGP,QAAQ,CAAC,SAAS,CAAC,CAC7C,KAAM,CAACQ,YAAY,CAAEC,eAAe,CAAC,CAAGT,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACU,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGX,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACY,YAAY,CAAEC,eAAe,CAAC,CAAGb,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACc,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACgB,KAAK,CAAEC,QAAQ,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAAAkB,kBAAkB,CAAGf,MAAM,CAAC,IAAI,CAAC,CACvC,KAAM,CAAAgB,UAAU,CAAGhB,MAAM,CAAC,IAAI,CAAC,CAE/B;AACF;AACA,KACE,KAAM,CAAAiB,qBAAqB,CAAGlB,WAAW,CAAC,SAAY,CACpD,GAAI,CACF,KAAM,CAAAmB,SAAS,CAAG,KAAM,CAAAjB,SAAS,CAACkB,eAAe,CAAC,CAAC,CACnD,GAAIH,UAAU,CAACI,OAAO,CAAE,CACtBZ,oBAAoB,CAACU,SAAS,CAAC,CAC/B,GAAIA,SAAS,CAAE,CACbJ,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CACA,MAAO,CAAAI,SAAS,CAClB,CAAE,MAAOG,GAAG,CAAE,CACZ,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBZ,oBAAoB,CAAC,KAAK,CAAC,CAC3BM,QAAQ,CAAC,qCAAqC,CAAC,CACjD,CACA,MAAO,MAAK,CACd,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAQ,WAAW,CAAGvB,WAAW,CAAC,KAAO,CAAAwB,QAAQ,EAAK,CAClD,GAAI,CACF,KAAM,CAAAtB,SAAS,CAACG,QAAQ,CAACmB,QAAQ,CAAC,CAClC,GAAIP,UAAU,CAACI,OAAO,CAAE,CACtBhB,QAAQ,CAACmB,QAAQ,CAAC,CAClBT,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAE,MAAOO,GAAG,CAAE,CACZG,OAAO,CAACX,KAAK,CAAC,yBAAyB,CAAEQ,GAAG,CAAC,CAC7C,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBN,QAAQ,4BAAAW,MAAA,CAA4BJ,GAAG,CAACK,OAAO,CAAE,CAAC,CACpD,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE,KAAM,CAAAC,WAAW,CAAG5B,WAAW,CAAC,KAAO,CAAA2B,OAAO,EAAK,CACjD,GAAI,CAACnB,iBAAiB,CAAE,CACtB,KAAM,IAAI,CAAAqB,KAAK,CAAC,iCAAiC,CAAC,CACpD,CAEA,GAAI,CACFtB,eAAe,CAAC,IAAI,CAAC,CACrBQ,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAA5B,SAAS,CAAC0B,WAAW,CAACD,OAAO,CAAC,CAErD,GAAIV,UAAU,CAACI,OAAO,CAAE,CACtBV,eAAe,CAACmB,QAAQ,CAACA,QAAQ,CAAC,CAClCzB,QAAQ,CAACyB,QAAQ,CAACC,aAAa,EAAI,MAAM,CAAC,CAE1C;AACAlB,sBAAsB,CAACmB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CACvCC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,IAAI,CAAET,OAAO,CACbU,MAAM,CAAEP,QAAQ,CAACA,QAAQ,CACzBQ,SAAS,CAAER,QAAQ,CAACQ,SACtB,CAAC,CAAC,CAAC,CAEH;AACA,GAAIR,QAAQ,CAACA,QAAQ,CAAE,CACrB,GAAI,CACF,KAAM,CAAA5B,SAAS,CAACqC,YAAY,CAACT,QAAQ,CAACA,QAAQ,CAAC,CACjD,CAAE,MAAOU,WAAW,CAAE,CACpBf,OAAO,CAACgB,IAAI,CAAC,wBAAwB,CAAED,WAAW,CAAC,CACnD;AACF,CACF,CACF,CAEA,MAAO,CAAAV,QAAQ,CACjB,CAAE,MAAOR,GAAG,CAAE,CACZG,OAAO,CAACX,KAAK,CAAC,yBAAyB,CAAEQ,GAAG,CAAC,CAC7C,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBN,QAAQ,4BAAAW,MAAA,CAA4BJ,GAAG,CAACK,OAAO,CAAE,CAAC,CACpD,CACA,KAAM,CAAAL,GAAG,CACX,CAAC,OAAS,CACR,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBd,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CACF,CAAC,CAAE,CAACC,iBAAiB,CAAC,CAAC,CAEvB;AACF;AACA,KACE,KAAM,CAAAkC,sBAAsB,CAAG1C,WAAW,CAAC,SAAY,CACrD,GAAI,CAACQ,iBAAiB,CAAE,CACtB,KAAM,IAAI,CAAAqB,KAAK,CAAC,iCAAiC,CAAC,CACpD,CAEA,GAAI,CACFtB,eAAe,CAAC,IAAI,CAAC,CACrBQ,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAA5B,SAAS,CAACwC,sBAAsB,CAAC,CAAC,CAEzD,GAAIzB,UAAU,CAACI,OAAO,EAAIS,QAAQ,CAACa,IAAI,CAAE,CACvC;AACA;AACA,MAAO,MAAM,CAAAf,WAAW,CAACE,QAAQ,CAACa,IAAI,CAAC,CACzC,CAEA,MAAO,CAAAb,QAAQ,CACjB,CAAE,MAAOR,GAAG,CAAE,CACZG,OAAO,CAACX,KAAK,CAAC,4BAA4B,CAAEQ,GAAG,CAAC,CAChD,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBN,QAAQ,+BAAAW,MAAA,CAA+BJ,GAAG,CAACK,OAAO,CAAE,CAAC,CACvD,CACA,KAAM,CAAAL,GAAG,CACX,CAAC,OAAS,CACR,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBd,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CACF,CAAC,CAAE,CAACC,iBAAiB,CAAEoB,WAAW,CAAC,CAAC,CAEpC;AACF;AACA,KACE,KAAM,CAAAgB,SAAS,CAAG5C,WAAW,CAAC,KAAO,CAAA2C,IAAI,EAAK,CAC5C,GAAI,CAACnC,iBAAiB,CAAE,CACtB,KAAM,IAAI,CAAAqB,KAAK,CAAC,iCAAiC,CAAC,CACpD,CAEA,GAAI,CACFd,QAAQ,CAAC,IAAI,CAAC,CACd,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAA5B,SAAS,CAACqC,YAAY,CAACI,IAAI,CAAC,CACnD,MAAO,CAAAb,QAAQ,CACjB,CAAE,MAAOR,GAAG,CAAE,CACZG,OAAO,CAACX,KAAK,CAAC,wBAAwB,CAAEQ,GAAG,CAAC,CAC5C,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBN,QAAQ,2BAAAW,MAAA,CAA2BJ,GAAG,CAACK,OAAO,CAAE,CAAC,CACnD,CACA,KAAM,CAAAL,GAAG,CACX,CACF,CAAC,CAAE,CAACd,iBAAiB,CAAC,CAAC,CAEvB;AACF;AACA,KACE,KAAM,CAAAqC,YAAY,CAAG7C,WAAW,CAAC,SAAY,CAC3C,GAAI,CACF,KAAM,CAAAE,SAAS,CAAC4C,wBAAwB,CAAC,CAAC,CAC1C,GAAI7B,UAAU,CAACI,OAAO,CAAE,CACtBR,sBAAsB,CAAC,EAAE,CAAC,CAC1BE,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAE,MAAOO,GAAG,CAAE,CACZG,OAAO,CAACX,KAAK,CAAC,0BAA0B,CAAEQ,GAAG,CAAC,CAC9C,GAAIL,UAAU,CAACI,OAAO,CAAE,CACtBN,QAAQ,6BAAAW,MAAA,CAA6BJ,GAAG,CAACK,OAAO,CAAE,CAAC,CACrD,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACF;AACA,KACE5B,SAAS,CAAC,IAAM,CACd,GAAI,CAAAgD,OAAO,CAAG,IAAI,CAClB9B,UAAU,CAACI,OAAO,CAAG,IAAI,CAEzB,KAAM,CAAA2B,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B;AACA,KAAM,CAAA9B,qBAAqB,CAAC,CAAC,CAE7B;AACA,GAAI6B,OAAO,CAAE,CACX/B,kBAAkB,CAACK,OAAO,CAAG,KAAM,CAAAnB,SAAS,CAAC+C,SAAS,CACnDC,SAAS,EAAK,CACb,GAAIjC,UAAU,CAACI,OAAO,CAAE,CACtBhB,QAAQ,CAAC6C,SAAS,CAACnB,aAAa,CAAC,CACjCxB,eAAe,CAAC2C,SAAS,CAACC,aAAa,CAAC,CACxCxC,eAAe,CAACuC,SAAS,CAACE,aAAa,CAAC,CAC1C,CACF,CAAC,CACD,IAAK;AACP,CAAC,CACH,CACF,CAAC,CAEDJ,UAAU,CAAC,CAAC,CAEZ;AACA,MAAO,IAAM,CACXD,OAAO,CAAG,KAAK,CACf9B,UAAU,CAACI,OAAO,CAAG,KAAK,CAC1B,GAAIL,kBAAkB,CAACK,OAAO,CAAE,CAC9BnB,SAAS,CAACmD,WAAW,CAACrC,kBAAkB,CAACK,OAAO,CAAC,CACnD,CACF,CAAC,CACH,CAAC,CAAE,CAACH,qBAAqB,CAAC,CAAC,CAE3B;AACF;AACA,KACE,KAAM,CAAAoC,eAAe,CAAGtD,WAAW,CAAC,SAAY,CAC9Ce,QAAQ,CAAC,IAAI,CAAC,CACd,MAAO,MAAM,CAAAG,qBAAqB,CAAC,CAAC,CACtC,CAAC,CAAE,CAACA,qBAAqB,CAAC,CAAC,CAE3B,MAAO,CACL;AACAd,KAAK,CACLE,YAAY,CACZE,iBAAiB,CACjBE,YAAY,CACZE,mBAAmB,CACnBE,KAAK,CAEL;AACAS,WAAW,CACXK,WAAW,CACXc,sBAAsB,CACtBE,SAAS,CACTC,YAAY,CACZS,eACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}