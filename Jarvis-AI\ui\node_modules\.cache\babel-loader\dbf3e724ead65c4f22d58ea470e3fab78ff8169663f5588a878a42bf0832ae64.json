{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\components\\\\ui\\\\CuteMenuIcon.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CuteMenuIcon = ({\n  onMenuToggle\n}) => {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [eyePosition, setEyePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovered, setIsHovered] = useState(false);\n  const iconRef = useRef(null);\n\n  // Track mouse position across entire screen for eye movement\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (iconRef.current) {\n        const rect = iconRef.current.getBoundingClientRect();\n        const iconCenterX = rect.left + rect.width / 2;\n        const iconCenterY = rect.top + rect.height / 2;\n\n        // Calculate eye movement based on mouse position relative to icon center\n        const maxDistance = 6; // Maximum eye movement distance\n        const distance = Math.min(Math.sqrt(Math.pow(e.clientX - iconCenterX, 2) + Math.pow(e.clientY - iconCenterY, 2)) / 100, maxDistance);\n        const angle = Math.atan2(e.clientY - iconCenterY, e.clientX - iconCenterX);\n        const eyeX = Math.cos(angle) * Math.min(distance, maxDistance);\n        const eyeY = Math.sin(angle) * Math.min(distance, maxDistance);\n        setEyePosition({\n          x: eyeX,\n          y: eyeY\n        });\n      }\n    };\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n  const toggleMenu = () => {\n    const newMenuState = !isMenuOpen;\n    setIsMenuOpen(newMenuState);\n    if (onMenuToggle) {\n      onMenuToggle(newMenuState);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cute-menu-wrapper\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: iconRef,\n      className: \"cute-menu-icon-container\",\n      onClick: toggleMenu,\n      onMouseEnter: () => setIsHovered(true),\n      onMouseLeave: () => setIsHovered(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cute-icon-background\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cute-eyes\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `cute-eye left-eye ${isHovered ? 'hidden' : ''}`,\n            style: {\n              transform: `translate(${eyePosition.x}px, ${eyePosition.y}px)`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `cute-eye right-eye ${isHovered ? 'hidden' : ''}`,\n            style: {\n              transform: `translate(${eyePosition.x}px, ${eyePosition.y}px)`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `cute-happy-eyes ${isHovered ? 'visible' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              className: \"happy-eye\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"white\",\n                d: \"M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              className: \"happy-eye\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"white\",\n                d: \"M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"click-me-label\",\n      children: \"Click Me!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), isMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cute-dropdown-menu\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-option\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-icon notifications-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-option\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-icon settings-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            stroke: \"currentColor\",\n            strokeWidth: \"1.5\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\",\n              strokeLinejoin: \"round\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\",\n              strokeLinejoin: \"round\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-option\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-icon chat-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            stroke: \"currentColor\",\n            strokeWidth: \"1.5\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\",\n              strokeLinejoin: \"round\",\n              strokeLinecap: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(CuteMenuIcon, \"Ny0P63hF5+80sMaCR60p4Qt9bYo=\");\n_c = CuteMenuIcon;\nexport default CuteMenuIcon;\nvar _c;\n$RefreshReg$(_c, \"CuteMenuIcon\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "CuteMenuIcon", "onMenuToggle", "_s", "isMenuOpen", "setIsMenuOpen", "eyePosition", "setEyePosition", "x", "y", "isHovered", "setIsHovered", "iconRef", "handleMouseMove", "e", "current", "rect", "getBoundingClientRect", "iconCenterX", "left", "width", "iconCenterY", "top", "height", "maxDistance", "distance", "Math", "min", "sqrt", "pow", "clientX", "clientY", "angle", "atan2", "eyeX", "cos", "eyeY", "sin", "window", "addEventListener", "removeEventListener", "toggleMenu", "newMenuState", "className", "children", "ref", "onClick", "onMouseEnter", "onMouseLeave", "style", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "viewBox", "d", "xmlns", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ui/CuteMenuIcon.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\n\nconst CuteMenuIcon = ({ onMenuToggle }) => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [eyePosition, setEyePosition] = useState({ x: 0, y: 0 });\n  const [isHovered, setIsHovered] = useState(false);\n  const iconRef = useRef(null);\n\n  // Track mouse position across entire screen for eye movement\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (iconRef.current) {\n        const rect = iconRef.current.getBoundingClientRect();\n        const iconCenterX = rect.left + rect.width / 2;\n        const iconCenterY = rect.top + rect.height / 2;\n\n        // Calculate eye movement based on mouse position relative to icon center\n        const maxDistance = 6; // Maximum eye movement distance\n        const distance = Math.min(\n          Math.sqrt(\n            Math.pow(e.clientX - iconCenterX, 2) +\n            Math.pow(e.clientY - iconCenterY, 2)\n          ) / 100,\n          maxDistance\n        );\n\n        const angle = Math.atan2(e.clientY - iconCenterY, e.clientX - iconCenterX);\n        const eyeX = Math.cos(angle) * Math.min(distance, maxDistance);\n        const eyeY = Math.sin(angle) * Math.min(distance, maxDistance);\n\n        setEyePosition({ x: eyeX, y: eyeY });\n      }\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  const toggleMenu = () => {\n    const newMenuState = !isMenuOpen;\n    setIsMenuOpen(newMenuState);\n    if (onMenuToggle) {\n      onMenuToggle(newMenuState);\n    }\n  };\n\n  return (\n    <div className=\"cute-menu-wrapper\">\n      <div\n        ref={iconRef}\n        className=\"cute-menu-icon-container\"\n        onClick={toggleMenu}\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n      >\n        <div className=\"cute-icon-background\">\n          <div className=\"cute-eyes\">\n            {/* Normal Eyes */}\n            <div\n              className={`cute-eye left-eye ${isHovered ? 'hidden' : ''}`}\n              style={{\n                transform: `translate(${eyePosition.x}px, ${eyePosition.y}px)`\n              }}\n            />\n            <div\n              className={`cute-eye right-eye ${isHovered ? 'hidden' : ''}`}\n              style={{\n                transform: `translate(${eyePosition.x}px, ${eyePosition.y}px)`\n              }}\n            />\n\n            {/* Happy Eyes (on hover) */}\n            <div className={`cute-happy-eyes ${isHovered ? 'visible' : ''}`}>\n              <svg fill=\"none\" viewBox=\"0 0 24 24\" className=\"happy-eye\">\n                <path\n                  fill=\"white\"\n                  d=\"M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z\"\n                />\n              </svg>\n              <svg fill=\"none\" viewBox=\"0 0 24 24\" className=\"happy-eye\">\n                <path\n                  fill=\"white\"\n                  d=\"M8.28386 16.2843C8.9917 15.7665 9.8765 14.731 12 14.731C14.1235 14.731 15.0083 15.7665 15.7161 16.2843C17.8397 17.8376 18.7542 16.4845 18.9014 15.7665C19.4323 13.1777 17.6627 11.1066 17.3088 10.5888C16.3844 9.23666 14.1235 8 12 8C9.87648 8 7.61556 9.23666 6.69122 10.5888C6.33728 11.1066 4.56771 13.1777 5.09858 15.7665C5.24582 16.4845 6.16034 17.8376 8.28386 16.2843Z\"\n                />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"click-me-label\">Click Me!</div>\n\n      {isMenuOpen && (\n        <div className=\"cute-dropdown-menu\">\n          <div className=\"menu-option\">\n            <div className=\"menu-icon notifications-icon\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\" />\n              </svg>\n            </div>\n            <span>Notifications</span>\n          </div>\n\n          <div className=\"menu-option\">\n            <div className=\"menu-icon settings-icon\">\n              <svg stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n                <path d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n              </svg>\n            </div>\n            <span>Settings</span>\n          </div>\n\n          <div className=\"menu-option\">\n            <div className=\"menu-icon chat-icon\">\n              <svg stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n              </svg>\n            </div>\n            <span>Chat</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CuteMenuIcon;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3D,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC;IAAEY,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC9D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMgB,OAAO,GAAGd,MAAM,CAAC,IAAI,CAAC;;EAE5B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMgB,eAAe,GAAIC,CAAC,IAAK;MAC7B,IAAIF,OAAO,CAACG,OAAO,EAAE;QACnB,MAAMC,IAAI,GAAGJ,OAAO,CAACG,OAAO,CAACE,qBAAqB,CAAC,CAAC;QACpD,MAAMC,WAAW,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;QAC9C,MAAMC,WAAW,GAAGL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;;QAE9C;QACA,MAAMC,WAAW,GAAG,CAAC,CAAC,CAAC;QACvB,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CACvBD,IAAI,CAACE,IAAI,CACPF,IAAI,CAACG,GAAG,CAACf,CAAC,CAACgB,OAAO,GAAGZ,WAAW,EAAE,CAAC,CAAC,GACpCQ,IAAI,CAACG,GAAG,CAACf,CAAC,CAACiB,OAAO,GAAGV,WAAW,EAAE,CAAC,CACrC,CAAC,GAAG,GAAG,EACPG,WACF,CAAC;QAED,MAAMQ,KAAK,GAAGN,IAAI,CAACO,KAAK,CAACnB,CAAC,CAACiB,OAAO,GAAGV,WAAW,EAAEP,CAAC,CAACgB,OAAO,GAAGZ,WAAW,CAAC;QAC1E,MAAMgB,IAAI,GAAGR,IAAI,CAACS,GAAG,CAACH,KAAK,CAAC,GAAGN,IAAI,CAACC,GAAG,CAACF,QAAQ,EAAED,WAAW,CAAC;QAC9D,MAAMY,IAAI,GAAGV,IAAI,CAACW,GAAG,CAACL,KAAK,CAAC,GAAGN,IAAI,CAACC,GAAG,CAACF,QAAQ,EAAED,WAAW,CAAC;QAE9DjB,cAAc,CAAC;UAAEC,CAAC,EAAE0B,IAAI;UAAEzB,CAAC,EAAE2B;QAAK,CAAC,CAAC;MACtC;IACF,CAAC;IAEDE,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAE1B,eAAe,CAAC;IACrD,OAAO,MAAMyB,MAAM,CAACE,mBAAmB,CAAC,WAAW,EAAE3B,eAAe,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4B,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAACtC,UAAU;IAChCC,aAAa,CAACqC,YAAY,CAAC;IAC3B,IAAIxC,YAAY,EAAE;MAChBA,YAAY,CAACwC,YAAY,CAAC;IAC5B;EACF,CAAC;EAED,oBACE1C,OAAA;IAAK2C,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC5C,OAAA;MACE6C,GAAG,EAAEjC,OAAQ;MACb+B,SAAS,EAAC,0BAA0B;MACpCG,OAAO,EAAEL,UAAW;MACpBM,YAAY,EAAEA,CAAA,KAAMpC,YAAY,CAAC,IAAI,CAAE;MACvCqC,YAAY,EAAEA,CAAA,KAAMrC,YAAY,CAAC,KAAK,CAAE;MAAAiC,QAAA,eAExC5C,OAAA;QAAK2C,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnC5C,OAAA;UAAK2C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB5C,OAAA;YACE2C,SAAS,EAAE,qBAAqBjC,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC5DuC,KAAK,EAAE;cACLC,SAAS,EAAE,aAAa5C,WAAW,CAACE,CAAC,OAAOF,WAAW,CAACG,CAAC;YAC3D;UAAE;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFtD,OAAA;YACE2C,SAAS,EAAE,sBAAsBjC,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC7DuC,KAAK,EAAE;cACLC,SAAS,EAAE,aAAa5C,WAAW,CAACE,CAAC,OAAOF,WAAW,CAACG,CAAC;YAC3D;UAAE;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGFtD,OAAA;YAAK2C,SAAS,EAAE,mBAAmBjC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;YAAAkC,QAAA,gBAC9D5C,OAAA;cAAKuD,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACb,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxD5C,OAAA;gBACEuD,IAAI,EAAC,OAAO;gBACZE,CAAC,EAAC;cAAkX;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtD,OAAA;cAAKuD,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACb,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxD5C,OAAA;gBACEuD,IAAI,EAAC,OAAO;gBACZE,CAAC,EAAC;cAAkX;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtD,OAAA;MAAK2C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAAC;IAAS;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EAE9ClD,UAAU,iBACTJ,OAAA;MAAK2C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC5C,OAAA;QAAK2C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5C,OAAA;UAAK2C,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3C5C,OAAA;YAAK0D,KAAK,EAAC,4BAA4B;YAACH,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACG,WAAW,EAAC,KAAK;YAACC,MAAM,EAAC,cAAc;YAAAhB,QAAA,eAC7G5C,OAAA;cAAM6D,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACL,CAAC,EAAC;YAAwN;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7Q;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAA4C,QAAA,EAAM;QAAa;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAENtD,OAAA;QAAK2C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5C,OAAA;UAAK2C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtC5C,OAAA;YAAK4D,MAAM,EAAC,cAAc;YAACD,WAAW,EAAC,KAAK;YAACH,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,MAAM;YAAAX,QAAA,gBAC1E5C,OAAA;cAAMyD,CAAC,EAAC,y+BAAy+B;cAACK,cAAc,EAAC,OAAO;cAACD,aAAa,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjiCtD,OAAA;cAAMyD,CAAC,EAAC,qCAAqC;cAACK,cAAc,EAAC,OAAO;cAACD,aAAa,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAA4C,QAAA,EAAM;QAAQ;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAENtD,OAAA;QAAK2C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5C,OAAA;UAAK2C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClC5C,OAAA;YAAK4D,MAAM,EAAC,cAAc;YAACD,WAAW,EAAC,KAAK;YAACH,OAAO,EAAC,WAAW;YAACD,IAAI,EAAC,MAAM;YAAAX,QAAA,eAC1E5C,OAAA;cAAMyD,CAAC,EAAC,4eAA4e;cAACK,cAAc,EAAC,OAAO;cAACD,aAAa,EAAC;YAAO;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjiB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAA4C,QAAA,EAAM;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnD,EAAA,CA3HIF,YAAY;AAAA8D,EAAA,GAAZ9D,YAAY;AA6HlB,eAAeA,YAAY;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}