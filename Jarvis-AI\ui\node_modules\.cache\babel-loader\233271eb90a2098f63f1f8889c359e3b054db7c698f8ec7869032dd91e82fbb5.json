{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import Footer from'./layout/Footer';import AnimatedButton from'./ui/AnimatedButton';// Import the existing cute menu icon (keeping your original implementation)\nimport CuteMenuIcon from'./ui/CuteMenuIcon';import ExpandableSidebar from'./ui/ExpandableSidebar';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HomeScreen=_ref=>{let{onEnterJarvis}=_ref;const[isEntering,setIsEntering]=useState(false);const[showExitAnimation,setShowExitAnimation]=useState(false);const[menuOpen,setMenuOpen]=useState(false);const wallpaperRef=useRef(null);const exitAnimationRef=useRef(null);// Handle entering Jarvis mode\nconst handleEnterJarvis=()=>{setIsEntering(true);setShowExitAnimation(true);// Wait for exit animation to complete before transitioning\nsetTimeout(()=>{onEnterJarvis();},2000);// Adjust timing based on screenexit.gif duration\n};// Handle menu actions\nconst handleMenuToggle=isOpen=>{setMenuOpen(isOpen);};const handleSettingsClick=()=>{console.log('Settings clicked');};const handleHelpClick=()=>{console.log('Help clicked');};const handleVoiceCommandsClick=()=>{console.log('Voice Commands clicked');};const handleAboutClick=()=>{console.log('About clicked');};// Additive dissolve effect for wallpaper looping\nuseEffect(()=>{const wallpaper=wallpaperRef.current;if(wallpaper){wallpaper.style.mixBlendMode='screen';wallpaper.style.filter='brightness(0.8) contrast(1.2)';}},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"homescreen-container\",children:[/*#__PURE__*/_jsx(CuteMenuIcon,{onMenuToggle:handleMenuToggle}),/*#__PURE__*/_jsx(\"div\",{className:\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",children:/*#__PURE__*/_jsx(ExpandableSidebar,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"wallpaper-container\",children:[/*#__PURE__*/_jsx(\"img\",{ref:wallpaperRef,src:\"/assets/HomeSettingsscreen.gif\",alt:\"Animated Wallpaper\",className:\"wallpaper-background\",onError:e=>{// Fallback to a gradient if gif not found\ne.target.style.display='none';e.target.parentElement.style.background='linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)';}}),/*#__PURE__*/_jsx(\"div\",{className:\"wallpaper-overlay\"})]}),showExitAnimation&&/*#__PURE__*/_jsx(\"div\",{className:\"exit-animation-overlay\",children:/*#__PURE__*/_jsx(\"img\",{ref:exitAnimationRef,src:\"/assets/screenexit.gif\",alt:\"Screen Exit Animation\",className:\"exit-animation\",onError:e=>{// Fallback animation if gif not found\ne.target.style.display='none';e.target.parentElement.innerHTML='<div class=\"fallback-exit-animation\"></div>';}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"homescreen-content \".concat(isEntering?'entering':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"title-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"marvel-logo\",children:\"MARVEL\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"main-title\",children:\"JARVIS\"}),/*#__PURE__*/_jsx(\"div\",{className:\"subtitle\",children:\"PRESS \\u26A1 TO START\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"jarvis-icon-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"jarvis-icon\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"icon-glow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"icon-core\",children:/*#__PURE__*/_jsxs(\"svg\",{viewBox:\"0 0 100 100\",className:\"jarvis-svg\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"50\",cy:\"50\",r:\"30\",className:\"outer-ring\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"50\",cy:\"50\",r:\"20\",className:\"middle-ring\"}),/*#__PURE__*/_jsx(\"circle\",{cx:\"50\",cy:\"50\",r:\"10\",className:\"inner-core\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M30 50 L70 50 M50 30 L50 70\",className:\"cross-lines\"})]})})]}),/*#__PURE__*/_jsx(AnimatedButton,{onClick:handleEnterJarvis,variant:\"primary\",size:\"large\",loading:isEntering,className:\"mt-4\",children:isEntering?'ACTIVATING...':'ACTIVATE JARVIS'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bottom-menu\",children:[/*#__PURE__*/_jsx(AnimatedButton,{onClick:handleSettingsClick,variant:\"secondary\",size:\"small\",className:\"menu-button\",children:\"\\u2699\\uFE0F Settings\"}),/*#__PURE__*/_jsx(AnimatedButton,{onClick:handleVoiceCommandsClick,variant:\"secondary\",size:\"small\",className:\"menu-button\",children:\"\\uD83C\\uDFA4 Voice\"}),/*#__PURE__*/_jsx(AnimatedButton,{onClick:handleHelpClick,variant:\"secondary\",size:\"small\",className:\"menu-button\",children:\"\\u2753 Help\"}),/*#__PURE__*/_jsx(AnimatedButton,{onClick:handleAboutClick,variant:\"secondary\",size:\"small\",className:\"menu-button\",children:\"\\u2139\\uFE0F About\"})]})]}),/*#__PURE__*/_jsx(Footer,{onHomeClick:()=>console.log('Home clicked'),onSettingsClick:handleSettingsClick,onHelpClick:handleHelpClick,showNavigation:!menuOpen// Hide footer nav when menu is open\n})]});};export default HomeScreen;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Footer", "AnimatedButton", "CuteMenuIcon", "ExpandableSidebar", "jsx", "_jsx", "jsxs", "_jsxs", "HomeScreen", "_ref", "on<PERSON><PERSON><PERSON><PERSON><PERSON>", "isEntering", "setIsEntering", "showExitAnimation", "setShowExitAnimation", "menuOpen", "setMenuOpen", "wallpaperRef", "exitAnimationRef", "handleE<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "handleMenuToggle", "isOpen", "handleSettingsClick", "console", "log", "handleHelpClick", "handleVoiceCommandsClick", "handleAboutClick", "wallpaper", "current", "style", "mixBlendMode", "filter", "className", "children", "onMenuToggle", "ref", "src", "alt", "onError", "e", "target", "display", "parentElement", "background", "innerHTML", "concat", "viewBox", "cx", "cy", "r", "d", "onClick", "variant", "size", "loading", "onHomeClick", "onSettingsClick", "onHelpClick", "showNavigation"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/HomeScreen.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport Footer from './layout/Footer';\nimport AnimatedButton from './ui/AnimatedButton';\n\n// Import the existing cute menu icon (keeping your original implementation)\nimport CuteMenuIcon from './ui/CuteMenuIcon';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\n\nconst HomeScreen = ({ onEnterJarvis }) => {\n  const [isEntering, setIsEntering] = useState(false);\n  const [showExitAnimation, setShowExitAnimation] = useState(false);\n  const [menuOpen, setMenuOpen] = useState(false);\n  const wallpaperRef = useRef(null);\n  const exitAnimationRef = useRef(null);\n\n  // Handle entering Jarvis mode\n  const handleEnterJarvis = () => {\n    setIsEntering(true);\n    setShowExitAnimation(true);\n\n    // Wait for exit animation to complete before transitioning\n    setTimeout(() => {\n      onEnterJarvis();\n    }, 2000); // Adjust timing based on screenexit.gif duration\n  };\n\n  // Handle menu actions\n  const handleMenuToggle = (isOpen) => {\n    setMenuOpen(isOpen);\n  };\n\n  const handleSettingsClick = () => {\n    console.log('Settings clicked');\n  };\n\n  const handleHelpClick = () => {\n    console.log('Help clicked');\n  };\n\n  const handleVoiceCommandsClick = () => {\n    console.log('Voice Commands clicked');\n  };\n\n  const handleAboutClick = () => {\n    console.log('About clicked');\n  };\n\n  // Additive dissolve effect for wallpaper looping\n  useEffect(() => {\n    const wallpaper = wallpaperRef.current;\n    if (wallpaper) {\n      wallpaper.style.mixBlendMode = 'screen';\n      wallpaper.style.filter = 'brightness(0.8) contrast(1.2)';\n    }\n  }, []);\n\n  return (\n    <div className=\"homescreen-container\">\n      {/* Cute Menu Icon in Top Left */}\n      <CuteMenuIcon onMenuToggle={handleMenuToggle} />\n      \n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Animated Wallpaper Background */}\n      <div className=\"wallpaper-container\">\n        <img\n          ref={wallpaperRef}\n          src=\"/assets/HomeSettingsscreen.gif\"\n          alt=\"Animated Wallpaper\"\n          className=\"wallpaper-background\"\n          onError={(e) => {\n            // Fallback to a gradient if gif not found\n            e.target.style.display = 'none';\n            e.target.parentElement.style.background =\n              'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)';\n          }}\n        />\n\n        {/* Overlay for better text readability */}\n        <div className=\"wallpaper-overlay\"></div>\n      </div>\n\n      {/* Exit Animation Overlay */}\n      {showExitAnimation && (\n        <div className=\"exit-animation-overlay\">\n          <img\n            ref={exitAnimationRef}\n            src=\"/assets/screenexit.gif\"\n            alt=\"Screen Exit Animation\"\n            className=\"exit-animation\"\n            onError={(e) => {\n              // Fallback animation if gif not found\n              e.target.style.display = 'none';\n              e.target.parentElement.innerHTML = '<div class=\"fallback-exit-animation\"></div>';\n            }}\n          />\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className={`homescreen-content ${isEntering ? 'entering' : ''}`}>\n        {/* Spider-Man Style Title */}\n        <div className=\"title-section\">\n          <div className=\"marvel-logo\">MARVEL</div>\n          <h1 className=\"main-title\">JARVIS</h1>\n          <div className=\"subtitle\">PRESS ⚡ TO START</div>\n        </div>\n\n        {/* Central Jarvis Icon/Button - Now using AnimatedButton */}\n        <div className=\"jarvis-icon-container\">\n          <div className=\"jarvis-icon\">\n            <div className=\"icon-glow\"></div>\n            <div className=\"icon-core\">\n              <svg viewBox=\"0 0 100 100\" className=\"jarvis-svg\">\n                <circle cx=\"50\" cy=\"50\" r=\"30\" className=\"outer-ring\" />\n                <circle cx=\"50\" cy=\"50\" r=\"20\" className=\"middle-ring\" />\n                <circle cx=\"50\" cy=\"50\" r=\"10\" className=\"inner-core\" />\n                <path d=\"M30 50 L70 50 M50 30 L50 70\" className=\"cross-lines\" />\n              </svg>\n            </div>\n          </div>\n          <AnimatedButton\n            onClick={handleEnterJarvis}\n            variant=\"primary\"\n            size=\"large\"\n            loading={isEntering}\n            className=\"mt-4\"\n          >\n            {isEntering ? 'ACTIVATING...' : 'ACTIVATE JARVIS'}\n          </AnimatedButton>\n        </div>\n\n        {/* Bottom Menu - Now using AnimatedButtons */}\n        <div className=\"bottom-menu\">\n          <AnimatedButton\n            onClick={handleSettingsClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            ⚙️ Settings\n          </AnimatedButton>\n\n          <AnimatedButton\n            onClick={handleVoiceCommandsClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            🎤 Voice\n          </AnimatedButton>\n\n          <AnimatedButton\n            onClick={handleHelpClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            ❓ Help\n          </AnimatedButton>\n\n          <AnimatedButton\n            onClick={handleAboutClick}\n            variant=\"secondary\"\n            size=\"small\"\n            className=\"menu-button\"\n          >\n            ℹ️ About\n          </AnimatedButton>\n        </div>\n      </div>\n\n      {/* Footer with additional navigation */}\n      <Footer\n        onHomeClick={() => console.log('Home clicked')}\n        onSettingsClick={handleSettingsClick}\n        onHelpClick={handleHelpClick}\n        showNavigation={!menuOpen} // Hide footer nav when menu is open\n      />\n    </div>\n\n      \n\n\n  );\n};\n\nexport default HomeScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CACpC,MAAO,CAAAC,cAAc,KAAM,qBAAqB,CAEhD;AACA,MAAO,CAAAC,YAAY,KAAM,mBAAmB,CAC5C,MAAO,CAAAC,iBAAiB,KAAM,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAuB,IAAtB,CAAEC,aAAc,CAAC,CAAAD,IAAA,CACnC,KAAM,CAACE,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACgB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAAoB,YAAY,CAAGlB,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAmB,gBAAgB,CAAGnB,MAAM,CAAC,IAAI,CAAC,CAErC;AACA,KAAM,CAAAoB,iBAAiB,CAAGA,CAAA,GAAM,CAC9BP,aAAa,CAAC,IAAI,CAAC,CACnBE,oBAAoB,CAAC,IAAI,CAAC,CAE1B;AACAM,UAAU,CAAC,IAAM,CACfV,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,IAAI,CAAC,CAAE;AACZ,CAAC,CAED;AACA,KAAM,CAAAW,gBAAgB,CAAIC,MAAM,EAAK,CACnCN,WAAW,CAACM,MAAM,CAAC,CACrB,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChCC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CACjC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5BF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAE,wBAAwB,CAAGA,CAAA,GAAM,CACrCH,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CACvC,CAAC,CAED,KAAM,CAAAG,gBAAgB,CAAGA,CAAA,GAAM,CAC7BJ,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAAC,CAED;AACA3B,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+B,SAAS,CAAGZ,YAAY,CAACa,OAAO,CACtC,GAAID,SAAS,CAAE,CACbA,SAAS,CAACE,KAAK,CAACC,YAAY,CAAG,QAAQ,CACvCH,SAAS,CAACE,KAAK,CAACE,MAAM,CAAG,+BAA+B,CAC1D,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,mBACE1B,KAAA,QAAK2B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eAEnC9B,IAAA,CAACH,YAAY,EAACkC,YAAY,CAAEf,gBAAiB,CAAE,CAAC,cAGhDhB,IAAA,QAAK6B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnE9B,IAAA,CAACF,iBAAiB,GAAE,CAAC,CAClB,CAAC,cAGNI,KAAA,QAAK2B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC9B,IAAA,QACEgC,GAAG,CAAEpB,YAAa,CAClBqB,GAAG,CAAC,gCAAgC,CACpCC,GAAG,CAAC,oBAAoB,CACxBL,SAAS,CAAC,sBAAsB,CAChCM,OAAO,CAAGC,CAAC,EAAK,CACd;AACAA,CAAC,CAACC,MAAM,CAACX,KAAK,CAACY,OAAO,CAAG,MAAM,CAC/BF,CAAC,CAACC,MAAM,CAACE,aAAa,CAACb,KAAK,CAACc,UAAU,CACrC,gEAAgE,CACpE,CAAE,CACH,CAAC,cAGFxC,IAAA,QAAK6B,SAAS,CAAC,mBAAmB,CAAM,CAAC,EACtC,CAAC,CAGLrB,iBAAiB,eAChBR,IAAA,QAAK6B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC9B,IAAA,QACEgC,GAAG,CAAEnB,gBAAiB,CACtBoB,GAAG,CAAC,wBAAwB,CAC5BC,GAAG,CAAC,uBAAuB,CAC3BL,SAAS,CAAC,gBAAgB,CAC1BM,OAAO,CAAGC,CAAC,EAAK,CACd;AACAA,CAAC,CAACC,MAAM,CAACX,KAAK,CAACY,OAAO,CAAG,MAAM,CAC/BF,CAAC,CAACC,MAAM,CAACE,aAAa,CAACE,SAAS,CAAG,6CAA6C,CAClF,CAAE,CACH,CAAC,CACC,CACN,cAGDvC,KAAA,QAAK2B,SAAS,uBAAAa,MAAA,CAAwBpC,UAAU,CAAG,UAAU,CAAG,EAAE,CAAG,CAAAwB,QAAA,eAEnE5B,KAAA,QAAK2B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B9B,IAAA,QAAK6B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,QAAM,CAAK,CAAC,cACzC9B,IAAA,OAAI6B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cACtC9B,IAAA,QAAK6B,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,uBAAgB,CAAK,CAAC,EAC7C,CAAC,cAGN5B,KAAA,QAAK2B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC5B,KAAA,QAAK2B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B9B,IAAA,QAAK6B,SAAS,CAAC,WAAW,CAAM,CAAC,cACjC7B,IAAA,QAAK6B,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB5B,KAAA,QAAKyC,OAAO,CAAC,aAAa,CAACd,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC/C9B,IAAA,WAAQ4C,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,YAAY,CAAE,CAAC,cACxD7B,IAAA,WAAQ4C,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,aAAa,CAAE,CAAC,cACzD7B,IAAA,WAAQ4C,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,YAAY,CAAE,CAAC,cACxD7B,IAAA,SAAM+C,CAAC,CAAC,6BAA6B,CAAClB,SAAS,CAAC,aAAa,CAAE,CAAC,EAC7D,CAAC,CACH,CAAC,EACH,CAAC,cACN7B,IAAA,CAACJ,cAAc,EACboD,OAAO,CAAElC,iBAAkB,CAC3BmC,OAAO,CAAC,SAAS,CACjBC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAE7C,UAAW,CACpBuB,SAAS,CAAC,MAAM,CAAAC,QAAA,CAEfxB,UAAU,CAAG,eAAe,CAAG,iBAAiB,CACnC,CAAC,EACd,CAAC,cAGNJ,KAAA,QAAK2B,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B9B,IAAA,CAACJ,cAAc,EACboD,OAAO,CAAE9B,mBAAoB,CAC7B+B,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,OAAO,CACZrB,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxB,uBAED,CAAgB,CAAC,cAEjB9B,IAAA,CAACJ,cAAc,EACboD,OAAO,CAAE1B,wBAAyB,CAClC2B,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,OAAO,CACZrB,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxB,oBAED,CAAgB,CAAC,cAEjB9B,IAAA,CAACJ,cAAc,EACboD,OAAO,CAAE3B,eAAgB,CACzB4B,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,OAAO,CACZrB,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxB,aAED,CAAgB,CAAC,cAEjB9B,IAAA,CAACJ,cAAc,EACboD,OAAO,CAAEzB,gBAAiB,CAC1B0B,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,OAAO,CACZrB,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxB,oBAED,CAAgB,CAAC,EACd,CAAC,EACH,CAAC,cAGN9B,IAAA,CAACL,MAAM,EACLyD,WAAW,CAAEA,CAAA,GAAMjC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE,CAC/CiC,eAAe,CAAEnC,mBAAoB,CACrCoC,WAAW,CAAEjC,eAAgB,CAC7BkC,cAAc,CAAE,CAAC7C,QAAU;AAAA,CAC5B,CAAC,EACC,CAAC,CAMV,CAAC,CAED,cAAe,CAAAP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}