{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport <PERSON> from './components/Jarvis';\nimport HomeScreen from './components/HomeScreen';\nimport ChatScreen from './components/ChatScreen';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentScreen, setCurrentScreen] = useState('home'); // 'home', 'jarvis', 'chat'\n  const [chatInitialMessage, setChatInitialMessage] = useState('');\n  const handleEnterJarvis = () => {\n    setCurrentScreen('jarvis');\n  };\n  const handleExitToHome = () => {\n    setCurrentScreen('home');\n  };\n  const handleEnterChat = (initialMessage = '') => {\n    setChatInitialMessage(initialMessage);\n    setCurrentScreen('chat');\n  };\n  const handleExitToJarvis = () => {\n    setCurrentScreen('jarvis');\n    setChatInitialMessage(''); // Clear the initial message\n  };\n\n  // Animation variants\n  const slideVariants = {\n    initial: {\n      y: '100%',\n      opacity: 0\n    },\n    animate: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        damping: 25,\n        stiffness: 120,\n        duration: 0.8\n      }\n    },\n    exit: {\n      y: '-100%',\n      opacity: 0,\n      transition: {\n        type: 'spring',\n        damping: 25,\n        stiffness: 120,\n        duration: 0.6\n      }\n    }\n  };\n  const fadeVariants = {\n    initial: {\n      opacity: 0\n    },\n    animate: {\n      opacity: 1,\n      transition: {\n        duration: 0.5\n      }\n    },\n    exit: {\n      opacity: 0,\n      transition: {\n        duration: 0.3\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App bg-gradient-to-br from-gray-900 to-black min-h-screen text-white relative overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      mode: \"wait\",\n      children: [currentScreen === 'home' && /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: fadeVariants,\n        initial: \"initial\",\n        animate: \"animate\",\n        exit: \"exit\",\n        className: \"absolute inset-0\",\n        children: /*#__PURE__*/_jsxDEV(HomeScreen, {\n          onEnterJarvis: handleEnterJarvis\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this)\n      }, \"home\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this), currentScreen === 'jarvis' && /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: fadeVariants,\n        initial: \"initial\",\n        animate: \"animate\",\n        exit: \"exit\",\n        className: \"absolute inset-0\",\n        children: /*#__PURE__*/_jsxDEV(Jarvis, {\n          onExitToHome: handleExitToHome,\n          onEnterChat: handleEnterChat\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)\n      }, \"jarvis\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), currentScreen === 'chat' && /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: slideVariants,\n        initial: \"initial\",\n        animate: \"animate\",\n        exit: \"exit\",\n        className: \"absolute inset-0\",\n        children: /*#__PURE__*/_jsxDEV(ChatScreen, {\n          onExitToHome: handleExitToJarvis,\n          initialMessage: chatInitialMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this)\n      }, \"chat\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"vUiN7i94uhDNKfPoAgxAShj/vg8=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "motion", "AnimatePresence", "<PERSON>", "HomeScreen", "ChatScreen", "jsxDEV", "_jsxDEV", "App", "_s", "currentScreen", "setCurrentScreen", "chatInitialMessage", "setChatInitialMessage", "handleE<PERSON><PERSON><PERSON><PERSON>", "handleExitToHome", "handleEnterChat", "initialMessage", "handleExitToJarvis", "slideVariants", "initial", "y", "opacity", "animate", "transition", "type", "damping", "stiffness", "duration", "exit", "fadeVariants", "className", "children", "mode", "div", "variants", "on<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onExitToHome", "onEnterChat", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/App.js"], "sourcesContent": ["import { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport <PERSON> from './components/Jarvis';\nimport HomeScreen from './components/HomeScreen';\nimport ChatScreen from './components/ChatScreen';\n\nfunction App() {\n  const [currentScreen, setCurrentScreen] = useState('home'); // 'home', 'jarvis', 'chat'\n  const [chatInitialMessage, setChatInitialMessage] = useState('');\n\n  const handleEnterJarvis = () => {\n    setCurrentScreen('jarvis');\n  };\n\n  const handleExitToHome = () => {\n    setCurrentScreen('home');\n  };\n\n  const handleEnterChat = (initialMessage = '') => {\n    setChatInitialMessage(initialMessage);\n    setCurrentScreen('chat');\n  };\n\n  const handleExitToJarvis = () => {\n    setCurrentScreen('jarvis');\n    setChatInitialMessage(''); // Clear the initial message\n  };\n\n  // Animation variants\n  const slideVariants = {\n    initial: {\n      y: '100%',\n      opacity: 0\n    },\n    animate: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        damping: 25,\n        stiffness: 120,\n        duration: 0.8\n      }\n    },\n    exit: {\n      y: '-100%',\n      opacity: 0,\n      transition: {\n        type: 'spring',\n        damping: 25,\n        stiffness: 120,\n        duration: 0.6\n      }\n    }\n  };\n\n  const fadeVariants = {\n    initial: { opacity: 0 },\n    animate: {\n      opacity: 1,\n      transition: { duration: 0.5 }\n    },\n    exit: {\n      opacity: 0,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  return (\n    <div className=\"App bg-gradient-to-br from-gray-900 to-black min-h-screen text-white relative overflow-hidden\">\n\n      <AnimatePresence mode=\"wait\">\n        {currentScreen === 'home' && (\n          <motion.div\n            key=\"home\"\n            variants={fadeVariants}\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            className=\"absolute inset-0\"\n          >\n            <HomeScreen onEnterJarvis={handleEnterJarvis} />\n          </motion.div>\n        )}\n\n        {currentScreen === 'jarvis' && (\n          <motion.div\n            key=\"jarvis\"\n            variants={fadeVariants}\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            className=\"absolute inset-0\"\n          >\n            <Jarvis onExitToHome={handleExitToHome} onEnterChat={handleEnterChat} />\n          </motion.div>\n        )}\n\n        {currentScreen === 'chat' && (\n          <motion.div\n            key=\"chat\"\n            variants={slideVariants}\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            className=\"absolute inset-0\"\n          >\n            <ChatScreen onExitToHome={handleExitToJarvis} initialMessage={chatInitialMessage} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACY,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMc,iBAAiB,GAAGA,CAAA,KAAM;IAC9BH,gBAAgB,CAAC,QAAQ,CAAC;EAC5B,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC7BJ,gBAAgB,CAAC,MAAM,CAAC;EAC1B,CAAC;EAED,MAAMK,eAAe,GAAGA,CAACC,cAAc,GAAG,EAAE,KAAK;IAC/CJ,qBAAqB,CAACI,cAAc,CAAC;IACrCN,gBAAgB,CAAC,MAAM,CAAC;EAC1B,CAAC;EAED,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAC/BP,gBAAgB,CAAC,QAAQ,CAAC;IAC1BE,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMM,aAAa,GAAG;IACpBC,OAAO,EAAE;MACPC,CAAC,EAAE,MAAM;MACTC,OAAO,EAAE;IACX,CAAC;IACDC,OAAO,EAAE;MACPF,CAAC,EAAE,CAAC;MACJC,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,GAAG;QACdC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDC,IAAI,EAAE;MACJR,CAAC,EAAE,OAAO;MACVC,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,GAAG;QACdC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EAED,MAAME,YAAY,GAAG;IACnBV,OAAO,EAAE;MAAEE,OAAO,EAAE;IAAE,CAAC;IACvBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEI,QAAQ,EAAE;MAAI;IAC9B,CAAC;IACDC,IAAI,EAAE;MACJP,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEI,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,oBACErB,OAAA;IAAKwB,SAAS,EAAC,+FAA+F;IAAAC,QAAA,eAE5GzB,OAAA,CAACL,eAAe;MAAC+B,IAAI,EAAC,MAAM;MAAAD,QAAA,GACzBtB,aAAa,KAAK,MAAM,iBACvBH,OAAA,CAACN,MAAM,CAACiC,GAAG;QAETC,QAAQ,EAAEL,YAAa;QACvBV,OAAO,EAAC,SAAS;QACjBG,OAAO,EAAC,SAAS;QACjBM,IAAI,EAAC,MAAM;QACXE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAE5BzB,OAAA,CAACH,UAAU;UAACgC,aAAa,EAAEtB;QAAkB;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAP5C,MAAM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQA,CACb,EAEA9B,aAAa,KAAK,QAAQ,iBACzBH,OAAA,CAACN,MAAM,CAACiC,GAAG;QAETC,QAAQ,EAAEL,YAAa;QACvBV,OAAO,EAAC,SAAS;QACjBG,OAAO,EAAC,SAAS;QACjBM,IAAI,EAAC,MAAM;QACXE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAE5BzB,OAAA,CAACJ,MAAM;UAACsC,YAAY,EAAE1B,gBAAiB;UAAC2B,WAAW,EAAE1B;QAAgB;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAPpE,QAAQ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQF,CACb,EAEA9B,aAAa,KAAK,MAAM,iBACvBH,OAAA,CAACN,MAAM,CAACiC,GAAG;QAETC,QAAQ,EAAEhB,aAAc;QACxBC,OAAO,EAAC,SAAS;QACjBG,OAAO,EAAC,SAAS;QACjBM,IAAI,EAAC,MAAM;QACXE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAE5BzB,OAAA,CAACF,UAAU;UAACoC,YAAY,EAAEvB,kBAAmB;UAACD,cAAc,EAAEL;QAAmB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAPhF,MAAM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQA,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV;AAAC/B,EAAA,CA3GQD,GAAG;AAAAmC,EAAA,GAAHnC,GAAG;AA6GZ,eAAeA,GAAG;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}