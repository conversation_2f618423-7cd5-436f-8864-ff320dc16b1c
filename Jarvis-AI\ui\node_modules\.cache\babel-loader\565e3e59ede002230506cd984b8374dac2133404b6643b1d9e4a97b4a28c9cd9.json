{"ast": null, "code": "import { velocityPerSecond } from '../../../utils/velocity-per-second.mjs';\nconst velocitySampleDuration = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n  const prevT = Math.max(t - velocitySampleDuration, 0);\n  return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\nexport { calcGeneratorVelocity };", "map": {"version": 3, "names": ["velocityPerSecond", "velocitySampleDuration", "calcGeneratorVelocity", "resolveValue", "t", "current", "prevT", "Math", "max"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/animation/generators/utils/velocity.mjs"], "sourcesContent": ["import { velocityPerSecond } from '../../../utils/velocity-per-second.mjs';\n\nconst velocitySampleDuration = 5; // ms\nfunction calcGeneratorVelocity(resolveValue, t, current) {\n    const prevT = Math.max(t - velocitySampleDuration, 0);\n    return velocityPerSecond(current - resolveValue(prevT), t - prevT);\n}\n\nexport { calcGeneratorVelocity };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wCAAwC;AAE1E,MAAMC,sBAAsB,GAAG,CAAC,CAAC,CAAC;AAClC,SAASC,qBAAqBA,CAACC,YAAY,EAAEC,CAAC,EAAEC,OAAO,EAAE;EACrD,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,CAAC,GAAGH,sBAAsB,EAAE,CAAC,CAAC;EACrD,OAAOD,iBAAiB,CAACK,OAAO,GAAGF,YAAY,CAACG,KAAK,CAAC,EAAEF,CAAC,GAAGE,KAAK,CAAC;AACtE;AAEA,SAASJ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}