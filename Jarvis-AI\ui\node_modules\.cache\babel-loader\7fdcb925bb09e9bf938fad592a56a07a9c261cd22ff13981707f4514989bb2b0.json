{"ast": null, "code": "import{useState}from'react';import{motion,AnimatePresence}from'framer-motion';import <PERSON> from'./components/Jarvis';import HomeScreen from'./components/HomeScreen';import ChatScreen from'./components/ChatScreen';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[currentScreen,setCurrentScreen]=useState('home');// 'home', 'jarvis', 'chat'\nconst[chatInitialMessage,setChatInitialMessage]=useState('');const handleEnterJarvis=()=>{setCurrentScreen('jarvis');};const handleExitToHome=()=>{setCurrentScreen('home');};const handleEnterChat=function(){let initialMessage=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'';setChatInitialMessage(initialMessage);setCurrentScreen('chat');};const handleExitToJarvis=()=>{setCurrentScreen('jarvis');setChatInitialMessage('');// Clear the initial message\n};// Animation variants\nconst slideVariants={initial:{y:'100%',opacity:0},animate:{y:0,opacity:1,transition:{type:'spring',damping:25,stiffness:120,duration:0.8}},exit:{y:'-100%',opacity:0,transition:{type:'spring',damping:25,stiffness:120,duration:0.6}}};const fadeVariants={initial:{opacity:0},animate:{opacity:1,transition:{duration:0.5}},exit:{opacity:0,transition:{duration:0.3}}};return/*#__PURE__*/_jsx(\"div\",{className:\"App bg-gradient-to-br from-gray-900 to-black min-h-screen text-white relative overflow-hidden\",children:/*#__PURE__*/_jsxs(AnimatePresence,{mode:\"wait\",children:[currentScreen==='home'&&/*#__PURE__*/_jsx(motion.div,{variants:fadeVariants,initial:\"initial\",animate:\"animate\",exit:\"exit\",className:\"absolute inset-0\",children:/*#__PURE__*/_jsx(HomeScreen,{onEnterJarvis:handleEnterJarvis})},\"home\"),currentScreen==='jarvis'&&/*#__PURE__*/_jsx(motion.div,{variants:fadeVariants,initial:\"initial\",animate:\"animate\",exit:\"exit\",className:\"absolute inset-0\",children:/*#__PURE__*/_jsx(Jarvis,{onExitToHome:handleExitToHome,onEnterChat:handleEnterChat})},\"jarvis\"),currentScreen==='chat'&&/*#__PURE__*/_jsx(motion.div,{variants:slideVariants,initial:\"initial\",animate:\"animate\",exit:\"exit\",className:\"absolute inset-0\",children:/*#__PURE__*/_jsx(ChatScreen,{onExitToHome:handleExitToJarvis,initialMessage:chatInitialMessage})},\"chat\")]})});}export default App;", "map": {"version": 3, "names": ["useState", "motion", "AnimatePresence", "<PERSON>", "HomeScreen", "ChatScreen", "jsx", "_jsx", "jsxs", "_jsxs", "App", "currentScreen", "setCurrentScreen", "chatInitialMessage", "setChatInitialMessage", "handleE<PERSON><PERSON><PERSON><PERSON>", "handleExitToHome", "handleEnterChat", "initialMessage", "arguments", "length", "undefined", "handleExitToJarvis", "slideVariants", "initial", "y", "opacity", "animate", "transition", "type", "damping", "stiffness", "duration", "exit", "fadeVariants", "className", "children", "mode", "div", "variants", "on<PERSON><PERSON><PERSON><PERSON><PERSON>", "onExitToHome", "onEnterChat"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/App.js"], "sourcesContent": ["import { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport <PERSON> from './components/Jarvis';\nimport HomeScreen from './components/HomeScreen';\nimport ChatScreen from './components/ChatScreen';\n\nfunction App() {\n  const [currentScreen, setCurrentScreen] = useState('home'); // 'home', 'jarvis', 'chat'\n  const [chatInitialMessage, setChatInitialMessage] = useState('');\n\n  const handleEnterJarvis = () => {\n    setCurrentScreen('jarvis');\n  };\n\n  const handleExitToHome = () => {\n    setCurrentScreen('home');\n  };\n\n  const handleEnterChat = (initialMessage = '') => {\n    setChatInitialMessage(initialMessage);\n    setCurrentScreen('chat');\n  };\n\n  const handleExitToJarvis = () => {\n    setCurrentScreen('jarvis');\n    setChatInitialMessage(''); // Clear the initial message\n  };\n\n  // Animation variants\n  const slideVariants = {\n    initial: {\n      y: '100%',\n      opacity: 0\n    },\n    animate: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: 'spring',\n        damping: 25,\n        stiffness: 120,\n        duration: 0.8\n      }\n    },\n    exit: {\n      y: '-100%',\n      opacity: 0,\n      transition: {\n        type: 'spring',\n        damping: 25,\n        stiffness: 120,\n        duration: 0.6\n      }\n    }\n  };\n\n  const fadeVariants = {\n    initial: { opacity: 0 },\n    animate: {\n      opacity: 1,\n      transition: { duration: 0.5 }\n    },\n    exit: {\n      opacity: 0,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  return (\n    <div className=\"App bg-gradient-to-br from-gray-900 to-black min-h-screen text-white relative overflow-hidden\">\n\n      <AnimatePresence mode=\"wait\">\n        {currentScreen === 'home' && (\n          <motion.div\n            key=\"home\"\n            variants={fadeVariants}\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            className=\"absolute inset-0\"\n          >\n            <HomeScreen onEnterJarvis={handleEnterJarvis} />\n          </motion.div>\n        )}\n\n        {currentScreen === 'jarvis' && (\n          <motion.div\n            key=\"jarvis\"\n            variants={fadeVariants}\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            className=\"absolute inset-0\"\n          >\n            <Jarvis onExitToHome={handleExitToHome} onEnterChat={handleEnterChat} />\n          </motion.div>\n        )}\n\n        {currentScreen === 'chat' && (\n          <motion.div\n            key=\"chat\"\n            variants={slideVariants}\n            initial=\"initial\"\n            animate=\"animate\"\n            exit=\"exit\"\n            className=\"absolute inset-0\"\n          >\n            <ChatScreen onExitToHome={handleExitToJarvis} initialMessage={chatInitialMessage} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n\nexport default App;"], "mappings": "AAAA,OAASA,QAAQ,KAAQ,OAAO,CAChC,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGZ,QAAQ,CAAC,MAAM,CAAC,CAAE;AAC5D,KAAM,CAACa,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAAe,iBAAiB,CAAGA,CAAA,GAAM,CAC9BH,gBAAgB,CAAC,QAAQ,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAGA,CAAA,GAAM,CAC7BJ,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAK,eAAe,CAAG,QAAAA,CAAA,CAAyB,IAAxB,CAAAC,cAAc,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC1CL,qBAAqB,CAACI,cAAc,CAAC,CACrCN,gBAAgB,CAAC,MAAM,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAU,kBAAkB,CAAGA,CAAA,GAAM,CAC/BV,gBAAgB,CAAC,QAAQ,CAAC,CAC1BE,qBAAqB,CAAC,EAAE,CAAC,CAAE;AAC7B,CAAC,CAED;AACA,KAAM,CAAAS,aAAa,CAAG,CACpBC,OAAO,CAAE,CACPC,CAAC,CAAE,MAAM,CACTC,OAAO,CAAE,CACX,CAAC,CACDC,OAAO,CAAE,CACPF,CAAC,CAAE,CAAC,CACJC,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CACVC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,GACZ,CACF,CAAC,CACDC,IAAI,CAAE,CACJR,CAAC,CAAE,OAAO,CACVC,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CACVC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,GACZ,CACF,CACF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,CACnBV,OAAO,CAAE,CAAEE,OAAO,CAAE,CAAE,CAAC,CACvBC,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CAAEI,QAAQ,CAAE,GAAI,CAC9B,CAAC,CACDC,IAAI,CAAE,CACJP,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CAAEI,QAAQ,CAAE,GAAI,CAC9B,CACF,CAAC,CAED,mBACEzB,IAAA,QAAK4B,SAAS,CAAC,+FAA+F,CAAAC,QAAA,cAE5G3B,KAAA,CAACP,eAAe,EAACmC,IAAI,CAAC,MAAM,CAAAD,QAAA,EACzBzB,aAAa,GAAK,MAAM,eACvBJ,IAAA,CAACN,MAAM,CAACqC,GAAG,EAETC,QAAQ,CAAEL,YAAa,CACvBV,OAAO,CAAC,SAAS,CACjBG,OAAO,CAAC,SAAS,CACjBM,IAAI,CAAC,MAAM,CACXE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAE5B7B,IAAA,CAACH,UAAU,EAACoC,aAAa,CAAEzB,iBAAkB,CAAE,CAAC,EAP5C,MAQM,CACb,CAEAJ,aAAa,GAAK,QAAQ,eACzBJ,IAAA,CAACN,MAAM,CAACqC,GAAG,EAETC,QAAQ,CAAEL,YAAa,CACvBV,OAAO,CAAC,SAAS,CACjBG,OAAO,CAAC,SAAS,CACjBM,IAAI,CAAC,MAAM,CACXE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAE5B7B,IAAA,CAACJ,MAAM,EAACsC,YAAY,CAAEzB,gBAAiB,CAAC0B,WAAW,CAAEzB,eAAgB,CAAE,CAAC,EAPpE,QAQM,CACb,CAEAN,aAAa,GAAK,MAAM,eACvBJ,IAAA,CAACN,MAAM,CAACqC,GAAG,EAETC,QAAQ,CAAEhB,aAAc,CACxBC,OAAO,CAAC,SAAS,CACjBG,OAAO,CAAC,SAAS,CACjBM,IAAI,CAAC,MAAM,CACXE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAE5B7B,IAAA,CAACF,UAAU,EAACoC,YAAY,CAAEnB,kBAAmB,CAACJ,cAAc,CAAEL,kBAAmB,CAAE,CAAC,EAPhF,MAQM,CACb,EACc,CAAC,CACf,CAAC,CAEV,CAEA,cAAe,CAAAH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}