import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ExpandableSidebar from './ui/ExpandableSidebar';
import MinimizedJarvis from './ui/MinimizedJarvis';
import { useJarvis } from '../hooks/useJarvis';

const ChatScreen = ({ onExitToHome, initialMessage = '' }) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const messagesContainerRef = useRef(null);

  // Jarvis API integration
  const {
    state,
    isProcessing,
    isServerConnected,
    error,
    sendMessage,
    speakText,
    conversationHistory,
    clearHistory
  } = useJarvis();

  // Add initial message if provided and send to backend
  useEffect(() => {
    if (initialMessage.trim()) {
      const userMessage = {
        id: Date.now(),
        text: initialMessage,
        sender: 'user',
        timestamp: new Date()
      };

      setMessages([userMessage]);

      // Send to backend and get real response
      const processInitialMessage = async () => {
        try {
          setIsTyping(true);
          const response = await sendMessage(initialMessage);

          setMessages(prev => [...prev, {
            id: Date.now() + 1,
            text: response.response,
            sender: 'jarvis',
            timestamp: new Date(response.timestamp)
          }]);
        } catch (error) {
          console.error('Failed to process initial message:', error);
          setMessages(prev => [...prev, {
            id: Date.now() + 1,
            text: "I'm sorry, I'm having trouble connecting to my backend systems. Please try again.",
            sender: 'jarvis',
            timestamp: new Date()
          }]);
        } finally {
          setIsTyping(false);
        }
      };

      processInitialMessage();
    }
  }, [initialMessage, sendMessage]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSendMessage = async () => {
    if (!inputText.trim() || !isServerConnected) return;

    const messageText = inputText.trim();
    const newMessage = {
      id: Date.now(),
      text: messageText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputText('');

    // Send to backend
    try {
      setIsTyping(true);
      const response = await sendMessage(messageText);

      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        text: response.response,
        sender: 'jarvis',
        timestamp: new Date(response.timestamp)
      }]);
    } catch (error) {
      console.error('Failed to send message:', error);
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        text: "I'm sorry, I encountered an error processing your request. Please try again.",
        sender: 'jarvis',
        timestamp: new Date()
      }]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Scroll functionality
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    setShowScrollButton(!isNearBottom && messages.length > 0);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Animation variants
  const messageVariants = {
    hidden: { opacity: 0, y: 10, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.3 }
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-gray-900 to-black text-white relative">
      {/* Connection Status Indicator */}
      {!isServerConnected && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-20">
          <div className="bg-red-900/80 border border-red-500/50 rounded-lg px-4 py-2">
            <span className="text-sm text-red-300">Backend Disconnected - Chat functionality limited</span>
          </div>
        </div>
      )}

      {/* Expandable Sidebar - Left Side */}
      <div className="fixed left-0 top-1/2 transform -translate-y-1/2 z-10">
        <ExpandableSidebar />
      </div>

      {/* Minimized JARVIS - Top Right */}
      <MinimizedJarvis
        onExpand={onExitToHome}
        isProcessing={isProcessing}
      />

      {/* Header - JARVIS Style */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-cyan-500/20">
        {/* Left side - Title with dropdown */}
        <div className="flex items-center space-x-2">
          <div className="relative">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="flex items-center space-x-2 text-white hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors"
            >
              <span className="font-semibold">JARVIS</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {showDropdown && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-filter backdrop-blur-12 rounded-lg shadow-lg border border-cyan-500/30 z-50">
                <div className="py-1">
                  <button className="w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors">
                    New Chat
                  </button>
                  <button className="w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors">
                    Chat History
                  </button>
                  <button className="w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors">
                    Settings
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Share button and menu */}
        <div className="flex items-center space-x-2">
          <button className="flex items-center space-x-2 bg-gradient-to-r from-cyan-600/20 to-blue-600/20 hover:from-cyan-500/30 hover:to-blue-500/30 border border-cyan-500/30 px-3 py-2 rounded-lg transition-all duration-300 text-cyan-100 hover:text-cyan-400">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            <span className="text-sm">Share</span>
          </button>

          <button
            onClick={onExitToHome}
            className="p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400"
            title="Return to JARVIS"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </button>

          <button className="p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Messages Container - ChatGPT Style */}
      <div className="flex-1 overflow-y-auto relative" onScroll={handleScroll} ref={messagesContainerRef}>
        <div className="max-w-3xl mx-auto">
          {messages.length === 0 ? (
            /* Welcome Message */
            <div className="flex flex-col items-center justify-center h-full text-center py-20">
              <div className="mb-8">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 rounded-full bg-cyan-400"></div>
                </div>
                <h2 className="text-2xl font-semibold text-gray-300 mb-2">How can I help you today?</h2>
                <p className="text-gray-500">I'm JARVIS, your AI assistant. Ask me anything!</p>
              </div>
            </div>
          ) : (
            <div className="py-8">
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    variants={messageVariants}
                    initial="hidden"
                    animate="visible"
                    className={`group mb-8 ${message.sender === 'user' ? 'ml-auto' : ''}`}
                  >
                    <div className="flex items-start space-x-4 px-4">
                      {message.sender === 'jarvis' && (
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center">
                            <div className="w-4 h-4 rounded-full bg-cyan-400"></div>
                          </div>
                        </div>
                      )}

                      <div className={`flex-1 ${message.sender === 'user' ? 'max-w-2xl ml-auto' : 'max-w-2xl'}`}>
                        {message.sender === 'user' && (
                          <div className="text-right mb-2">
                            <span className="text-sm text-gray-400">You</span>
                          </div>
                        )}

                        <div className={`${message.sender === 'user'
                          ? 'bg-gray-700 text-white rounded-2xl px-4 py-3 ml-auto inline-block max-w-fit'
                          : 'text-gray-100'
                          }`}>
                          <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.text}</p>
                        </div>

                        <div className={`mt-2 ${message.sender === 'user' ? 'text-right' : 'text-left'}`}>
                          <span className="text-xs text-gray-500">
                            {message.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                      </div>

                      {message.sender === 'user' && (
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
                            <span className="text-sm font-medium">U</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Typing indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-8 px-4"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center">
                        <div className="w-4 h-4 rounded-full bg-cyan-400"></div>
                      </div>
                    </div>
                    <div className="flex-1 max-w-2xl">
                      <div className="flex space-x-1 py-2">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Scroll to bottom button */}
        {showScrollButton && (
          <div className="absolute bottom-4 right-4">
            <button
              onClick={scrollToBottom}
              className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow-lg transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Input Area - JARVIS Style */}
      <div className="flex justify-center p-6">
        <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>
          <input
            ref={inputRef}
            type="text"
            className="input"
            placeholder="Type something intelligent (Press Enter to chat)"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onFocus={() => setIsInputFocused(true)}
            onBlur={() => setIsInputFocused(false)}
            onKeyDown={handleKeyPress}
          />
          <div className="input-border"></div>
        </div>
      </div>
    </div>
  );
};

export default ChatScreen;
