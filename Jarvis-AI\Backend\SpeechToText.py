from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from dotenv import dotenv_values
import os
import mtranslate as mt

env_vars = dotenv_values(".env")

InputLanguage = env_vars.get("InputLanguage")

HtmlCode = '''<!DOCTYPE html>
<html lang="en">
<head>
    <title>Speech Recognition</title>
</head>
<body>
    <button id="start" onclick="startRecognition()">Start Recognition</button>
    <button id="end" onclick="stopRecognition()">Stop Recognition</button>
    <p id="output"></p>
    <script>
        const output = document.getElementById('output');
        let recognition;

        function startRecognition() {
            recognition = new webkitSpeechRecognition() || new SpeechRecognition();
            recognition.lang = '';
            recognition.continuous = true;

            recognition.onresult = function(event) {
                const transcript = event.results[event.results.length - 1][0].transcript;
                output.textContent += transcript;
            };

            recognition.onend = function() {
                recognition.start();
            };
            recognition.start();
        }

        function stopRecognition() {
            recognition.stop();
            output.innerHTML = "";
        }
    </script>
</body>
</html>'''

HtmlCode = str(HtmlCode).replace("recognition.lang = '';", f"recognition.lang = '{InputLanguage}';")

# Create Data directory if it doesn't exist
os.makedirs("Data", exist_ok=True)

with open(r"Data\Voice.html", "w") as f:
    f.write(HtmlCode)

current_dir = os.getcwd()

Link = f"{current_dir}/Data/Voice.html"

def initialize_webdriver():
    """Initialize WebDriver with Edge browser"""
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36"

    try:
        print("Using Edge browser...")
        edge_options = EdgeOptions()
        edge_options.add_argument(f'user-agent={user_agent}')
        edge_options.add_argument("--use-fake-ui-for-media-stream")
        edge_options.add_argument("--use-fake-device-for-media-stream")
        edge_options.add_argument("--headless=new")
        edge_options.add_argument("--no-sandbox")
        edge_options.add_argument("--disable-dev-shm-usage")

        service = EdgeService(EdgeChromiumDriverManager().install())
        return webdriver.Edge(service=service, options=edge_options)
    except Exception as e:
        print(f"Edge failed: {e}")
        print("Please ensure Microsoft Edge is installed on your system.")
        exit(1)

driver = initialize_webdriver()

TempDirPath = rf"{current_dir}/Frontend/Files"

def SetAssistantStatus(Status):
    with open(rf'{TempDirPath}/Status.data', "w", encoding='utf-8') as file:
        file.write(Status)

def QueryModifier(Query):
    new_query = Query.lower().strip()
    query_words = new_query.split()
    question_words = ["what", "where", "when", "how", "why", "which", "who", "whom", "whose", "can you", "what's", "where's", "how's", "are you", "can i", "am i"]

    if any(word + " " in new_query for word in question_words):
        if query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "?"
        else:
            new_query += "?"
    else:

        if query_words[-1][-1] not in ['.', '?', '!']:
            new_query = new_query[:-1] + "."
        else:
            new_query += "."
        
    return new_query.capitalize()

def UniversalTranslator(Text):
    english_translation = mt.translate(Text, "en", "auto")
    return english_translation.capitalize()

def SpeechRecognition():

    driver.get("file:///" + Link)

    driver.find_element(by=By.ID, value="start").click()

    while True:
        try:
            
            Text = driver.find_element(by=By.ID, value="output").text

            if Text:

                driver.find_element(by=By.ID, value="end").click()

                if InputLanguage and (InputLanguage.lower() == "en" or "en" in InputLanguage.lower()):
                    return QueryModifier(Text)
                else:

                    SetAssistantStatus("Translating... ")
                    return QueryModifier(UniversalTranslator(Text))
        except Exception as e:
            pass
    
if __name__ == "__main__":
    while True:

        Text = SpeechRecognition()
        print(Text)