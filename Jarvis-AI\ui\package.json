{"name": "jarvis-ui", "version": "1.0.0", "private": true, "homepage": "./", "main": "public/electron.js", "dependencies": {"framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "styled-components": "^6.1.19"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"cross-env BROWSER=none npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "electron-dist": "npm run build && electron-builder --publish=never", "preelectron-pack": "npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "concurrently": "^8.0.1", "cross-env": "^7.0.3", "electron": "^23.0.0", "electron-builder": "^24.6.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "wait-on": "^7.0.1"}, "build": {"appId": "com.jarvis.ai", "productName": "Jarvis AI", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*"], "extraResources": [{"from": "../Backend", "to": "Backend", "filter": ["**/*", "!__pycache__/**/*", "!*.pyc"]}], "win": {"target": "nsis", "icon": "public/assets/icon.ico"}, "mac": {"target": "dmg", "icon": "public/assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "public/assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}