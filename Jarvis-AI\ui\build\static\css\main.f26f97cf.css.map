{"version": 3, "file": "static/css/main.f26f97cf.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CACd,qBAAoB,CAApB,mDAAoB,EAApB,mDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EACpB,wCAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,gBAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,gBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,2CAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,8BAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,4CAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,8BAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,wCAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,wCAAmB,CAAnB,mOAAmB,CAAnB,wCAAmB,CAAnB,uCAAmB,CAAnB,2NAAmB,CAAnB,uCAAmB,CAAnB,wMAAmB,CAAnB,mGAAmB,CAAnB,mEAAmB,EAAnB,4CAAmB,CAAnB,mDAAmB,EAAnB,+DAAmB,CAAnB,+BAAmB,EAAnB,kEAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,wBAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,qCAAmB,CAAnB,gBAAmB,CAAnB,0DAAmB,CAAnB,yCAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,qBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,yEAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,8EAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,4CAAmB,CAAnB,6CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,4CAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,oCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,4CAAmB,CAAnB,6CAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,gFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,gFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,yEAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,8DAAmB,CAAnB,uEAAmB,CAAnB,oEAAmB,CAAnB,0EAAmB,CAAnB,uEAAmB,CAAnB,oEAAmB,CAAnB,oEAAmB,CAAnB,sEAAmB,CAAnB,sEAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,8BAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,8CAAmB,CAAnB,4CAAmB,CAAnB,2CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,uCAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,iDAAmB,CAAnB,8BAAmB,CAAnB,6DAAmB,CAAnB,aAAmB,CAAnB,sDAAmB,CAAnB,uBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,gDAAmB,CAAnB,oCAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,wLAAmB,CAAnB,8CAAmB,CAAnB,iTAAmB,CAAnB,sQAAmB,CAAnB,kMAAmB,CAAnB,6IAAmB,CAAnB,mMAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,+DAAmB,CAAnB,2DAAmB,CAEnB,KAGE,kCAAmC,CACnC,iCAAkC,CAElC,gBAAuB,CAJvB,6CAAuD,CADvD,QAAS,CAIT,eAEF,CAEA,OAEE,iDAA0C,CAA1C,yCAA0C,CAD1C,oBAAiC,CAEjC,0BACF,CAGA,oBACE,SACF,CAEA,0BACE,oBACF,CAEA,0BACE,eAAmB,CACnB,iBACF,CAEA,gCACE,eACF,CAGA,UAGE,kCAA2B,CAA3B,0BAA2B,CAF3B,yCAA0C,CAC1C,2BAA4B,CAE5B,uBAAwB,CACxB,6BACF,CAGA,iBAIE,wBAAyB,CAHzB,oBAAqB,CAErB,uBAEF,CAGA,YAEE,kCAA2B,CAA3B,0BAA2B,CAC3B,kBAAmB,CAFnB,uBAGF,CAGA,WACE,yBAAkC,CAClC,uBAAoC,CACpC,iBAAmB,CACnB,sCAAuC,CACvC,iBAAkB,CAClB,gBAAiB,CACjB,gBAAiB,CACjB,mBAAoB,CAUpB,sBAAuB,CALvB,kBAAmB,CAMnB,uBAAwB,CAJxB,cAAe,CAHf,YAAa,CAIb,cAAe,CAFf,iBAAkB,CAGlB,wBAAiB,CAAjB,gBARF,CAcA,iBACE,YACF,CAGA,WACE,iCAAkC,CAClC,gCAAiC,CACjC,YAAa,CACb,YACF,CAEA,MAGE,4DAAmE,CADnE,WAAY,CADZ,sBAGF,CAEA,MACE,cACF,CAEA,KACE,OACF,CAEA,UACE,YACF,CAGA,0EAGE,4CACF,CAEA,6EAIE,wCAA2C,CAD3C,8CAEF,CAEA,kBAIE,gCAAiC,CAEjC,qBAAsB,CALtB,UAAW,CAQX,WAAY,CAFZ,MAAO,CALP,UAAY,CAGZ,iBAAkB,CAGlB,KAAM,CALN,kBAAmB,CAOnB,UACF,CAEA,wBACE,0BACF,CAEA,2CACE,0CAA2C,CAC3C,wCACF,CAGA,oCACE,sCAAuC,CACvC,8BAA+B,CAC/B,+BACF,CAEA,wCACE,OACF,CAEA,6CACE,YACF,CAEA,yCACE,sBACF,CAEA,8CAEE,cAAc,CAAd,eAAc,CADd,UAEF,CAGA,8DAKE,+BACF,CAGA,oBACE,IAEE,iBAAkB,CADlB,2DAEF,CACA,GAEE,gCAAiC,CADjC,uBAEF,CACF,CAGA,eACE,IACE,oBACF,CACA,IAEE,uCAAwC,CADxC,oBAEF,CACA,GACE,kBACF,CACF,CAGA,MAGE,iDAA0C,CAA1C,yCAA0C,CAD1C,sDAAgF,CAEhF,sBAAwC,CAIxC,kBAAmB,CACnB,oDAA4E,CAH5E,YAAa,CALb,aASF,CAEA,YALE,sBAAuB,CAFvB,iBAqBF,CAdA,MAGE,kBAAmB,CAGnB,iBAAkB,CAOlB,UAAc,CAZd,mBAAoB,CAIpB,WAAY,CAIZ,eAAgB,CAGhB,oBAAqB,CAFrB,4BAA6B,CAC7B,4BAA8B,CAP9B,UAAW,CAIX,SAMF,CAEA,aAYE,8CAAmF,CAPnF,iBAAkB,CAFlB,UAAW,CACX,aAAc,CAGd,WAAY,CANZ,iBAAkB,CAOlB,KAAM,CACN,0BAA2B,CAE3B,6BAA8B,CAD9B,gCAAkC,CAJlC,UAAW,CAJX,UAWF,CAEA,wBAEE,SAAU,CACV,WACF,CAEA,sFAKE,SAAU,CADV,uBAEF,CAEA,WAGE,aAAc,CACd,aAAc,CACd,SAAU,CACV,iBACF,CAEA,0BAPE,WAAY,CADZ,UAWF,CAEA,YAQE,UAAc,CAJd,aAAc,CAKd,gBAAkB,CAClB,eAAgB,CALhB,iBAAkB,CAClB,gBAAiB,CALjB,0BAA2B,CAE3B,6BAA8B,CAD9B,gCAAkC,CAKlC,UAIF,CAGA,mBAME,kBAAmB,CADnB,YAAa,CAEb,QAAS,CAJT,SAAU,CAFV,cAAe,CACf,QAAS,CAET,WAIF,CAEA,0BAGE,cAAe,CADf,WAAY,CAEZ,uBAAyB,CAHzB,UAIF,CAEA,gCACE,qBACF,CAEA,sBAWE,kBAAmB,CARnB,mFAKe,CACf,kBAAmB,CAInB,oDAEoC,CALpC,YAAa,CARb,WAAY,CAUZ,sBAAuB,CAKvB,eAAgB,CADhB,iBAAkB,CAflB,UAiBF,CAEA,6BAOE,oEAGiC,CACjC,kBAAmB,CALnB,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KASF,CAEA,WAKE,kBAAmB,CAJnB,YAAa,CACb,QAAS,CAMT,WAAY,CAFZ,sBAAuB,CAFvB,iBAAkB,CAGlB,UAAW,CAJX,SAMF,CAEA,UAME,8BAA+B,CAH/B,eAAiB,CACjB,kBAAmB,CAFnB,WAAY,CAGZ,kDAAsD,CAJtD,UAMF,CAEA,oBACE,UACE,WACF,CACA,IACE,UACF,CACF,CAEA,iBACE,SACF,CAEA,iBASE,kBAAmB,CAJnB,YAAa,CACb,QAAS,CAIT,sBAAuB,CAPvB,QAAS,CAIT,SAAU,CANV,iBAAkB,CAClB,OAAQ,CAER,8BAAgC,CAIhC,2BAA6B,CAG7B,SACF,CAEA,yBACE,SACF,CAEA,WAEE,WAAY,CADZ,UAEF,CAEA,gBAKE,2CAA4C,CAJ5C,UAAc,CACd,gBAAkB,CAClB,eAAgB,CAChB,8BAA4C,CAE5C,kBACF,CAEA,oBAWE,kCAAoC,CANpC,kCAA2B,CAA3B,0BAA2B,CAD3B,sDAAiF,CAMjF,0BAAwC,CAJxC,kBAAmB,CAGnB,2BAA6C,CAN7C,MAAO,CAKP,eAAgB,CADhB,YAAa,CANb,iBAAkB,CAClB,QAUF,CAEA,aAEE,kBAAmB,CAGnB,iBAAkB,CAGlB,UAAc,CAFd,cAAe,CALf,YAAa,CAQb,gBAAkB,CANlB,QAAS,CACT,gBAAiB,CAGjB,uBAGF,CAEA,mBACE,oBAAkC,CAClC,yBACF,CAEA,WAKE,kBAAmB,CAFnB,iBAAkB,CAClB,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CACvB,WAAY,CANZ,UAOF,CAEA,oBACE,kDACF,CAEA,eACE,kDACF,CAEA,WACE,kDACF,CAEA,eAGE,UAAY,CADZ,WAAY,CADZ,UAGF,CAEA,qBACE,MAAW,UAAc,CACzB,IAAM,SAAY,CACpB,CAEA,uBACE,GACE,SAAU,CACV,sCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,sBAME,kBAAmB,CADnB,YAAa,CAFb,YAAa,CAIb,sBAAuB,CAHvB,eAAgB,CAHhB,iBAAkB,CAClB,WAMF,CAEA,qBAQE,iDAAkD,CADlD,8DAA0E,CAF1E,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAEX,SAGF,CAEA,sBAME,0DAA4D,CAD5D,mCAAqC,CAHrC,WAAY,CAEZ,qBAAsB,CADtB,gBAAiB,CAFjB,UAMF,CAGA,4BAWE,gDAAiD,CAJjD,2LAKF,CAEA,uDAbE,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAoBF,CAXA,2BAUE,sCAAuC,CAHvC,6HAIF,CAEA,mBAYE,iCAA0B,CAA1B,yBAA0B,CAN1B,oEAKC,CAND,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UASF,CAEA,wBAQE,kBAAmB,CAGnB,gCAAkC,CADlC,gBAA8B,CAH9B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAEX,YAMF,CAEA,gBAEE,WAAY,CAEZ,qBAAsB,CADtB,gBAAiB,CAFjB,UAIF,CAEA,yBAKE,kCAAmC,CADnC,mDAAgE,CADhE,iBAAkB,CADlB,YAAa,CADb,WAKF,CAEA,oBAKE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,YAAa,CADb,6BAA8B,CAE9B,YAAa,CAPb,iBAAkB,CAQlB,8BAAgC,CAPhC,UAQF,CAEA,6BACE,SAAU,CACV,mBACF,CAGA,eAEE,eAAgB,CADhB,iBAEF,CAEA,aAME,6BAAoC,CAFpC,6CAAoD,CACpD,4BAA6B,CAE7B,oBAAqB,CAJrB,SAAc,CAFd,gBAAiB,CACjB,eAAiB,CAMjB,mBAAqB,CACrB,mBAAqB,CACrB,8BACF,CAEA,YAUE,qDAAsD,CAPtD,UAAc,CAFd,cAAe,CACf,eAAiB,CAMjB,mBAAqB,CACrB,QAAS,CALT,gEAOF,CAEA,UAKE,iDAAkD,CAHlD,UAAc,CADd,cAAe,CAEf,eAAgB,CAChB,UAEF,CAGA,uBAGE,kBAAmB,CACnB,cAAe,CAHf,YAAa,CACb,qBAAsB,CAGtB,8BACF,CAEA,6BACE,qBACF,CAEA,aAKE,kBAAmB,CADnB,YAAa,CADb,YAAa,CAGb,sBAAuB,CACvB,kBAAmB,CANnB,iBAAkB,CAClB,WAMF,CAEA,WAME,oDAAqD,CADrD,wDAA+E,CAD/E,iBAAkB,CADlB,WAAY,CAFZ,iBAAkB,CAClB,UAKF,CAEA,WASE,kBAAmB,CAHnB,8CAAmF,CACnF,qBAAyB,CAFzB,iBAAkB,CAGlB,YAAa,CAJb,UAAW,CAMX,sBAAuB,CATvB,iBAAkB,CAUlB,8BAAgC,CARhC,SAAU,CADV,SAUF,CAEA,wCACE,iBAAqB,CACrB,6BACF,CAEA,YAGE,SAAU,CACV,WAAe,CACf,cAAe,CAHf,UAAW,CAIX,8BAAgC,CALhC,SAMF,CAEA,YACE,oBAAqB,CACrB,qBAAsB,CACtB,0CACF,CAEA,aACE,oBAAqB,CACrB,qBAAsB,CACtB,8CACF,CAEA,YACE,SAAa,CAEb,oDAAqD,CADrD,UAEF,CAEA,aACE,mBAAoB,CACpB,oBAAqB,CACrB,6CACF,CAEA,YAEE,UAAc,CADd,eAAiB,CAEjB,eAAgB,CAChB,mBAAqB,CACrB,UACF,CAGA,aACE,YAAa,CACb,UAAW,CACX,kBACF,CAEA,WASE,kBAAmB,CAHnB,iDAA0C,CAA1C,yCAA0C,CAD1C,sDAAgF,CAEhF,sBAAwC,CAHxC,kBAAmB,CASnB,+BAA6C,CAF7C,cAAe,CAHf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CATvB,iBAAkB,CAWlB,8BAAgC,CAVhC,UAYF,CAEA,iBAIE,8CAAmF,CAFnF,iBAAqB,CACrB,+BAA6C,CAF7C,sCAIF,CAEA,wBACE,SAAU,CACV,kBACF,CAEA,kBAME,oBAA8B,CAS9B,0BAAwC,CANxC,iBAAkB,CANlB,WAAY,CAFZ,0BAA2B,CAS3B,gBAAkB,CANlB,QAAS,CAQT,SAAU,CAJV,mBAAsB,CAMtB,mBAAoB,CAZpB,iBAAkB,CAGlB,oCAAsC,CAMtC,kBAKF,CAEA,+BAXE,UAAc,CAMd,8BAUF,CALA,aACE,gBAAiB,CACjB,eAGF,CAEA,8BACE,UAAc,CACd,0BACF,CAGA,yBACE,GAAK,UAAc,CACnB,GAAO,SAAY,CACrB,CAEA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAEA,qBACE,GAA0B,SAAU,CAA/B,kBAAiC,CACtC,IAA6B,SAAU,CAAjC,oBAAmC,CACzC,GAA4B,SAAU,CAA/B,kBAAiC,CAC1C,CAEA,qBACE,GAAK,iDAA+E,CACpF,GAAO,6CAA+E,CACxF,CAEA,yBACE,MAAW,UAAc,CACzB,IAAM,SAAY,CACpB,CAaA,oBACE,GAAK,UAAc,CACnB,GAAO,SAAY,CACrB,CAGA,oBACE,GAA0B,UAAY,CAAjC,kBAAmC,CACxC,GAA8B,UAAY,CAAnC,oBAAqC,CAC9C,CAEA,oBACE,GAAK,qBAAwB,CAC7B,IAAM,mBAAsB,CAC5B,GAAO,sBAAyB,CAClC,CAEA,2BACE,GACE,8DACF,CACA,IACE,8DACF,CACA,IACE,8DACF,CACA,IACE,6DACF,CACA,GACE,8DACF,CACF,CAEA,yBACE,MAEE,UAAY,CADZ,oCAEF,CACA,IAEE,UAAY,CADZ,0CAEF,CACA,IAEE,UAAY,CADZ,yCAEF,CACF,CAEA,qBACE,GACE,2BACF,CACA,GACE,0BACF,CACF,CAGA,oBACE,oBAAqB,CACrB,iBAAkB,CAKlB,cAAe,CACf,YAAa,CACb,mCAAqC,CALrC,MAAO,CADP,iBAAkB,CAElB,OAAQ,CACR,WAAY,CAIZ,2BACF,CAEA,gBAEE,kBAAmB,CAQnB,cAAe,CATf,YAAa,CAEb,oBAAqB,CAErB,QAAS,CAMT,WAAY,CAPZ,iBAAkB,CAElB,OAAQ,CACR,2CAA4C,CAE5C,2BAA4B,CAG5B,uBAAyB,CAJzB,SAKF,CAEA,sBACE,SACF,CAEA,uBACE,sDACF,CAEA,sBAQE,wBAAyB,CACzB,oBAAqB,CARrB,UAAW,CAMX,YAAa,CAJb,QAAS,CADT,iBAAkB,CAElB,OAAQ,CACR,2CAA4C,CAK5C,uBAAyB,CAJzB,WAKF,CAEA,4BAEE,YAAa,CADb,2CAEF,CAEA,sBAGE,QAAS,CAFT,SAAU,CAGV,iBAAkB,CAFlB,OAGF,CAEA,0CACE,SACF,CAEA,kDAEE,YAAa,CADb,WAEF,CAEA,2DACE,kBACF,CAEA,uDACE,SAAU,CAGV,sBAAuB,CAFvB,kBAAmB,CACnB,aAEF,CAEA,MASE,kBAAmB,CAFnB,kBAAmB,CACnB,YAAa,CANb,WAAY,CASZ,sBAAuB,CADvB,0BAA2B,CAN3B,2BAA4B,CAE5B,uBAAyB,CALzB,UAAW,CAIX,qBAOF,CAEA,YACE,yDAGF,CAEA,uBAUE,sBAA0C,CAF1C,kBAAmB,CAFnB,WAAY,CAKZ,eAAgB,CAFhB,uBAAyB,CAJzB,UAAW,CAEX,WAKF,CAEA,8BAZE,QAAS,CADT,iBAAkB,CAElB,OAAQ,CACR,2CAgBF,CANA,OAKE,qDACF,CAEA,6BACE,2BACF,CAEA,6BAIE,iBAAkB,CAClB,iBAAkB,CAHlB,WAAY,CACZ,iBAAkB,CAFlB,UAKF,CAEA,oCAIE,wBAAyB,CAFzB,QAAS,CADT,KAAM,CAEN,0BAEF,CAEA,mCAIE,wBAAyB,CAHzB,QAAS,CACT,QAAS,CACT,0BAEF,CAEA,kCAIE,wBAAyB,CAFzB,MAAO,CADP,OAAQ,CAER,0BAEF,CAEA,kCAIE,wBAAyB,CAFzB,OAAQ,CADR,OAAQ,CAER,0BAEF,CAEA,cAIE,kBAAmB,CADnB,YAAa,CADb,YAAa,CAIb,eAAgB,CADhB,uBAAyB,CAJzB,WAMF,CAEA,sBAGE,kCAA2B,CAA3B,0BAA2B,CAD3B,WAAY,CADZ,UAGF,CAEA,MAME,kBAAmB,CAHnB,UAAW,CAEX,YAAa,CAIb,QAAS,CAFT,sBAAuB,CALvB,QAAS,CADT,iBAAkB,CAGlB,0BAOF,CAEA,iBALE,WAAY,CAEZ,uBAWF,CARA,WAKE,0CAA2C,CAF3C,sDAAqF,CAIrF,sBAAwC,CAHxC,iBAAkB,CAHlB,UAOF,CAEA,YAEE,UAAW,CADX,YAAa,CAEb,KACF,CAEA,gBACE,UACF,CAEA,iCACE,YACF,CAEA,kCACE,YACF,CAEA,mBAGE,WAAY,CAEZ,SAAU,CADV,WAAY,CAEZ,mBAAoB,CALpB,iBAAkB,CAOlB,uBAAyB,CADzB,iBAAkB,CALlB,UAOF,CAEA,4BASE,qBAAyB,CALzB,kBAAmB,CAFnB,6BAA8B,CAM9B,eAAgB,CADhB,WAGF,CAEA,0CAXE,YAAa,CAEb,qBAAsB,CAGtB,WAAY,CADZ,UAcF,CAPA,cAGE,SAAW,CACX,YAGF,CAEA,aAEE,kBAAmB,CAOnB,kCAA2B,CAA3B,0BAA2B,CAL3B,sDAAmF,CACnF,sBAAwC,CACxC,kBAAmB,CACnB,cAAe,CANf,YAAa,CAEb,mBAAqB,CAKrB,uBAEF,CAEA,mBACE,8CAAmF,CACnF,iBAAqB,CAErB,+BAA6C,CAD7C,0BAEF,CAEA,kBAGE,UAAW,CAFX,eAAiB,CACjB,eAEF,CAEA,eAKE,8CAA+C,CAF/C,UAAc,CAFd,gBAAkB,CAClB,eAAgB,CAKhB,mBAAoB,CAHpB,6BAA2C,CAE3C,kBAEF,CAEA,wBACE,MACE,UAAY,CACZ,kBACF,CACA,IACE,SAAU,CACV,qBACF,CACF,CAEA,mCACE,GACE,yDACF,CACA,GACE,qDACF,CACF,CAEA,wBACE,IACE,WACF,CACA,IACE,WACF,CACA,IACE,WACF,CACA,IACE,WACF,CACA,IACE,WACF,CACA,GACE,WACF,CACF,CAGA,qGAEE,8HAEF,CACA,qGAEE,6HAEF,CACA,qGAEE,0HAEF,CACA,qGAEE,8HAEF,CACA,qGAEE,+HAEF,CAEA,qGAEE,2GAEF,CACA,mGAEE,0GAEF,CACA,mGAEE,uGAEF,CACA,mGAEE,2GAEF,CACA,mGAEE,4GAEF,CAEA,mGAEE,+GAEF,CACA,mGAEE,8GAEF,CACA,mGAEE,2GAEF,CACA,mGAEE,+GAEF,CACA,iGAEE,gHAEF,CAGA,SAKE,kCAA2B,CAA3B,0BAA2B,CAD3B,sDAAiF,CAMjF,0BAAwC,CAJxC,kBAAmB,CAGnB,+BAAyC,CANzC,MAAO,CAaP,YAAa,CARb,eAAgB,CAGhB,SAAU,CAMV,eAAgB,CAVhB,aAAe,CANf,iBAAkB,CAClB,QAAS,CAWT,qCAAuC,CACvC,0CAAiD,CAFjD,iBAAkB,CAGlB,YAGF,CAEA,cAIE,gBAAiB,CAHjB,SAAU,CAEV,gCAAiC,CADjC,kBAGF,CAEA,cAEE,kBAAmB,CASnB,sBAA6B,CAN7B,iBAAkB,CAGlB,WAA+B,CAF/B,cAAe,CALf,YAAa,CAQb,gBAAkB,CANlB,SAAW,CACX,oBAAuB,CAGvB,uBAAyB,CAGzB,kBAEF,CAEA,oBACE,kDAAmF,CAEnF,kBAAoC,CADpC,UAA2B,CAE3B,yBACF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAGA,OAGE,wBAA6B,CAK7B,WAAY,CACZ,8CAAqD,CACrD,8BAAwC,CALxC,qBAAsB,CAJtB,UAAW,CACX,eAAiB,CAKjB,kBAAoB,CADpB,mBAAqB,CAMrB,iBAAkB,CADlB,uBAAyB,CAPzB,UASF,CAGA,aACE,6BAA2C,CAC3C,qBACF,CAEA,cAEE,oCAAqC,CAKrC,yBAA0B,CAF1B,UAAW,CADX,UAAW,CAEX,QAAS,CAIT,2BAA4B,CAT5B,iBAAkB,CAOlB,oDAA6D,CAL7D,OAAS,CAMT,SAEF,CAEA,aACE,YACF,CAEA,2BACE,uBACF,CAEA,cAEE,sBAAuB,CACvB,+BAAgC,CAChC,mBAAoB,CACpB,+BAAgD,CAChD,uEAAmF,CAenF,oEAGgC,CAGhC,0BAAwC,CAjBxC,iBAAkB,CAKlB,iEAG+B,CAV/B,eAAgB,CAIhB,eAAgB,CADhB,WAAY,CAVZ,iBAAkB,CAQlB,0CAAiD,CAFjD,2BAqBF,CAEA,oBAKE,sBAAoC,CAJpC,6DAKF,CAEA,sBAME,kBAAoC,CAJpC,6DAG+B,CAE/B,qBAAsB,CANtB,oCAOF,CAEA,qBAcE,wCAAyC,CAPzC,mEAC8C,CAC9C,yBAA0B,CAC1B,kBAAmB,CAJnB,WAAY,CALZ,UAAW,CAGX,SAAU,CAQV,SAAU,CAVV,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAUT,2BAA6B,CAF7B,UAIF,CAEA,6BACE,UACF,CAEA,yBACE,GAAK,yBAA6B,CAClC,IAAM,4BAA+B,CACrC,GAAO,yBAA6B,CACtC,CAEA,0BACE,GAGE,gCAAiC,CAFjC,UAAY,CACZ,kBAEF,CACA,IAGE,gCAAiC,CAFjC,SAAU,CACV,qBAEF,CACA,GAGE,gCAAiC,CAFjC,UAAY,CACZ,kBAEF,CACF,CAEA,WAIE,8BAAwC,CAHxC,gBAAiB,CAEjB,kBAAoB,CADpB,kBAGF,CAEA,kBAEE,6DAAyE,CADzE,UAAW,CAEX,gCACF,CAEA,mCACE,UACF,CA7+CA,8BA6+CC,CA7+CD,yCA6+CC,CA7+CD,iBA6+CC,CA7+CD,6OA6+CC,CA7+CD,wCA6+CC,CA7+CD,gBA6+CC,CA7+CD,wDA6+CC,CA7+CD,wDA6+CC,CA7+CD,wDA6+CC,CA7+CD,wDA6+CC,CA7+CD,2CA6+CC,CA7+CD,wBA6+CC,CA7+CD,qDA6+CC,CA7+CD,2CA6+CC,CA7+CD,wBA6+CC,CA7+CD,qDA6+CC,CA7+CD,uDA6+CC,CA7+CD,uFA6+CC,CA7+CD,yDA6+CC,CA7+CD,iEA6+CC,CA7+CD,6FA6+CC,CA7+CD,yDA6+CC,CA7+CD,iEA6+CC,CA7+CD,uFA6+CC,CA7+CD,yDA6+CC,CA7+CD,iEA6+CC,CA7+CD,wFA6+CC,CA7+CD,yDA6+CC,CA7+CD,iEA6+CC,CA7+CD,sFA6+CC,CA7+CD,yDA6+CC,CA7+CD,iEA6+CC,CA7+CD,uFA6+CC,CA7+CD,oFA6+CC,CA7+CD,iFA6+CC,CA7+CD,iFA6+CC,CA7+CD,mFA6+CC,CA7+CD,+CA6+CC,CA7+CD,aA6+CC,CA7+CD,+CA6+CC,CA7+CD,+CA6+CC,CA7+CD,aA6+CC,CA7+CD,+CA6+CC,CA7+CD,+CA6+CC,CA7+CD,aA6+CC,CA7+CD,+CA6+CC,CA7+CD,+CA6+CC,CA7+CD,aA6+CC,CA7+CD,8CA6+CC,CA7+CD,gDA6+CC,CA7+CD,aA6+CC,CA7+CD,+CA6+CC,CA7+CD,iDA6+CC,CA7+CD,aA6+CC,CA7+CD,+CA6+CC,CA7+CD,8CA6+CC,CA7+CD,aA6+CC,CA7+CD,+CA6+CC,CA7+CD,uFA6+CC,CA7+CD,iGA6+CC,CA7+CD,+FA6+CC,CA7+CD,kGA6+CC,CA7+CD,wFA6+CC,CA7+CD,kGA6+CC,CA7+CD,6DA6+CC,CA7+CD,oCA6+CC,CA7+CD,wDA6+CC,CA7+CD,kDA6+CC,CA7+CD,kBA6+CC,CA7+CD,+HA6+CC,CA7+CD,wGA6+CC,CA7+CD,iHA6+CC,CA7+CD,wFA6+CC,CA7+CD,+HA6+CC,CA7+CD,wGA6+CC,CA7+CD,+CA6+CC,CA7+CD,wDA6+CC,CA7+CD,yDA6+CC,CA7+CD,mDA6+CC,CA7+CD,yCA6+CC,CA7+CD,gBA6+CC,CA7+CD,6LA6+CC,CA7+CD,iDA6+CC,CA7+CD,yDA6+CC,CA7+CD,yCA6+CC,CA7+CD,+DA6+CC,CA7+CD,6LA6+CC,CA7+CD,iEA6+CC,CA7+CD,8CA6+CC,CA7+CD,uGA6+CC,CA7+CD,iGA6+CC,CA7+CD,+CA6+CC,CA7+CD,kGA6+CC,CA7+CD,6EA6+CC,CA7+CD,oCA6+CC,CA7+CD,uFA6+CC,CA7+CD,+BA6+CC,CA7+CD,aA6+CC", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\nbody {\n  margin: 0;\n  font-family: 'Segoe UI', 'Roboto', 'Oxygen', sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  overflow: hidden;\n  background: transparent;\n}\n\n.glass {\n  background: rgba(10, 14, 41, 0.7);\n  backdrop-filter: blur(12px) saturate(180%);\n  border: 1px solid rgba(0, 238, 255, 0.1);\n}\n\n/* Sci-Fi Terminal Scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: rgba(0, 30, 60, 0.4);\n}\n\n::-webkit-scrollbar-thumb {\n  background: #00eeff;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #ff00aa;\n}\n\n/* Smooth GIF rendering and transitions */\nimg, video {\n  image-rendering: -webkit-optimize-contrast;\n  image-rendering: crisp-edges;\n  backface-visibility: hidden;\n  transform: translateZ(0);\n  will-change: transform, opacity;\n}\n\n/* Ensure smooth GIF loops */\nimg[src$=\".gif\"] {\n  image-rendering: auto;\n  -webkit-transform: translateZ(0);\n  transform: translateZ(0);\n  animation-fill-mode: both;\n}\n\n/* Hardware acceleration for smooth transitions */\n.motion-div {\n  transform: translateZ(0);\n  backface-visibility: hidden;\n  perspective: 1000px;\n}\n\n/* Custom Mic Button Styles */\n.container {\n  --UnChacked-color: hsl(0, 0%, 10%);\n  --chacked-color: hsl(216, 100%, 60%);\n  --font-color: white;\n  --chacked-font-color: var(--font-color);\n  --icon-size: 1.5em;\n  --anim-time: 0.2s;\n  --anim-scale: 0.1;\n  --base-radius: 0.8em;\n}\n\n.container {\n  display: flex;\n  align-items: center;\n  position: relative;\n  cursor: pointer;\n  font-size: 20px;\n  user-select: none;\n  fill: var(--font-color);\n  color: var(--font-color);\n}\n\n/* Hide the default checkbox */\n.container input {\n  display: none;\n}\n\n/* Base custom checkbox */\n.checkmark {\n  background: var(--UnChacked-color);\n  border-radius: var(--base-radius);\n  display: flex;\n  padding: 0.5em;\n}\n\n.icon {\n  width: var(--icon-size);\n  height: auto;\n  filter: drop-shadow(0px 2px var(--base-radius) rgba(0, 0, 0, 0.25));\n}\n\n.name {\n  margin: 0 0.25em;\n}\n\n.Yes {\n  width: 0;\n}\n\n.name.Yes {\n  display: none;\n}\n\n/* action custom checkbox */\n.container:hover .checkmark,\n.container:hover .icon,\n.container:hover .name {\n  transform: scale(calc(1 + var(--anim-scale)));\n}\n\n.container:active .checkmark,\n.container:active .icon,\n.container:active .name {\n  transform: scale(calc(1 - var(--anim-scale) / 2));\n  border-radius: calc(var(--base-radius) * 2);\n}\n\n.checkmark::before {\n  content: \"\";\n  opacity: 0.5;\n  transform: scale(1);\n  border-radius: var(--base-radius);\n  position: absolute;\n  box-sizing: border-box;\n  left: 0;\n  top: 0;\n  height: 100%;\n  width: 100%;\n}\n\n.checkmark:hover:before {\n  background-color: hsla(0, 0%, 50%, 0.2);\n}\n\n.container input:checked + .checkmark:before {\n  animation: boon calc(var(--anim-time)) ease;\n  animation-delay: calc(var(--anim-time) / 2);\n}\n\n/* When the checkbox is checked*/\n.container input:checked + .checkmark {\n  --UnChacked-color: var(--chacked-color);\n  fill: var(--chacked-font-color);\n  color: var(--chacked-font-color);\n}\n\n.container input:checked ~ .checkmark .No {\n  width: 0;\n}\n\n.container input:checked ~ .checkmark .name.No {\n  display: none;\n}\n\n.container input:checked ~ .checkmark .Yes {\n  width: var(--icon-size);\n}\n\n.container input:checked ~ .checkmark .name.Yes {\n  width: auto;\n  display: unset;\n}\n\n/*Animation*/\n.container,\n.checkmark,\n.checkmark:after,\n.icon,\n.checkmark .name {\n  transition: all var(--anim-time);\n}\n\n/*Unuse*/\n@keyframes icon-rot {\n  50% {\n    transform: rotateZ(180deg) scale(calc(1 - var(--anim-scale)));\n    border-radius: 1em;\n  }\n  to {\n    transform: rotate(360deg);\n    border-radius: var(--base-radius);\n  }\n}\n\n/*Unuse*/\n@keyframes boo {\n  80% {\n    transform: scale(1.4);\n  }\n  99% {\n    transform: scale(1.7);\n    border: 2px solid var(--UnChacked-color);\n  }\n  to {\n    transform: scale(0);\n  }\n}\n\n/* Menu Styles */\n.menu {\n  padding: 0.3rem;\n  background: linear-gradient(135deg, rgba(10, 14, 41, 0.9), rgba(0, 30, 60, 0.8));\n  backdrop-filter: blur(12px) saturate(180%);\n  border: 1px solid rgba(0, 238, 255, 0.2);\n  position: relative;\n  display: flex;\n  justify-content: center;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 238, 255, 0.1), 0 4px 16px rgba(0, 0, 0, 0.3);\n}\n\n.link {\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  width: 45px;\n  height: 35px;\n  border-radius: 6px;\n  position: relative;\n  z-index: 1;\n  overflow: hidden;\n  transform-origin: center left;\n  transition: width 0.2s ease-in;\n  text-decoration: none;\n  color: #00eeff;\n}\n\n.link:before {\n  position: absolute;\n  z-index: -1;\n  content: \"\";\n  display: block;\n  border-radius: 6px;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  transform: translateX(100%);\n  transition: transform 0.2s ease-in;\n  transform-origin: center right;\n  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));\n}\n\n.link:hover,\n.link:focus {\n  outline: 0;\n  width: 100px;\n}\n\n.link:hover:before,\n.link:hover .link-title,\n.link:focus:before,\n.link:focus .link-title {\n  transform: translateX(0);\n  opacity: 1;\n}\n\n.link-icon {\n  width: 20px;\n  height: 20px;\n  display: block;\n  flex-shrink: 0;\n  left: 12px;\n  position: absolute;\n}\n\n.link-icon svg {\n  width: 20px;\n  height: 20px;\n}\n\n.link-title {\n  transform: translateX(100%);\n  transition: transform 0.2s ease-in;\n  transform-origin: center right;\n  display: block;\n  text-align: center;\n  text-indent: 20px;\n  width: 100%;\n  color: #00eeff;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n/* Cute Menu Icon - Exact Reference Implementation */\n.cute-menu-wrapper {\n  position: fixed;\n  top: 20px;\n  left: 20px;\n  z-index: 100;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.cute-menu-icon-container {\n  width: 80px;\n  height: 80px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.cute-menu-icon-container:hover {\n  transform: scale(1.05);\n}\n\n.cute-icon-background {\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg,\n    #00f5ff 0%,\n    #00d4ff 25%,\n    #0099ff 50%,\n    #6b73ff 75%,\n    #9b59b6 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow:\n    0 8px 32px rgba(0, 245, 255, 0.3),\n    0 4px 16px rgba(155, 89, 182, 0.2);\n  position: relative;\n  overflow: hidden;\n}\n\n.cute-icon-background::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(135deg,\n    rgba(255, 255, 255, 0.3) 0%,\n    rgba(255, 255, 255, 0.1) 50%,\n    rgba(255, 255, 255, 0.05) 100%);\n  border-radius: 20px;\n}\n\n.cute-eyes {\n  display: flex;\n  gap: 12px;\n  z-index: 2;\n  position: relative;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n.cute-eye {\n  width: 12px;\n  height: 24px;\n  background: white;\n  border-radius: 12px;\n  transition: transform 0.1s ease-out, opacity 0.3s ease;\n  animation: eyeBlink 4s infinite;\n}\n\n@keyframes eyeBlink {\n  0%, 90%, 100% {\n    height: 24px;\n  }\n  95% {\n    height: 4px;\n  }\n}\n\n.cute-eye.hidden {\n  opacity: 0;\n}\n\n.cute-happy-eyes {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  gap: 12px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  align-items: center;\n  justify-content: center;\n  z-index: 3;\n}\n\n.cute-happy-eyes.visible {\n  opacity: 1;\n}\n\n.happy-eye {\n  width: 20px;\n  height: 20px;\n}\n\n.click-me-label {\n  color: #00eeff;\n  font-size: 0.85rem;\n  font-weight: 600;\n  text-shadow: 0 0 10px rgba(0, 238, 255, 0.5);\n  animation: textPulse 2s ease-in-out infinite;\n  white-space: nowrap;\n}\n\n.cute-dropdown-menu {\n  position: absolute;\n  top: 90px;\n  left: 0;\n  background: linear-gradient(135deg, rgba(10, 14, 41, 0.95), rgba(0, 30, 60, 0.9));\n  backdrop-filter: blur(12px);\n  border-radius: 12px;\n  padding: 12px;\n  min-width: 180px;\n  box-shadow: 0 8px 32px rgba(0, 238, 255, 0.2);\n  border: 1px solid rgba(0, 238, 255, 0.3);\n  animation: menuSlideIn 0.3s ease-out;\n}\n\n.menu-option {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px 10px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  color: #ffffff;\n  font-size: 0.85rem;\n}\n\n.menu-option:hover {\n  background: rgba(0, 238, 255, 0.1);\n  transform: translateX(3px);\n}\n\n.menu-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 4px;\n}\n\n.notifications-icon {\n  background: linear-gradient(135deg, #9147ff, #b347ff);\n}\n\n.settings-icon {\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n}\n\n.chat-icon {\n  background: linear-gradient(135deg, #10b981, #059669);\n}\n\n.menu-icon svg {\n  width: 16px;\n  height: 16px;\n  color: white;\n}\n\n@keyframes textPulse {\n  0%, 100% { opacity: 0.8; }\n  50% { opacity: 1; }\n}\n\n@keyframes menuSlideIn {\n  0% {\n    opacity: 0;\n    transform: translateY(-10px) scale(0.95);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* HomeScreen Styles */\n.homescreen-container {\n  position: relative;\n  width: 100vw;\n  height: 100vh;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.wallpaper-container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\n  animation: backgroundShift 8s ease-in-out infinite;\n}\n\n.wallpaper-background {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  mix-blend-mode: screen;\n  filter: brightness(0.8) contrast(1.2);\n  animation: wallpaperLoop 0.5s ease-in-out infinite alternate;\n}\n\n/* Animated background particles effect */\n.wallpaper-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    radial-gradient(circle at 20% 80%, rgba(0, 238, 255, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(255, 0, 170, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 40% 40%, rgba(0, 238, 255, 0.05) 0%, transparent 50%);\n  animation: particleFloat 12s ease-in-out infinite;\n}\n\n.wallpaper-container::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(45deg, transparent 30%, rgba(0, 238, 255, 0.03) 50%, transparent 70%),\n    linear-gradient(-45deg, transparent 30%, rgba(255, 0, 170, 0.03) 50%, transparent 70%);\n  animation: scanLines 6s linear infinite;\n}\n\n.wallpaper-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    135deg,\n    rgba(0, 0, 0, 0.3) 0%,\n    rgba(10, 14, 41, 0.4) 50%,\n    rgba(0, 0, 0, 0.3) 100%\n  );\n  backdrop-filter: blur(1px);\n}\n\n.exit-animation-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(0, 0, 0, 0.8);\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n.exit-animation {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  mix-blend-mode: screen;\n}\n\n.fallback-exit-animation {\n  width: 200px;\n  height: 200px;\n  border-radius: 50%;\n  background: radial-gradient(circle, #00eeff 0%, transparent 70%);\n  animation: pulseExit 2s ease-in-out;\n}\n\n.homescreen-content {\n  position: relative;\n  z-index: 10;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: space-between;\n  height: 100vh;\n  padding: 2rem;\n  transition: all 0.5s ease-in-out;\n}\n\n.homescreen-content.entering {\n  opacity: 0;\n  transform: scale(0.9);\n}\n\n/* Title Section Styles */\n.title-section {\n  text-align: center;\n  margin-top: 2rem;\n}\n\n.marvel-logo {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #ff0000;\n  background: linear-gradient(45deg, #ff0000, #ff6b6b);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  letter-spacing: 0.2em;\n  margin-bottom: 0.5rem;\n  text-shadow: 0 0 10px rgba(255, 0, 0, 0.3);\n}\n\n.main-title {\n  font-size: 4rem;\n  font-weight: bold;\n  color: #ffffff;\n  text-shadow:\n    0 0 20px rgba(0, 238, 255, 0.5),\n    0 0 40px rgba(0, 238, 255, 0.3),\n    0 0 60px rgba(0, 238, 255, 0.2);\n  letter-spacing: 0.1em;\n  margin: 0;\n  animation: titleGlow 2s ease-in-out infinite alternate;\n}\n\n.subtitle {\n  font-size: 1rem;\n  color: #00eeff;\n  margin-top: 1rem;\n  opacity: 0.8;\n  animation: subtitlePulse 1.5s ease-in-out infinite;\n}\n\n/* Jarvis Icon Styles */\n.jarvis-icon-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n}\n\n.jarvis-icon-container:hover {\n  transform: scale(1.05);\n}\n\n.jarvis-icon {\n  position: relative;\n  width: 150px;\n  height: 150px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 1rem;\n}\n\n.icon-glow {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(0, 238, 255, 0.3) 0%, transparent 70%);\n  animation: iconGlow 2s ease-in-out infinite alternate;\n}\n\n.icon-core {\n  position: relative;\n  z-index: 2;\n  width: 80%;\n  height: 80%;\n  border-radius: 50%;\n  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));\n  border: 2px solid #00eeff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease-in-out;\n}\n\n.jarvis-icon-container:hover .icon-core {\n  border-color: #ff00aa;\n  box-shadow: 0 0 30px rgba(0, 238, 255, 0.5);\n}\n\n.jarvis-svg {\n  width: 60%;\n  height: 60%;\n  fill: none;\n  stroke: #00eeff;\n  stroke-width: 2;\n  transition: all 0.3s ease-in-out;\n}\n\n.outer-ring {\n  stroke-dasharray: 188;\n  stroke-dashoffset: 188;\n  animation: drawRing 3s ease-in-out infinite;\n}\n\n.middle-ring {\n  stroke-dasharray: 125;\n  stroke-dashoffset: 125;\n  animation: drawRing 3s ease-in-out infinite 0.5s;\n}\n\n.inner-core {\n  fill: #00eeff;\n  opacity: 0.8;\n  animation: coreGlow 2s ease-in-out infinite alternate;\n}\n\n.cross-lines {\n  stroke-dasharray: 40;\n  stroke-dashoffset: 40;\n  animation: drawRing 3s ease-in-out infinite 1s;\n}\n\n.icon-label {\n  font-size: 0.9rem;\n  color: #00eeff;\n  font-weight: 500;\n  letter-spacing: 0.1em;\n  opacity: 0.9;\n}\n\n/* Bottom Menu Styles */\n.bottom-menu {\n  display: flex;\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.menu-item {\n  position: relative;\n  width: 50px;\n  height: 50px;\n  border-radius: 12px;\n  background: linear-gradient(135deg, rgba(10, 14, 41, 0.9), rgba(0, 30, 60, 0.8));\n  backdrop-filter: blur(12px) saturate(180%);\n  border: 1px solid rgba(0, 238, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease-in-out;\n  box-shadow: 0 4px 16px rgba(0, 238, 255, 0.1);\n}\n\n.menu-item:hover {\n  transform: translateY(-5px) scale(1.05);\n  border-color: #00eeff;\n  box-shadow: 0 8px 32px rgba(0, 238, 255, 0.3);\n  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));\n}\n\n.menu-item:hover::before {\n  opacity: 1;\n  transform: scale(1);\n}\n\n.menu-item::before {\n  content: attr(data-tooltip);\n  position: absolute;\n  bottom: 120%;\n  left: 50%;\n  transform: translateX(-50%) scale(0.8);\n  background: rgba(0, 0, 0, 0.9);\n  color: #00eeff;\n  padding: 0.5rem 0.8rem;\n  border-radius: 6px;\n  font-size: 0.75rem;\n  white-space: nowrap;\n  opacity: 0;\n  transition: all 0.3s ease-in-out;\n  pointer-events: none;\n  border: 1px solid rgba(0, 238, 255, 0.3);\n}\n\n.menu-letter {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #00eeff;\n  transition: all 0.3s ease-in-out;\n}\n\n.menu-item:hover .menu-letter {\n  color: #ffffff;\n  text-shadow: 0 0 10px rgba(0, 238, 255, 0.8);\n}\n\n/* Animations */\n@keyframes wallpaperLoop {\n  0% { opacity: 0.8; }\n  100% { opacity: 1; }\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes pulseExit {\n  0% { transform: scale(0); opacity: 0; }\n  50% { transform: scale(1.2); opacity: 1; }\n  100% { transform: scale(2); opacity: 0; }\n}\n\n@keyframes titleGlow {\n  0% { text-shadow: 0 0 20px rgba(0, 238, 255, 0.5), 0 0 40px rgba(0, 238, 255, 0.3); }\n  100% { text-shadow: 0 0 30px rgba(0, 238, 255, 0.8), 0 0 60px rgba(0, 238, 255, 0.5); }\n}\n\n@keyframes subtitlePulse {\n  0%, 100% { opacity: 0.8; }\n  50% { opacity: 1; }\n}\n\n@keyframes iconGlow {\n  0% { transform: scale(1); opacity: 0.3; }\n  100% { transform: scale(1.1); opacity: 0.6; }\n}\n\n@keyframes drawRing {\n  0% { stroke-dashoffset: 188; }\n  50% { stroke-dashoffset: 0; }\n  100% { stroke-dashoffset: -188; }\n}\n\n@keyframes coreGlow {\n  0% { opacity: 0.8; }\n  100% { opacity: 1; }\n}\n\n/* Enhanced animations for MinimizedJarvis */\n@keyframes iconGlow {\n  0% { transform: scale(1); opacity: 0.3; }\n  100% { transform: scale(1.1); opacity: 0.6; }\n}\n\n@keyframes drawRing {\n  0% { stroke-dashoffset: 220; }\n  50% { stroke-dashoffset: 0; }\n  100% { stroke-dashoffset: -220; }\n}\n\n@keyframes backgroundShift {\n  0% {\n    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\n  }\n  25% {\n    background: linear-gradient(225deg, #16213e 0%, #0f3460 50%, #1a1a2e 100%);\n  }\n  50% {\n    background: linear-gradient(315deg, #0f3460 0%, #1a1a2e 50%, #16213e 100%);\n  }\n  75% {\n    background: linear-gradient(45deg, #1a1a2e 0%, #0f3460 50%, #16213e 100%);\n  }\n  100% {\n    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);\n  }\n}\n\n@keyframes particleFloat {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n    opacity: 0.3;\n  }\n  33% {\n    transform: translateY(-20px) rotate(120deg);\n    opacity: 0.6;\n  }\n  66% {\n    transform: translateY(10px) rotate(240deg);\n    opacity: 0.4;\n  }\n}\n\n@keyframes scanLines {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n/* AI Assistant Component Styles */\n.container-ai-input {\n  --perspective: 1000px;\n  --translateY: 45px;\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: -2.5rem;\n  bottom: -2.5rem;\n  display: grid;\n  grid-template-columns: repeat(5, 1fr);\n  transform-style: preserve-3d;\n}\n\n.container-wrap {\n  display: flex;\n  align-items: center;\n  justify-items: center;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translateX(-50%) translateY(-50%);\n  z-index: 9;\n  transform-style: preserve-3d;\n  cursor: pointer;\n  padding: 4px;\n  transition: all 0.3s ease;\n}\n\n.container-wrap:hover {\n  padding: 0;\n}\n\n.container-wrap:active {\n  transform: translateX(-50%) translateY(-50%) scale(0.95);\n}\n\n.container-wrap:after {\n  content: \"\";\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translateX(-50%) translateY(-55%);\n  width: 12rem;\n  height: 11rem;\n  background-color: #dedfe0;\n  border-radius: 3.2rem;\n  transition: all 0.3s ease;\n}\n\n.container-wrap:hover:after {\n  transform: translateX(-50%) translateY(-50%);\n  height: 12rem;\n}\n\n.container-wrap input {\n  opacity: 0;\n  width: 0;\n  height: 0;\n  position: absolute;\n}\n\n.container-wrap input:checked + .card .eyes {\n  opacity: 0;\n}\n\n.container-wrap input:checked + .card .content-card {\n  width: 260px;\n  height: 160px;\n}\n\n.container-wrap input:checked + .card .background-blur-balls {\n  border-radius: 20px;\n}\n\n.container-wrap input:checked + .card .container-ai-chat {\n  opacity: 1;\n  visibility: visible;\n  z-index: 99999;\n  pointer-events: visible;\n}\n\n.card {\n  width: 100%;\n  height: 100%;\n  /* background-color: #fff; */\n  transform-style: preserve-3d;\n  will-change: transform;\n  transition: all 0.6s ease;\n  border-radius: 3rem;\n  display: flex;\n  align-items: center;\n  transform: translateZ(50px);\n  justify-content: center;\n}\n\n.card:hover {\n  box-shadow:\n    0 10px 40px rgba(0, 0, 60, 0.25),\n    inset 0 0 10px rgba(255, 255, 255, 0.5);\n}\n\n.background-blur-balls {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translateX(-50%) translateY(-50%);\n  width: 100%;\n  height: 100%;\n  z-index: -10;\n  border-radius: 3rem;\n  transition: all 0.3s ease;\n  background-color: rgba(255, 255, 255, 0.8);\n  overflow: hidden;\n}\n\n.balls {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translateX(-50%) translateY(-50%);\n  animation: rotate-background-balls 10s linear infinite;\n}\n\n.container-wrap:hover .balls {\n  animation-play-state: paused;\n}\n\n.background-blur-balls .ball {\n  width: 6rem;\n  height: 6rem;\n  position: absolute;\n  border-radius: 50%;\n  filter: blur(30px);\n}\n\n.background-blur-balls .ball.violet {\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #9147ff;\n}\n\n.background-blur-balls .ball.green {\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #34d399;\n}\n\n.background-blur-balls .ball.rosa {\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n  background-color: #ec4899;\n}\n\n.background-blur-balls .ball.cyan {\n  top: 50%;\n  right: 0;\n  transform: translateY(-50%);\n  background-color: #05e0f5;\n}\n\n.content-card {\n  width: 12rem;\n  height: 12rem;\n  display: flex;\n  border-radius: 3rem;\n  transition: all 0.3s ease;\n  overflow: hidden;\n}\n\n.background-blur-card {\n  width: 100%;\n  height: 100%;\n  backdrop-filter: blur(50px);\n}\n\n.eyes {\n  position: absolute;\n  left: 50%;\n  bottom: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 26px;\n  gap: 1rem;\n  transition: all 0.3s ease;\n}\n\n.eyes .eye {\n  width: 13px;\n  height: 26px;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(0, 238, 255, 0.3));\n  border-radius: 8px;\n  animation: animate-eyes 10s infinite linear;\n  transition: all 0.3s ease;\n  border: 1px solid rgba(0, 238, 255, 0.4);\n}\n\n.eyes.happy {\n  display: none;\n  color: #fff;\n  gap: 0;\n}\n\n.eyes.happy svg {\n  width: 60px;\n}\n\n.container-wrap:hover .eyes .eye {\n  display: none;\n}\n\n.container-wrap:hover .eyes.happy {\n  display: flex;\n}\n\n.container-ai-chat {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  padding: 6px;\n  opacity: 0;\n  pointer-events: none;\n  visibility: hidden;\n  transition: all 0.3s ease;\n}\n\n.container-wrap .card .chat {\n  display: flex;\n  justify-content: space-between;\n  flex-direction: column;\n  border-radius: 15px;\n  width: 100%;\n  height: 100%;\n  padding: 4px;\n  overflow: hidden;\n  background-color: #ffffff;\n}\n\n.options-menu {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n  padding: 1rem;\n  width: 100%;\n  height: 100%;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 0.75rem 1rem;\n  background: linear-gradient(135deg, rgba(0, 238, 255, 0.1), rgba(255, 0, 170, 0.1));\n  border: 1px solid rgba(0, 238, 255, 0.2);\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n}\n\n.option-item:hover {\n  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(255, 0, 170, 0.2));\n  border-color: #00eeff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 238, 255, 0.3);\n}\n\n.option-item span {\n  font-size: 0.9rem;\n  font-weight: 500;\n  color: #333;\n}\n\n.click-me-text {\n  font-size: 0.75rem;\n  font-weight: 600;\n  color: #00eeff;\n  text-shadow: 0 0 8px rgba(0, 238, 255, 0.5);\n  animation: clickMePulse 2s ease-in-out infinite;\n  white-space: nowrap;\n  pointer-events: none;\n}\n\n@keyframes clickMePulse {\n  0%, 100% {\n    opacity: 0.8;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.05);\n  }\n}\n\n@keyframes rotate-background-balls {\n  from {\n    transform: translateX(-50%) translateY(-50%) rotate(360deg);\n  }\n  to {\n    transform: translateX(-50%) translateY(-50%) rotate(0);\n  }\n}\n\n@keyframes animate-eyes {\n  46% {\n    height: 52px;\n  }\n  48% {\n    height: 20px;\n  }\n  50% {\n    height: 52px;\n  }\n  96% {\n    height: 52px;\n  }\n  98% {\n    height: 20px;\n  }\n  100% {\n    height: 52px;\n  }\n}\n\n/* 3D Hover Effects for AI Assistant */\n.area:nth-child(15):hover ~ .container-wrap .card,\n.area:nth-child(15):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(10deg)\n    translateZ(var(--translateY)) scale3d(1.05, 1.05, 1.05);\n}\n.area:nth-child(14):hover ~ .container-wrap .card,\n.area:nth-child(14):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(5deg)\n    translateZ(var(--translateY)) scale3d(1.02, 1.02, 1.02);\n}\n.area:nth-child(13):hover ~ .container-wrap .card,\n.area:nth-child(13):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(0)\n    translateZ(var(--translateY)) scale3d(1.02, 1.02, 1.02);\n}\n.area:nth-child(12):hover ~ .container-wrap .card,\n.area:nth-child(12):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(-5deg)\n    translateZ(var(--translateY)) scale3d(1.02, 1.02, 1.02);\n}\n.area:nth-child(11):hover ~ .container-wrap .card,\n.area:nth-child(11):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(-10deg) rotateY(-10deg)\n    translateZ(var(--translateY)) scale3d(1.05, 1.05, 1.05);\n}\n\n.area:nth-child(10):hover ~ .container-wrap .card,\n.area:nth-child(10):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(0) rotateY(15deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(9):hover ~ .container-wrap .card,\n.area:nth-child(9):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(0) rotateY(7deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(8):hover ~ .container-wrap .card,\n.area:nth-child(8):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(0) rotateY(0)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(7):hover ~ .container-wrap .card,\n.area:nth-child(7):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(0) rotateY(-7deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(6):hover ~ .container-wrap .card,\n.area:nth-child(6):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(0) rotateY(-15deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n\n.area:nth-child(5):hover ~ .container-wrap .card,\n.area:nth-child(5):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(15deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(4):hover ~ .container-wrap .card,\n.area:nth-child(4):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(7deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(3):hover ~ .container-wrap .card,\n.area:nth-child(3):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(0)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(2):hover ~ .container-wrap .card,\n.area:nth-child(2):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(-7deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n.area:nth-child(1):hover ~ .container-wrap .card,\n.area:nth-child(1):hover ~ .container-wrap .eyes .eye {\n  transform: perspective(var(--perspective)) rotateX(15deg) rotateY(-15deg)\n    translateZ(var(--translateY)) scale3d(1, 1, 1);\n}\n\n/* AI Assistant Menu Styles */\n.ai-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  background: linear-gradient(135deg, rgba(10, 14, 41, 0.95), rgba(0, 30, 60, 0.9));\n  backdrop-filter: blur(15px);\n  border-radius: 12px;\n  padding: 0.5rem;\n  min-width: 120px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(0, 238, 255, 0.3);\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-20px) scale(0.9);\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  z-index: 1000;\n  max-height: 0;\n  overflow: hidden;\n}\n\n.ai-menu.open {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0) scale(1);\n  max-height: 200px;\n}\n\n.ai-menu-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 0.75rem;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.75rem;\n  white-space: nowrap;\n  border: 1px solid transparent;\n}\n\n.ai-menu-item:hover {\n  background: linear-gradient(135deg, rgba(0, 238, 255, 0.2), rgba(0, 123, 255, 0.1));\n  color: rgba(0, 238, 255, 1);\n  border-color: rgba(0, 238, 255, 0.4);\n  transform: translateX(2px);\n}\n\n.ai-menu-item svg {\n  width: 12px;\n  height: 12px;\n}\n\n/* Text Input Styles for JARVIS */\n.input {\n  color: #fff;\n  font-size: 0.9rem;\n  background-color: transparent;\n  width: 100%;\n  box-sizing: border-box;\n  padding-inline: 0.5em;\n  padding-block: 0.7em;\n  border: none;\n  border-bottom: var(--border-height) solid transparent;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n/* Chat Screen Specific Input Styles */\n.input:focus {\n  box-shadow: 0 0 10px rgba(0, 238, 255, 0.3);\n  transform: scale(1.02);\n}\n\n.input-border {\n  position: absolute;\n  background: var(--border-after-color);\n  width: 0%;\n  height: 2px;\n  bottom: 6px;\n  left: 8px;\n  border-radius: 0 0 2px 2px;\n  transition: width 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045);\n  z-index: 1;\n  max-width: calc(100% - 16px);\n}\n\n.input:focus {\n  outline: none;\n}\n\n.input:focus + .input-border {\n  width: calc(100% - 16px);\n}\n\n.form-control {\n  position: relative;\n  --width-of-input: 300px;\n  --width-of-input-expanded: 450px;\n  --border-height: 2px;\n  --border-before-color: rgba(221, 221, 221, 0.39);\n  --border-after-color: linear-gradient(90deg, #FF6464 0%, #FFBF59 50%, #47C9FF 100%);\n  width: var(--width-of-input);\n  margin-top: 2rem;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 8px;\n  padding: 4px;\n  overflow: hidden;\n\n  /* Glow effect */\n  box-shadow:\n    0 0 20px rgba(0, 238, 255, 0.3),\n    0 0 40px rgba(0, 238, 255, 0.1),\n    0 4px 16px rgba(0, 0, 0, 0.2);\n\n  /* Subtle background glow */\n  background: linear-gradient(135deg,\n    rgba(0, 238, 255, 0.05) 0%,\n    rgba(255, 0, 170, 0.05) 50%,\n    rgba(71, 201, 255, 0.05) 100%);\n\n  /* Animated border glow */\n  border: 1px solid rgba(0, 238, 255, 0.3);\n}\n\n.form-control:hover {\n  box-shadow:\n    0 0 30px rgba(0, 238, 255, 0.4),\n    0 0 60px rgba(0, 238, 255, 0.2),\n    0 6px 20px rgba(0, 0, 0, 0.3);\n  border-color: rgba(0, 238, 255, 0.5);\n}\n\n.form-control.focused {\n  width: var(--width-of-input-expanded);\n  box-shadow:\n    0 0 40px rgba(0, 238, 255, 0.6),\n    0 0 80px rgba(0, 238, 255, 0.3),\n    0 8px 32px rgba(0, 0, 0, 0.4);\n  border-color: rgba(0, 238, 255, 0.8);\n  transform: scale(1.02);\n}\n\n.form-control::before {\n  content: '';\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  background: linear-gradient(45deg,\n    #FF6464, #FFBF59, #47C9FF, #00eeff, #ff00aa);\n  background-size: 400% 400%;\n  border-radius: 10px;\n  z-index: -1;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n  animation: gradientShift 3s ease infinite;\n}\n\n.form-control.focused::before {\n  opacity: 0.3;\n}\n\n@keyframes gradientShift {\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n}\n\n@keyframes blinkingCircle {\n  0% {\n    opacity: 0.6;\n    transform: scale(1);\n    box-shadow: 0 0 20px currentColor;\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.02);\n    box-shadow: 0 0 30px currentColor;\n  }\n  100% {\n    opacity: 0.6;\n    transform: scale(1);\n    box-shadow: 0 0 20px currentColor;\n  }\n}\n\n.input-alt {\n  font-size: 1.2rem;\n  padding-inline: 1em;\n  padding-block: 0.8em;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n\n.input-border-alt {\n  height: 3px;\n  background: linear-gradient(90deg, #FF6464 0%, #FFBF59 50%, #47C9FF 100%);\n  transition: width 0.4s cubic-bezier(0.42, 0, 0.58, 1.00);\n}\n\n.input-alt:focus + .input-border-alt {\n  width: 100%;\n}"], "names": [], "sourceRoot": ""}