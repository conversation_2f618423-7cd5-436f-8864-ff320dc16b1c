import React from 'react';

const StatusIndicator = ({ status, size = 'medium', showLabel = true }) => {
  const statusConfig = {
    idle: {
      color: 'bg-gray-500',
      pulse: false,
      label: 'Idle',
      icon: '💤'
    },
    listening: {
      color: 'bg-blue-500',
      pulse: true,
      label: 'Listening',
      icon: '👂'
    },
    thinking: {
      color: 'bg-yellow-500',
      pulse: true,
      label: 'Thinking',
      icon: '🧠'
    },
    speaking: {
      color: 'bg-green-500',
      pulse: true,
      label: 'Speaking',
      icon: '🗣️'
    },
    error: {
      color: 'bg-red-500',
      pulse: false,
      label: 'Error',
      icon: '⚠️'
    }
  };

  const sizeClasses = {
    small: 'w-4 h-4 text-xs',
    medium: 'w-6 h-6 text-sm',
    large: 'w-8 h-8 text-base'
  };

  const config = statusConfig[status] || statusConfig.idle;

  return (
    <div className="flex items-center space-x-2">
      <div className={`relative ${sizeClasses[size]}`}>
        <div
          className={`${config.color} ${sizeClasses[size]} rounded-full 
                     flex items-center justify-center
                     ${config.pulse ? 'animate-pulse' : ''}`}
        >
          <span className="text-white">{config.icon}</span>
        </div>
        {config.pulse && (
          <div
            className={`absolute inset-0 ${config.color} rounded-full 
                       animate-ping opacity-75`}
          />
        )}
      </div>
      {showLabel && (
        <span className="text-sm text-gray-300 font-medium">
          {config.label}
        </span>
      )}
    </div>
  );
};

export default StatusIndicator;
