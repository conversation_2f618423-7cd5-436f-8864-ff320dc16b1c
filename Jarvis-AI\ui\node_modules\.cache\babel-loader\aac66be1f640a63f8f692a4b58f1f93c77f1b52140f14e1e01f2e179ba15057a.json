{"ast": null, "code": "import { isAnimationControls } from '../../../animation/utils/is-animation-controls.mjs';\nimport { createAnimationState } from '../../../render/utils/animation-state.mjs';\nimport { Feature } from '../Feature.mjs';\nclass AnimationFeature extends Feature {\n  /**\n   * We dynamically generate the AnimationState manager as it contains a reference\n   * to the underlying animation library. We only want to load that if we load this,\n   * so people can optionally code split it out using the `m` component.\n   */\n  constructor(node) {\n    super(node);\n    node.animationState || (node.animationState = createAnimationState(node));\n  }\n  updateAnimationControlsSubscription() {\n    const {\n      animate\n    } = this.node.getProps();\n    this.unmount();\n    if (isAnimationControls(animate)) {\n      this.unmount = animate.subscribe(this.node);\n    }\n  }\n  /**\n   * Subscribe any provided AnimationControls to the component's VisualElement\n   */\n  mount() {\n    this.updateAnimationControlsSubscription();\n  }\n  update() {\n    const {\n      animate\n    } = this.node.getProps();\n    const {\n      animate: prevAnimate\n    } = this.node.prevProps || {};\n    if (animate !== prevAnimate) {\n      this.updateAnimationControlsSubscription();\n    }\n  }\n  unmount() {}\n}\nexport { AnimationFeature };", "map": {"version": 3, "names": ["isAnimationControls", "createAnimationState", "Feature", "AnimationFeature", "constructor", "node", "animationState", "updateAnimationControlsSubscription", "animate", "getProps", "unmount", "subscribe", "mount", "update", "prevAnimate", "prevProps"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs"], "sourcesContent": ["import { isAnimationControls } from '../../../animation/utils/is-animation-controls.mjs';\nimport { createAnimationState } from '../../../render/utils/animation-state.mjs';\nimport { Feature } from '../Feature.mjs';\n\nclass AnimationFeature extends Feature {\n    /**\n     * We dynamically generate the AnimationState manager as it contains a reference\n     * to the underlying animation library. We only want to load that if we load this,\n     * so people can optionally code split it out using the `m` component.\n     */\n    constructor(node) {\n        super(node);\n        node.animationState || (node.animationState = createAnimationState(node));\n    }\n    updateAnimationControlsSubscription() {\n        const { animate } = this.node.getProps();\n        this.unmount();\n        if (isAnimationControls(animate)) {\n            this.unmount = animate.subscribe(this.node);\n        }\n    }\n    /**\n     * Subscribe any provided AnimationControls to the component's VisualElement\n     */\n    mount() {\n        this.updateAnimationControlsSubscription();\n    }\n    update() {\n        const { animate } = this.node.getProps();\n        const { animate: prevAnimate } = this.node.prevProps || {};\n        if (animate !== prevAnimate) {\n            this.updateAnimationControlsSubscription();\n        }\n    }\n    unmount() { }\n}\n\nexport { AnimationFeature };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,oDAAoD;AACxF,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,OAAO,QAAQ,gBAAgB;AAExC,MAAMC,gBAAgB,SAASD,OAAO,CAAC;EACnC;AACJ;AACA;AACA;AACA;EACIE,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAAC;IACXA,IAAI,CAACC,cAAc,KAAKD,IAAI,CAACC,cAAc,GAAGL,oBAAoB,CAACI,IAAI,CAAC,CAAC;EAC7E;EACAE,mCAAmCA,CAAA,EAAG;IAClC,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC;IACxC,IAAI,CAACC,OAAO,CAAC,CAAC;IACd,IAAIV,mBAAmB,CAACQ,OAAO,CAAC,EAAE;MAC9B,IAAI,CAACE,OAAO,GAAGF,OAAO,CAACG,SAAS,CAAC,IAAI,CAACN,IAAI,CAAC;IAC/C;EACJ;EACA;AACJ;AACA;EACIO,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACL,mCAAmC,CAAC,CAAC;EAC9C;EACAM,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEL;IAAQ,CAAC,GAAG,IAAI,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC;IACxC,MAAM;MAAED,OAAO,EAAEM;IAAY,CAAC,GAAG,IAAI,CAACT,IAAI,CAACU,SAAS,IAAI,CAAC,CAAC;IAC1D,IAAIP,OAAO,KAAKM,WAAW,EAAE;MACzB,IAAI,CAACP,mCAAmC,CAAC,CAAC;IAC9C;EACJ;EACAG,OAAOA,CAAA,EAAG,CAAE;AAChB;AAEA,SAASP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}