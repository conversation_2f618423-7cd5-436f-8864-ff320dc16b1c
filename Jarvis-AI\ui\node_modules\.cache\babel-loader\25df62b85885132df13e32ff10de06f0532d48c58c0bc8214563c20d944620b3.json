{"ast": null, "code": "function buildProjectionTransform(delta, treeScale, latestTransform) {\n  let transform = \"\";\n  /**\n   * The translations we use to calculate are always relative to the viewport coordinate space.\n   * But when we apply scales, we also scale the coordinate space of an element and its children.\n   * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n   * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n   */\n  const xTranslate = delta.x.translate / treeScale.x;\n  const yTranslate = delta.y.translate / treeScale.y;\n  if (xTranslate || yTranslate) {\n    transform = `translate3d(${xTranslate}px, ${yTranslate}px, 0) `;\n  }\n  /**\n   * Apply scale correction for the tree transform.\n   * This will apply scale to the screen-orientated axes.\n   */\n  if (treeScale.x !== 1 || treeScale.y !== 1) {\n    transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n  }\n  if (latestTransform) {\n    const {\n      rotate,\n      rotateX,\n      rotateY\n    } = latestTransform;\n    if (rotate) transform += `rotate(${rotate}deg) `;\n    if (rotateX) transform += `rotateX(${rotateX}deg) `;\n    if (rotateY) transform += `rotateY(${rotateY}deg) `;\n  }\n  /**\n   * Apply scale to match the size of the element to the size we want it.\n   * This will apply scale to the element-orientated axes.\n   */\n  const elementScaleX = delta.x.scale * treeScale.x;\n  const elementScaleY = delta.y.scale * treeScale.y;\n  if (elementScaleX !== 1 || elementScaleY !== 1) {\n    transform += `scale(${elementScaleX}, ${elementScaleY})`;\n  }\n  return transform || \"none\";\n}\nexport { buildProjectionTransform };", "map": {"version": 3, "names": ["buildProjectionTransform", "delta", "treeScale", "latestTransform", "transform", "xTranslate", "x", "translate", "yTranslate", "y", "rotate", "rotateX", "rotateY", "elementScaleX", "scale", "elementScaleY"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/projection/styles/transform.mjs"], "sourcesContent": ["function buildProjectionTransform(delta, treeScale, latestTransform) {\n    let transform = \"\";\n    /**\n     * The translations we use to calculate are always relative to the viewport coordinate space.\n     * But when we apply scales, we also scale the coordinate space of an element and its children.\n     * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n     * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n     */\n    const xTranslate = delta.x.translate / treeScale.x;\n    const yTranslate = delta.y.translate / treeScale.y;\n    if (xTranslate || yTranslate) {\n        transform = `translate3d(${xTranslate}px, ${yTranslate}px, 0) `;\n    }\n    /**\n     * Apply scale correction for the tree transform.\n     * This will apply scale to the screen-orientated axes.\n     */\n    if (treeScale.x !== 1 || treeScale.y !== 1) {\n        transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n    }\n    if (latestTransform) {\n        const { rotate, rotateX, rotateY } = latestTransform;\n        if (rotate)\n            transform += `rotate(${rotate}deg) `;\n        if (rotateX)\n            transform += `rotateX(${rotateX}deg) `;\n        if (rotateY)\n            transform += `rotateY(${rotateY}deg) `;\n    }\n    /**\n     * Apply scale to match the size of the element to the size we want it.\n     * This will apply scale to the element-orientated axes.\n     */\n    const elementScaleX = delta.x.scale * treeScale.x;\n    const elementScaleY = delta.y.scale * treeScale.y;\n    if (elementScaleX !== 1 || elementScaleY !== 1) {\n        transform += `scale(${elementScaleX}, ${elementScaleY})`;\n    }\n    return transform || \"none\";\n}\n\nexport { buildProjectionTransform };\n"], "mappings": "AAAA,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAE;EACjE,IAAIC,SAAS,GAAG,EAAE;EAClB;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMC,UAAU,GAAGJ,KAAK,CAACK,CAAC,CAACC,SAAS,GAAGL,SAAS,CAACI,CAAC;EAClD,MAAME,UAAU,GAAGP,KAAK,CAACQ,CAAC,CAACF,SAAS,GAAGL,SAAS,CAACO,CAAC;EAClD,IAAIJ,UAAU,IAAIG,UAAU,EAAE;IAC1BJ,SAAS,GAAG,eAAeC,UAAU,OAAOG,UAAU,SAAS;EACnE;EACA;AACJ;AACA;AACA;EACI,IAAIN,SAAS,CAACI,CAAC,KAAK,CAAC,IAAIJ,SAAS,CAACO,CAAC,KAAK,CAAC,EAAE;IACxCL,SAAS,IAAI,SAAS,CAAC,GAAGF,SAAS,CAACI,CAAC,KAAK,CAAC,GAAGJ,SAAS,CAACO,CAAC,IAAI;EACjE;EACA,IAAIN,eAAe,EAAE;IACjB,MAAM;MAAEO,MAAM;MAAEC,OAAO;MAAEC;IAAQ,CAAC,GAAGT,eAAe;IACpD,IAAIO,MAAM,EACNN,SAAS,IAAI,UAAUM,MAAM,OAAO;IACxC,IAAIC,OAAO,EACPP,SAAS,IAAI,WAAWO,OAAO,OAAO;IAC1C,IAAIC,OAAO,EACPR,SAAS,IAAI,WAAWQ,OAAO,OAAO;EAC9C;EACA;AACJ;AACA;AACA;EACI,MAAMC,aAAa,GAAGZ,KAAK,CAACK,CAAC,CAACQ,KAAK,GAAGZ,SAAS,CAACI,CAAC;EACjD,MAAMS,aAAa,GAAGd,KAAK,CAACQ,CAAC,CAACK,KAAK,GAAGZ,SAAS,CAACO,CAAC;EACjD,IAAII,aAAa,KAAK,CAAC,IAAIE,aAAa,KAAK,CAAC,EAAE;IAC5CX,SAAS,IAAI,SAASS,aAAa,KAAKE,aAAa,GAAG;EAC5D;EACA,OAAOX,SAAS,IAAI,MAAM;AAC9B;AAEA,SAASJ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}