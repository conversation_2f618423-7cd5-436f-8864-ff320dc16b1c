{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _objectSpread from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nconst _excluded = [\"willChange\"],\n  _excluded2 = [\"children\"];\nimport { warning, invariant } from '../utils/errors.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { isRefObject } from '../utils/is-ref-object.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { motionValue } from '../value/index.mjs';\nimport { isWillChangeMotionValue } from '../value/use-will-change/is.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\nimport { transformProps } from './html/utils/transform.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from './utils/is-variant-label.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { variantProps } from './utils/variant-props.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\nconst featureNames = Object.keys(featureDefinitions);\nconst numFeatures = featureNames.length;\nconst propEventHandlers = [\"AnimationStart\", \"AnimationComplete\", \"Update\", \"BeforeLayoutMeasure\", \"LayoutMeasure\", \"LayoutAnimationStart\", \"LayoutAnimationComplete\"];\nconst numVariantProps = variantProps.length;\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n  constructor(_ref) {\n    let {\n      parent,\n      props,\n      presenceContext,\n      reducedMotionConfig,\n      visualState\n    } = _ref;\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    /**\n     * A reference to the current underlying Instance, e.g. a HTMLElement\n     * or Three.Mesh etc.\n     */\n    this.current = null;\n    /**\n     * A set containing references to this VisualElement's children.\n     */\n    this.children = new Set();\n    /**\n     * Determine what role this visual element should take in the variant tree.\n     */\n    this.isVariantNode = false;\n    this.isControllingVariants = false;\n    /**\n     * Decides whether this VisualElement should animate in reduced motion\n     * mode.\n     *\n     * TODO: This is currently set on every individual VisualElement but feels\n     * like it could be set globally.\n     */\n    this.shouldReduceMotion = null;\n    /**\n     * A map of all motion values attached to this visual element. Motion\n     * values are source of truth for any given animated value. A motion\n     * value might be provided externally by the component via props.\n     */\n    this.values = new Map();\n    /**\n     * Cleanup functions for active features (hover/tap/exit etc)\n     */\n    this.features = {};\n    /**\n     * A map of every subscription that binds the provided or generated\n     * motion values onChange listeners to this visual element.\n     */\n    this.valueSubscriptions = new Map();\n    /**\n     * A reference to the previously-provided motion values as returned\n     * from scrapeMotionValuesFromProps. We use the keys in here to determine\n     * if any motion values need to be removed after props are updated.\n     */\n    this.prevMotionValues = {};\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    /**\n     * An object containing an unsubscribe function for each prop event subscription.\n     * For example, every \"Update\" event can have multiple subscribers via\n     * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n     */\n    this.propEventSubscriptions = {};\n    this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n    this.render = () => {\n      if (!this.current) return;\n      this.triggerBuild();\n      this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n    };\n    this.scheduleRender = () => frame.render(this.render, false, true);\n    const {\n      latestValues,\n      renderState\n    } = visualState;\n    this.latestValues = latestValues;\n    this.baseTarget = _objectSpread({}, latestValues);\n    this.initialValues = props.initial ? _objectSpread({}, latestValues) : {};\n    this.renderState = renderState;\n    this.parent = parent;\n    this.props = props;\n    this.presenceContext = presenceContext;\n    this.depth = parent ? parent.depth + 1 : 0;\n    this.reducedMotionConfig = reducedMotionConfig;\n    this.options = options;\n    this.isControllingVariants = isControllingVariants(props);\n    this.isVariantNode = isVariantNode(props);\n    if (this.isVariantNode) {\n      this.variantChildren = new Set();\n    }\n    this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n    /**\n     * Any motion values that are provided to the element when created\n     * aren't yet bound to the element, as this would technically be impure.\n     * However, we iterate through the motion values and set them to the\n     * initial values for this component.\n     *\n     * TODO: This is impure and we should look at changing this to run on mount.\n     * Doing so will break some tests but this isn't neccessarily a breaking change,\n     * more a reflection of the test.\n     */\n    const _this$scrapeMotionVal = this.scrapeMotionValuesFromProps(props, {}),\n      {\n        willChange\n      } = _this$scrapeMotionVal,\n      initialMotionValues = _objectWithoutProperties(_this$scrapeMotionVal, _excluded);\n    for (const key in initialMotionValues) {\n      const value = initialMotionValues[key];\n      if (latestValues[key] !== undefined && isMotionValue(value)) {\n        value.set(latestValues[key], false);\n        if (isWillChangeMotionValue(willChange)) {\n          willChange.add(key);\n        }\n      }\n    }\n  }\n  /**\n   * This method takes React props and returns found MotionValues. For example, HTML\n   * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n   *\n   * This isn't an abstract method as it needs calling in the constructor, but it is\n   * intended to be one.\n   */\n  scrapeMotionValuesFromProps(_props, _prevProps) {\n    return {};\n  }\n  mount(instance) {\n    this.current = instance;\n    visualElementStore.set(instance, this);\n    if (this.projection && !this.projection.instance) {\n      this.projection.mount(instance);\n    }\n    if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n      this.removeFromVariantTree = this.parent.addVariantChild(this);\n    }\n    this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n    if (!hasReducedMotionListener.current) {\n      initPrefersReducedMotion();\n    }\n    this.shouldReduceMotion = this.reducedMotionConfig === \"never\" ? false : this.reducedMotionConfig === \"always\" ? true : prefersReducedMotion.current;\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n    }\n    if (this.parent) this.parent.children.add(this);\n    this.update(this.props, this.presenceContext);\n  }\n  unmount() {\n    visualElementStore.delete(this.current);\n    this.projection && this.projection.unmount();\n    cancelFrame(this.notifyUpdate);\n    cancelFrame(this.render);\n    this.valueSubscriptions.forEach(remove => remove());\n    this.removeFromVariantTree && this.removeFromVariantTree();\n    this.parent && this.parent.children.delete(this);\n    for (const key in this.events) {\n      this.events[key].clear();\n    }\n    for (const key in this.features) {\n      this.features[key].unmount();\n    }\n    this.current = null;\n  }\n  bindToMotionValue(key, value) {\n    const valueIsTransform = transformProps.has(key);\n    const removeOnChange = value.on(\"change\", latestValue => {\n      this.latestValues[key] = latestValue;\n      this.props.onUpdate && frame.update(this.notifyUpdate, false, true);\n      if (valueIsTransform && this.projection) {\n        this.projection.isTransformDirty = true;\n      }\n    });\n    const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n    this.valueSubscriptions.set(key, () => {\n      removeOnChange();\n      removeOnRenderRequest();\n    });\n  }\n  sortNodePosition(other) {\n    /**\n     * If these nodes aren't even of the same type we can't compare their depth.\n     */\n    if (!this.current || !this.sortInstanceNodePosition || this.type !== other.type) {\n      return 0;\n    }\n    return this.sortInstanceNodePosition(this.current, other.current);\n  }\n  loadFeatures(_ref2, isStrict, preloadedFeatures, initialLayoutGroupConfig) {\n    let {\n        children\n      } = _ref2,\n      renderedProps = _objectWithoutProperties(_ref2, _excluded2);\n    let ProjectionNodeConstructor;\n    let MeasureLayout;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if (process.env.NODE_ENV !== \"production\" && preloadedFeatures && isStrict) {\n      const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n      renderedProps.ignoreStrict ? warning(false, strictMessage) : invariant(false, strictMessage);\n    }\n    for (let i = 0; i < numFeatures; i++) {\n      const name = featureNames[i];\n      const {\n        isEnabled,\n        Feature: FeatureConstructor,\n        ProjectionNode,\n        MeasureLayout: MeasureLayoutComponent\n      } = featureDefinitions[name];\n      if (ProjectionNode) ProjectionNodeConstructor = ProjectionNode;\n      if (isEnabled(renderedProps)) {\n        if (!this.features[name] && FeatureConstructor) {\n          this.features[name] = new FeatureConstructor(this);\n        }\n        if (MeasureLayoutComponent) {\n          MeasureLayout = MeasureLayoutComponent;\n        }\n      }\n    }\n    if ((this.type === \"html\" || this.type === \"svg\") && !this.projection && ProjectionNodeConstructor) {\n      this.projection = new ProjectionNodeConstructor(this.latestValues, this.parent && this.parent.projection);\n      const {\n        layoutId,\n        layout,\n        drag,\n        dragConstraints,\n        layoutScroll,\n        layoutRoot\n      } = renderedProps;\n      this.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || dragConstraints && isRefObject(dragConstraints),\n        visualElement: this,\n        scheduleRender: () => this.scheduleRender(),\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig: initialLayoutGroupConfig,\n        layoutScroll,\n        layoutRoot\n      });\n    }\n    return MeasureLayout;\n  }\n  updateFeatures() {\n    for (const key in this.features) {\n      const feature = this.features[key];\n      if (feature.isMounted) {\n        feature.update();\n      } else {\n        feature.mount();\n        feature.isMounted = true;\n      }\n    }\n  }\n  triggerBuild() {\n    this.build(this.renderState, this.latestValues, this.options, this.props);\n  }\n  /**\n   * Measure the current viewport box with or without transforms.\n   * Only measures axis-aligned boxes, rotate and skew must be manually\n   * removed with a re-render to work.\n   */\n  measureViewportBox() {\n    return this.current ? this.measureInstanceViewportBox(this.current, this.props) : createBox();\n  }\n  getStaticValue(key) {\n    return this.latestValues[key];\n  }\n  setStaticValue(key, value) {\n    this.latestValues[key] = value;\n  }\n  /**\n   * Make a target animatable by Popmotion. For instance, if we're\n   * trying to animate width from 100px to 100vw we need to measure 100vw\n   * in pixels to determine what we really need to animate to. This is also\n   * pluggable to support Framer's custom value types like Color,\n   * and CSS variables.\n   */\n  makeTargetAnimatable(target) {\n    let canMutate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    return this.makeTargetAnimatableFromInstance(target, this.props, canMutate);\n  }\n  /**\n   * Update the provided props. Ensure any newly-added motion values are\n   * added to our map, old ones removed, and listeners updated.\n   */\n  update(props, presenceContext) {\n    if (props.transformTemplate || this.props.transformTemplate) {\n      this.scheduleRender();\n    }\n    this.prevProps = this.props;\n    this.props = props;\n    this.prevPresenceContext = this.presenceContext;\n    this.presenceContext = presenceContext;\n    /**\n     * Update prop event handlers ie onAnimationStart, onAnimationComplete\n     */\n    for (let i = 0; i < propEventHandlers.length; i++) {\n      const key = propEventHandlers[i];\n      if (this.propEventSubscriptions[key]) {\n        this.propEventSubscriptions[key]();\n        delete this.propEventSubscriptions[key];\n      }\n      const listener = props[\"on\" + key];\n      if (listener) {\n        this.propEventSubscriptions[key] = this.on(key, listener);\n      }\n    }\n    this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps), this.prevMotionValues);\n    if (this.handleChildMotionValue) {\n      this.handleChildMotionValue();\n    }\n  }\n  getProps() {\n    return this.props;\n  }\n  /**\n   * Returns the variant definition with a given name.\n   */\n  getVariant(name) {\n    return this.props.variants ? this.props.variants[name] : undefined;\n  }\n  /**\n   * Returns the defined default transition on this component.\n   */\n  getDefaultTransition() {\n    return this.props.transition;\n  }\n  getTransformPagePoint() {\n    return this.props.transformPagePoint;\n  }\n  getClosestVariantNode() {\n    return this.isVariantNode ? this : this.parent ? this.parent.getClosestVariantNode() : undefined;\n  }\n  getVariantContext() {\n    let startAtParent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    if (startAtParent) {\n      return this.parent ? this.parent.getVariantContext() : undefined;\n    }\n    if (!this.isControllingVariants) {\n      const context = this.parent ? this.parent.getVariantContext() || {} : {};\n      if (this.props.initial !== undefined) {\n        context.initial = this.props.initial;\n      }\n      return context;\n    }\n    const context = {};\n    for (let i = 0; i < numVariantProps; i++) {\n      const name = variantProps[i];\n      const prop = this.props[name];\n      if (isVariantLabel(prop) || prop === false) {\n        context[name] = prop;\n      }\n    }\n    return context;\n  }\n  /**\n   * Add a child visual element to our set of children.\n   */\n  addVariantChild(child) {\n    const closestVariantNode = this.getClosestVariantNode();\n    if (closestVariantNode) {\n      closestVariantNode.variantChildren && closestVariantNode.variantChildren.add(child);\n      return () => closestVariantNode.variantChildren.delete(child);\n    }\n  }\n  /**\n   * Add a motion value and bind it to this visual element.\n   */\n  addValue(key, value) {\n    // Remove existing value if it exists\n    if (value !== this.values.get(key)) {\n      this.removeValue(key);\n      this.bindToMotionValue(key, value);\n    }\n    this.values.set(key, value);\n    this.latestValues[key] = value.get();\n  }\n  /**\n   * Remove a motion value and unbind any active subscriptions.\n   */\n  removeValue(key) {\n    this.values.delete(key);\n    const unsubscribe = this.valueSubscriptions.get(key);\n    if (unsubscribe) {\n      unsubscribe();\n      this.valueSubscriptions.delete(key);\n    }\n    delete this.latestValues[key];\n    this.removeValueFromRenderState(key, this.renderState);\n  }\n  /**\n   * Check whether we have a motion value for this key\n   */\n  hasValue(key) {\n    return this.values.has(key);\n  }\n  getValue(key, defaultValue) {\n    if (this.props.values && this.props.values[key]) {\n      return this.props.values[key];\n    }\n    let value = this.values.get(key);\n    if (value === undefined && defaultValue !== undefined) {\n      value = motionValue(defaultValue, {\n        owner: this\n      });\n      this.addValue(key, value);\n    }\n    return value;\n  }\n  /**\n   * If we're trying to animate to a previously unencountered value,\n   * we need to check for it in our state and as a last resort read it\n   * directly from the instance (which might have performance implications).\n   */\n  readValue(key) {\n    var _a;\n    return this.latestValues[key] !== undefined || !this.current ? this.latestValues[key] : (_a = this.getBaseTargetFromProps(this.props, key)) !== null && _a !== void 0 ? _a : this.readValueFromInstance(this.current, key, this.options);\n  }\n  /**\n   * Set the base target to later animate back to. This is currently\n   * only hydrated on creation and when we first read a value.\n   */\n  setBaseTarget(key, value) {\n    this.baseTarget[key] = value;\n  }\n  /**\n   * Find the base target for a value thats been removed from all animation\n   * props.\n   */\n  getBaseTarget(key) {\n    var _a;\n    const {\n      initial\n    } = this.props;\n    const valueFromInitial = typeof initial === \"string\" || typeof initial === \"object\" ? (_a = resolveVariantFromProps(this.props, initial)) === null || _a === void 0 ? void 0 : _a[key] : undefined;\n    /**\n     * If this value still exists in the current initial variant, read that.\n     */\n    if (initial && valueFromInitial !== undefined) {\n      return valueFromInitial;\n    }\n    /**\n     * Alternatively, if this VisualElement config has defined a getBaseTarget\n     * so we can read the value from an alternative source, try that.\n     */\n    const target = this.getBaseTargetFromProps(this.props, key);\n    if (target !== undefined && !isMotionValue(target)) return target;\n    /**\n     * If the value was initially defined on initial, but it doesn't any more,\n     * return undefined. Otherwise return the value as initially read from the DOM.\n     */\n    return this.initialValues[key] !== undefined && valueFromInitial === undefined ? undefined : this.baseTarget[key];\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    return this.events[eventName].add(callback);\n  }\n  notify(eventName) {\n    if (this.events[eventName]) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      this.events[eventName].notify(...args);\n    }\n  }\n}\nexport { VisualElement };", "map": {"version": 3, "names": ["warning", "invariant", "createBox", "isRefObject", "initPrefersReducedMotion", "hasReducedMotionListener", "prefersReducedMotion", "SubscriptionManager", "motionValue", "isWillChangeMotionValue", "isMotionValue", "transformProps", "isControllingVariants", "isVariantNode", "isVariantLabel", "updateMotionValuesFromProps", "resolveVariantFromProps", "warnOnce", "featureDefinitions", "variantProps", "visualElementStore", "frame", "cancelFrame", "featureNames", "Object", "keys", "numFeatures", "length", "propEventHandlers", "numVariantProps", "VisualElement", "constructor", "_ref", "parent", "props", "presenceContext", "reducedMotionConfig", "visualState", "options", "arguments", "undefined", "current", "children", "Set", "shouldReduceMotion", "values", "Map", "features", "valueSubscriptions", "prevMotionValues", "events", "propEventSubscriptions", "notifyUpdate", "notify", "latestValues", "render", "triggerBuild", "renderInstance", "renderState", "style", "projection", "scheduleRender", "baseTarget", "_objectSpread", "initialValues", "initial", "depth", "variant<PERSON><PERSON><PERSON>n", "manuallyAnimateOnMount", "Boolean", "_this$scrapeMotionVal", "scrapeMotionValuesFromProps", "<PERSON><PERSON><PERSON><PERSON>", "initialMotionValues", "_objectWithoutProperties", "_excluded", "key", "value", "set", "add", "_props", "_prevProps", "mount", "instance", "removeFromVariantTree", "addVariant<PERSON>hild", "for<PERSON>ach", "bindToMotionValue", "process", "env", "NODE_ENV", "update", "unmount", "delete", "remove", "clear", "valueIsTransform", "has", "removeOnChange", "on", "latestValue", "onUpdate", "isTransformDirty", "removeOnRenderRequest", "sortNodePosition", "other", "sortInstanceNodePosition", "type", "loadFeatures", "_ref2", "isStrict", "preloadedFeatures", "initialLayoutGroupConfig", "renderedProps", "_excluded2", "ProjectionNodeConstructor", "MeasureLayout", "strictMessage", "ignoreStrict", "i", "name", "isEnabled", "Feature", "FeatureConstructor", "ProjectionNode", "MeasureLayoutComponent", "layoutId", "layout", "drag", "dragConstraints", "layoutScroll", "layoutRoot", "setOptions", "alwaysMeasureLayout", "visualElement", "animationType", "initialPromotionConfig", "updateFeatures", "feature", "isMounted", "build", "measureViewportBox", "measureInstanceViewportBox", "getStaticValue", "setStaticValue", "makeTargetAnimatable", "target", "canMutate", "makeTargetAnimatableFromInstance", "transformTemplate", "prevProps", "prevPresenceContext", "listener", "handleChildMotionValue", "getProps", "getVariant", "variants", "getDefaultTransition", "transition", "getTransformPagePoint", "transformPagePoint", "getClosestVariantNode", "getVariantContext", "startAtParent", "context", "prop", "child", "closestVariantNode", "addValue", "get", "removeValue", "unsubscribe", "removeValueFromRenderState", "hasValue", "getValue", "defaultValue", "owner", "readValue", "_a", "getBaseTargetFromProps", "readValueFromInstance", "set<PERSON><PERSON><PERSON><PERSON>get", "getBase<PERSON>arget", "valueFromInitial", "eventName", "callback", "_len", "args", "Array", "_key"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/render/VisualElement.mjs"], "sourcesContent": ["import { warning, invariant } from '../utils/errors.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { isRefObject } from '../utils/is-ref-object.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { motionValue } from '../value/index.mjs';\nimport { isWillChangeMotionValue } from '../value/use-will-change/is.mjs';\nimport { isMotionValue } from '../value/utils/is-motion-value.mjs';\nimport { transformProps } from './html/utils/transform.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from './utils/is-variant-label.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\nimport { warnOnce } from '../utils/warn-once.mjs';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { variantProps } from './utils/variant-props.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { frame, cancelFrame } from '../frameloop/frame.mjs';\n\nconst featureNames = Object.keys(featureDefinitions);\nconst numFeatures = featureNames.length;\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\nconst numVariantProps = variantProps.length;\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    constructor({ parent, props, presenceContext, reducedMotionConfig, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.scheduleRender = () => frame.render(this.render, false, true);\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't neccessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {});\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key], false);\n                if (isWillChangeMotionValue(willChange)) {\n                    willChange.add(key);\n                }\n            }\n        }\n    }\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps) {\n        return {};\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        visualElementStore.delete(this.current);\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            this.features[key].unmount();\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        const valueIsTransform = transformProps.has(key);\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate &&\n                frame.update(this.notifyUpdate, false, true);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    loadFeatures({ children, ...renderedProps }, isStrict, preloadedFeatures, initialLayoutGroupConfig) {\n        let ProjectionNodeConstructor;\n        let MeasureLayout;\n        /**\n         * If we're in development mode, check to make sure we're not rendering a motion component\n         * as a child of LazyMotion, as this will break the file-size benefits of using it.\n         */\n        if (process.env.NODE_ENV !== \"production\" &&\n            preloadedFeatures &&\n            isStrict) {\n            const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n            renderedProps.ignoreStrict\n                ? warning(false, strictMessage)\n                : invariant(false, strictMessage);\n        }\n        for (let i = 0; i < numFeatures; i++) {\n            const name = featureNames[i];\n            const { isEnabled, Feature: FeatureConstructor, ProjectionNode, MeasureLayout: MeasureLayoutComponent, } = featureDefinitions[name];\n            if (ProjectionNode)\n                ProjectionNodeConstructor = ProjectionNode;\n            if (isEnabled(renderedProps)) {\n                if (!this.features[name] && FeatureConstructor) {\n                    this.features[name] = new FeatureConstructor(this);\n                }\n                if (MeasureLayoutComponent) {\n                    MeasureLayout = MeasureLayoutComponent;\n                }\n            }\n        }\n        if ((this.type === \"html\" || this.type === \"svg\") &&\n            !this.projection &&\n            ProjectionNodeConstructor) {\n            this.projection = new ProjectionNodeConstructor(this.latestValues, this.parent && this.parent.projection);\n            const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, } = renderedProps;\n            this.projection.setOptions({\n                layoutId,\n                layout,\n                alwaysMeasureLayout: Boolean(drag) ||\n                    (dragConstraints && isRefObject(dragConstraints)),\n                visualElement: this,\n                scheduleRender: () => this.scheduleRender(),\n                /**\n                 * TODO: Update options in an effect. This could be tricky as it'll be too late\n                 * to update by the time layout animations run.\n                 * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n                 * ensuring it gets called if there's no potential layout animations.\n                 *\n                 */\n                animationType: typeof layout === \"string\" ? layout : \"both\",\n                initialPromotionConfig: initialLayoutGroupConfig,\n                layoutScroll,\n                layoutRoot,\n            });\n        }\n        return MeasureLayout;\n    }\n    updateFeatures() {\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature.isMounted) {\n                feature.update();\n            }\n            else {\n                feature.mount();\n                feature.isMounted = true;\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.options, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Make a target animatable by Popmotion. For instance, if we're\n     * trying to animate width from 100px to 100vw we need to measure 100vw\n     * in pixels to determine what we really need to animate to. This is also\n     * pluggable to support Framer's custom value types like Color,\n     * and CSS variables.\n     */\n    makeTargetAnimatable(target, canMutate = true) {\n        return this.makeTargetAnimatableFromInstance(target, this.props, canMutate);\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listener = props[\"on\" + key];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    getVariantContext(startAtParent = false) {\n        if (startAtParent) {\n            return this.parent ? this.parent.getVariantContext() : undefined;\n        }\n        if (!this.isControllingVariants) {\n            const context = this.parent\n                ? this.parent.getVariantContext() || {}\n                : {};\n            if (this.props.initial !== undefined) {\n                context.initial = this.props.initial;\n            }\n            return context;\n        }\n        const context = {};\n        for (let i = 0; i < numVariantProps; i++) {\n            const name = variantProps[i];\n            const prop = this.props[name];\n            if (isVariantLabel(prop) || prop === false) {\n                context[name] = prop;\n            }\n        }\n        return context;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        if (value !== this.values.get(key)) {\n            this.removeValue(key);\n            this.bindToMotionValue(key, value);\n        }\n        this.values.set(key, value);\n        this.latestValues[key] = value.get();\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key) {\n        var _a;\n        return this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : (_a = this.getBaseTargetFromProps(this.props, key)) !== null && _a !== void 0 ? _a : this.readValueFromInstance(this.current, key, this.options);\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        var _a;\n        const { initial } = this.props;\n        const valueFromInitial = typeof initial === \"string\" || typeof initial === \"object\"\n            ? (_a = resolveVariantFromProps(this.props, initial)) === null || _a === void 0 ? void 0 : _a[key]\n            : undefined;\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\nexport { VisualElement };\n"], "mappings": ";;;;AAAA,SAASA,OAAO,EAAEC,SAAS,QAAQ,qBAAqB;AACxD,SAASC,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAClG,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,uBAAuB,QAAQ,iCAAiC;AACzE,SAASC,aAAa,QAAQ,oCAAoC;AAClE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,qCAAqC;AAC1F,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,2BAA2B,QAAQ,2BAA2B;AACvE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,kBAAkB,QAAQ,aAAa;AAChD,SAASC,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;AAE3D,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACP,kBAAkB,CAAC;AACpD,MAAMQ,WAAW,GAAGH,YAAY,CAACI,MAAM;AACvC,MAAMC,iBAAiB,GAAG,CACtB,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,qBAAqB,EACrB,eAAe,EACf,sBAAsB,EACtB,yBAAyB,CAC5B;AACD,MAAMC,eAAe,GAAGV,YAAY,CAACQ,MAAM;AAC3C;AACA;AACA;AACA;AACA,MAAMG,aAAa,CAAC;EAChBC,WAAWA,CAAAC,IAAA,EAAsF;IAAA,IAArF;MAAEC,MAAM;MAAEC,KAAK;MAAEC,eAAe;MAAEC,mBAAmB;MAAEC;IAAa,CAAC,GAAAL,IAAA;IAAA,IAAEM,OAAO,GAAAC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAC3F;AACR;AACA;AACA;IACQ,IAAI,CAACE,OAAO,GAAG,IAAI;IACnB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB;AACR;AACA;IACQ,IAAI,CAAC9B,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACgC,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IACvB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACG,gBAAgB,GAAG,CAAC,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;IAChC,IAAI,CAACC,YAAY,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC;IAClE,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAC,IAAI,CAACd,OAAO,EACb;MACJ,IAAI,CAACe,YAAY,CAAC,CAAC;MACnB,IAAI,CAACC,cAAc,CAAC,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACiB,WAAW,EAAE,IAAI,CAACxB,KAAK,CAACyB,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;IAC1F,CAAC;IACD,IAAI,CAACC,cAAc,GAAG,MAAMxC,KAAK,CAACkC,MAAM,CAAC,IAAI,CAACA,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;IAClE,MAAM;MAAED,YAAY;MAAEI;IAAY,CAAC,GAAGrB,WAAW;IACjD,IAAI,CAACiB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACQ,UAAU,GAAAC,aAAA,KAAQT,YAAY,CAAE;IACrC,IAAI,CAACU,aAAa,GAAG9B,KAAK,CAAC+B,OAAO,GAAAF,aAAA,KAAQT,YAAY,IAAK,CAAC,CAAC;IAC7D,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACzB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC+B,KAAK,GAAGjC,MAAM,GAAGA,MAAM,CAACiC,KAAK,GAAG,CAAC,GAAG,CAAC;IAC1C,IAAI,CAAC9B,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC1B,qBAAqB,GAAGA,qBAAqB,CAACsB,KAAK,CAAC;IACzD,IAAI,CAACrB,aAAa,GAAGA,aAAa,CAACqB,KAAK,CAAC;IACzC,IAAI,IAAI,CAACrB,aAAa,EAAE;MACpB,IAAI,CAACsD,eAAe,GAAG,IAAIxB,GAAG,CAAC,CAAC;IACpC;IACA,IAAI,CAACyB,sBAAsB,GAAGC,OAAO,CAACpC,MAAM,IAAIA,MAAM,CAACQ,OAAO,CAAC;IAC/D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAA6B,qBAAA,GAA+C,IAAI,CAACC,2BAA2B,CAACrC,KAAK,EAAE,CAAC,CAAC,CAAC;MAApF;QAAEsC;MAAmC,CAAC,GAAAF,qBAAA;MAArBG,mBAAmB,GAAAC,wBAAA,CAAAJ,qBAAA,EAAAK,SAAA;IAC1C,KAAK,MAAMC,GAAG,IAAIH,mBAAmB,EAAE;MACnC,MAAMI,KAAK,GAAGJ,mBAAmB,CAACG,GAAG,CAAC;MACtC,IAAItB,YAAY,CAACsB,GAAG,CAAC,KAAKpC,SAAS,IAAI9B,aAAa,CAACmE,KAAK,CAAC,EAAE;QACzDA,KAAK,CAACC,GAAG,CAACxB,YAAY,CAACsB,GAAG,CAAC,EAAE,KAAK,CAAC;QACnC,IAAInE,uBAAuB,CAAC+D,UAAU,CAAC,EAAE;UACrCA,UAAU,CAACO,GAAG,CAACH,GAAG,CAAC;QACvB;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIL,2BAA2BA,CAACS,MAAM,EAAEC,UAAU,EAAE;IAC5C,OAAO,CAAC,CAAC;EACb;EACAC,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAAC1C,OAAO,GAAG0C,QAAQ;IACvB/D,kBAAkB,CAAC0D,GAAG,CAACK,QAAQ,EAAE,IAAI,CAAC;IACtC,IAAI,IAAI,CAACvB,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACuB,QAAQ,EAAE;MAC9C,IAAI,CAACvB,UAAU,CAACsB,KAAK,CAACC,QAAQ,CAAC;IACnC;IACA,IAAI,IAAI,CAAClD,MAAM,IAAI,IAAI,CAACpB,aAAa,IAAI,CAAC,IAAI,CAACD,qBAAqB,EAAE;MAClE,IAAI,CAACwE,qBAAqB,GAAG,IAAI,CAACnD,MAAM,CAACoD,eAAe,CAAC,IAAI,CAAC;IAClE;IACA,IAAI,CAACxC,MAAM,CAACyC,OAAO,CAAC,CAACT,KAAK,EAAED,GAAG,KAAK,IAAI,CAACW,iBAAiB,CAACX,GAAG,EAAEC,KAAK,CAAC,CAAC;IACvE,IAAI,CAACxE,wBAAwB,CAACoC,OAAO,EAAE;MACnCrC,wBAAwB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACwC,kBAAkB,GACnB,IAAI,CAACR,mBAAmB,KAAK,OAAO,GAC9B,KAAK,GACL,IAAI,CAACA,mBAAmB,KAAK,QAAQ,GACjC,IAAI,GACJ9B,oBAAoB,CAACmC,OAAO;IAC1C,IAAI+C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvCzE,QAAQ,CAAC,IAAI,CAAC2B,kBAAkB,KAAK,IAAI,EAAE,wFAAwF,CAAC;IACxI;IACA,IAAI,IAAI,CAACX,MAAM,EACX,IAAI,CAACA,MAAM,CAACS,QAAQ,CAACqC,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,CAACY,MAAM,CAAC,IAAI,CAACzD,KAAK,EAAE,IAAI,CAACC,eAAe,CAAC;EACjD;EACAyD,OAAOA,CAAA,EAAG;IACNxE,kBAAkB,CAACyE,MAAM,CAAC,IAAI,CAACpD,OAAO,CAAC;IACvC,IAAI,CAACmB,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgC,OAAO,CAAC,CAAC;IAC5CtE,WAAW,CAAC,IAAI,CAAC8B,YAAY,CAAC;IAC9B9B,WAAW,CAAC,IAAI,CAACiC,MAAM,CAAC;IACxB,IAAI,CAACP,kBAAkB,CAACsC,OAAO,CAAEQ,MAAM,IAAKA,MAAM,CAAC,CAAC,CAAC;IACrD,IAAI,CAACV,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAAC,CAAC;IAC1D,IAAI,CAACnD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACS,QAAQ,CAACmD,MAAM,CAAC,IAAI,CAAC;IAChD,KAAK,MAAMjB,GAAG,IAAI,IAAI,CAAC1B,MAAM,EAAE;MAC3B,IAAI,CAACA,MAAM,CAAC0B,GAAG,CAAC,CAACmB,KAAK,CAAC,CAAC;IAC5B;IACA,KAAK,MAAMnB,GAAG,IAAI,IAAI,CAAC7B,QAAQ,EAAE;MAC7B,IAAI,CAACA,QAAQ,CAAC6B,GAAG,CAAC,CAACgB,OAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAACnD,OAAO,GAAG,IAAI;EACvB;EACA8C,iBAAiBA,CAACX,GAAG,EAAEC,KAAK,EAAE;IAC1B,MAAMmB,gBAAgB,GAAGrF,cAAc,CAACsF,GAAG,CAACrB,GAAG,CAAC;IAChD,MAAMsB,cAAc,GAAGrB,KAAK,CAACsB,EAAE,CAAC,QAAQ,EAAGC,WAAW,IAAK;MACvD,IAAI,CAAC9C,YAAY,CAACsB,GAAG,CAAC,GAAGwB,WAAW;MACpC,IAAI,CAAClE,KAAK,CAACmE,QAAQ,IACfhF,KAAK,CAACsE,MAAM,CAAC,IAAI,CAACvC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC;MAChD,IAAI4C,gBAAgB,IAAI,IAAI,CAACpC,UAAU,EAAE;QACrC,IAAI,CAACA,UAAU,CAAC0C,gBAAgB,GAAG,IAAI;MAC3C;IACJ,CAAC,CAAC;IACF,MAAMC,qBAAqB,GAAG1B,KAAK,CAACsB,EAAE,CAAC,eAAe,EAAE,IAAI,CAACtC,cAAc,CAAC;IAC5E,IAAI,CAACb,kBAAkB,CAAC8B,GAAG,CAACF,GAAG,EAAE,MAAM;MACnCsB,cAAc,CAAC,CAAC;MAChBK,qBAAqB,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAACC,KAAK,EAAE;IACpB;AACR;AACA;IACQ,IAAI,CAAC,IAAI,CAAChE,OAAO,IACb,CAAC,IAAI,CAACiE,wBAAwB,IAC9B,IAAI,CAACC,IAAI,KAAKF,KAAK,CAACE,IAAI,EAAE;MAC1B,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACD,wBAAwB,CAAC,IAAI,CAACjE,OAAO,EAAEgE,KAAK,CAAChE,OAAO,CAAC;EACrE;EACAmE,YAAYA,CAAAC,KAAA,EAAiCC,QAAQ,EAAEC,iBAAiB,EAAEC,wBAAwB,EAAE;IAAA,IAAvF;QAAEtE;MAA2B,CAAC,GAAAmE,KAAA;MAAfI,aAAa,GAAAvC,wBAAA,CAAAmC,KAAA,EAAAK,UAAA;IACrC,IAAIC,yBAAyB;IAC7B,IAAIC,aAAa;IACjB;AACR;AACA;AACA;IACQ,IAAI5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCqB,iBAAiB,IACjBD,QAAQ,EAAE;MACV,MAAMO,aAAa,GAAG,kJAAkJ;MACxKJ,aAAa,CAACK,YAAY,GACpBtH,OAAO,CAAC,KAAK,EAAEqH,aAAa,CAAC,GAC7BpH,SAAS,CAAC,KAAK,EAAEoH,aAAa,CAAC;IACzC;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7F,WAAW,EAAE6F,CAAC,EAAE,EAAE;MAClC,MAAMC,IAAI,GAAGjG,YAAY,CAACgG,CAAC,CAAC;MAC5B,MAAM;QAAEE,SAAS;QAAEC,OAAO,EAAEC,kBAAkB;QAAEC,cAAc;QAAER,aAAa,EAAES;MAAwB,CAAC,GAAG3G,kBAAkB,CAACsG,IAAI,CAAC;MACnI,IAAII,cAAc,EACdT,yBAAyB,GAAGS,cAAc;MAC9C,IAAIH,SAAS,CAACR,aAAa,CAAC,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAClE,QAAQ,CAACyE,IAAI,CAAC,IAAIG,kBAAkB,EAAE;UAC5C,IAAI,CAAC5E,QAAQ,CAACyE,IAAI,CAAC,GAAG,IAAIG,kBAAkB,CAAC,IAAI,CAAC;QACtD;QACA,IAAIE,sBAAsB,EAAE;UACxBT,aAAa,GAAGS,sBAAsB;QAC1C;MACJ;IACJ;IACA,IAAI,CAAC,IAAI,CAAClB,IAAI,KAAK,MAAM,IAAI,IAAI,CAACA,IAAI,KAAK,KAAK,KAC5C,CAAC,IAAI,CAAC/C,UAAU,IAChBuD,yBAAyB,EAAE;MAC3B,IAAI,CAACvD,UAAU,GAAG,IAAIuD,yBAAyB,CAAC,IAAI,CAAC7D,YAAY,EAAE,IAAI,CAACrB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC2B,UAAU,CAAC;MACzG,MAAM;QAAEkE,QAAQ;QAAEC,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC,YAAY;QAAEC;MAAY,CAAC,GAAGlB,aAAa;MAC5F,IAAI,CAACrD,UAAU,CAACwE,UAAU,CAAC;QACvBN,QAAQ;QACRC,MAAM;QACNM,mBAAmB,EAAEhE,OAAO,CAAC2D,IAAI,CAAC,IAC7BC,eAAe,IAAI9H,WAAW,CAAC8H,eAAe,CAAE;QACrDK,aAAa,EAAE,IAAI;QACnBzE,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACA,cAAc,CAAC,CAAC;QAC3C;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB0E,aAAa,EAAE,OAAOR,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,MAAM;QAC3DS,sBAAsB,EAAExB,wBAAwB;QAChDkB,YAAY;QACZC;MACJ,CAAC,CAAC;IACN;IACA,OAAOf,aAAa;EACxB;EACAqB,cAAcA,CAAA,EAAG;IACb,KAAK,MAAM7D,GAAG,IAAI,IAAI,CAAC7B,QAAQ,EAAE;MAC7B,MAAM2F,OAAO,GAAG,IAAI,CAAC3F,QAAQ,CAAC6B,GAAG,CAAC;MAClC,IAAI8D,OAAO,CAACC,SAAS,EAAE;QACnBD,OAAO,CAAC/C,MAAM,CAAC,CAAC;MACpB,CAAC,MACI;QACD+C,OAAO,CAACxD,KAAK,CAAC,CAAC;QACfwD,OAAO,CAACC,SAAS,GAAG,IAAI;MAC5B;IACJ;EACJ;EACAnF,YAAYA,CAAA,EAAG;IACX,IAAI,CAACoF,KAAK,CAAC,IAAI,CAAClF,WAAW,EAAE,IAAI,CAACJ,YAAY,EAAE,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACJ,KAAK,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;EACI2G,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACpG,OAAO,GACb,IAAI,CAACqG,0BAA0B,CAAC,IAAI,CAACrG,OAAO,EAAE,IAAI,CAACP,KAAK,CAAC,GACzDhC,SAAS,CAAC,CAAC;EACrB;EACA6I,cAAcA,CAACnE,GAAG,EAAE;IAChB,OAAO,IAAI,CAACtB,YAAY,CAACsB,GAAG,CAAC;EACjC;EACAoE,cAAcA,CAACpE,GAAG,EAAEC,KAAK,EAAE;IACvB,IAAI,CAACvB,YAAY,CAACsB,GAAG,CAAC,GAAGC,KAAK;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIoE,oBAAoBA,CAACC,MAAM,EAAoB;IAAA,IAAlBC,SAAS,GAAA5G,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;IACzC,OAAO,IAAI,CAAC6G,gCAAgC,CAACF,MAAM,EAAE,IAAI,CAAChH,KAAK,EAAEiH,SAAS,CAAC;EAC/E;EACA;AACJ;AACA;AACA;EACIxD,MAAMA,CAACzD,KAAK,EAAEC,eAAe,EAAE;IAC3B,IAAID,KAAK,CAACmH,iBAAiB,IAAI,IAAI,CAACnH,KAAK,CAACmH,iBAAiB,EAAE;MACzD,IAAI,CAACxF,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACyF,SAAS,GAAG,IAAI,CAACpH,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqH,mBAAmB,GAAG,IAAI,CAACpH,eAAe;IAC/C,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;IACQ,KAAK,IAAIoF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3F,iBAAiB,CAACD,MAAM,EAAE4F,CAAC,EAAE,EAAE;MAC/C,MAAM3C,GAAG,GAAGhD,iBAAiB,CAAC2F,CAAC,CAAC;MAChC,IAAI,IAAI,CAACpE,sBAAsB,CAACyB,GAAG,CAAC,EAAE;QAClC,IAAI,CAACzB,sBAAsB,CAACyB,GAAG,CAAC,CAAC,CAAC;QAClC,OAAO,IAAI,CAACzB,sBAAsB,CAACyB,GAAG,CAAC;MAC3C;MACA,MAAM4E,QAAQ,GAAGtH,KAAK,CAAC,IAAI,GAAG0C,GAAG,CAAC;MAClC,IAAI4E,QAAQ,EAAE;QACV,IAAI,CAACrG,sBAAsB,CAACyB,GAAG,CAAC,GAAG,IAAI,CAACuB,EAAE,CAACvB,GAAG,EAAE4E,QAAQ,CAAC;MAC7D;IACJ;IACA,IAAI,CAACvG,gBAAgB,GAAGlC,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAACwD,2BAA2B,CAACrC,KAAK,EAAE,IAAI,CAACoH,SAAS,CAAC,EAAE,IAAI,CAACrG,gBAAgB,CAAC;IACzI,IAAI,IAAI,CAACwG,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxH,KAAK;EACrB;EACA;AACJ;AACA;EACIyH,UAAUA,CAACnC,IAAI,EAAE;IACb,OAAO,IAAI,CAACtF,KAAK,CAAC0H,QAAQ,GAAG,IAAI,CAAC1H,KAAK,CAAC0H,QAAQ,CAACpC,IAAI,CAAC,GAAGhF,SAAS;EACtE;EACA;AACJ;AACA;EACIqH,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC3H,KAAK,CAAC4H,UAAU;EAChC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC7H,KAAK,CAAC8H,kBAAkB;EACxC;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpJ,aAAa,GACnB,IAAI,GACJ,IAAI,CAACoB,MAAM,GACP,IAAI,CAACA,MAAM,CAACgI,qBAAqB,CAAC,CAAC,GACnCzH,SAAS;EACvB;EACA0H,iBAAiBA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAA5H,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IACnC,IAAI4H,aAAa,EAAE;MACf,OAAO,IAAI,CAAClI,MAAM,GAAG,IAAI,CAACA,MAAM,CAACiI,iBAAiB,CAAC,CAAC,GAAG1H,SAAS;IACpE;IACA,IAAI,CAAC,IAAI,CAAC5B,qBAAqB,EAAE;MAC7B,MAAMwJ,OAAO,GAAG,IAAI,CAACnI,MAAM,GACrB,IAAI,CAACA,MAAM,CAACiI,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,GACrC,CAAC,CAAC;MACR,IAAI,IAAI,CAAChI,KAAK,CAAC+B,OAAO,KAAKzB,SAAS,EAAE;QAClC4H,OAAO,CAACnG,OAAO,GAAG,IAAI,CAAC/B,KAAK,CAAC+B,OAAO;MACxC;MACA,OAAOmG,OAAO;IAClB;IACA,MAAMA,OAAO,GAAG,CAAC,CAAC;IAClB,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1F,eAAe,EAAE0F,CAAC,EAAE,EAAE;MACtC,MAAMC,IAAI,GAAGrG,YAAY,CAACoG,CAAC,CAAC;MAC5B,MAAM8C,IAAI,GAAG,IAAI,CAACnI,KAAK,CAACsF,IAAI,CAAC;MAC7B,IAAI1G,cAAc,CAACuJ,IAAI,CAAC,IAAIA,IAAI,KAAK,KAAK,EAAE;QACxCD,OAAO,CAAC5C,IAAI,CAAC,GAAG6C,IAAI;MACxB;IACJ;IACA,OAAOD,OAAO;EAClB;EACA;AACJ;AACA;EACI/E,eAAeA,CAACiF,KAAK,EAAE;IACnB,MAAMC,kBAAkB,GAAG,IAAI,CAACN,qBAAqB,CAAC,CAAC;IACvD,IAAIM,kBAAkB,EAAE;MACpBA,kBAAkB,CAACpG,eAAe,IAC9BoG,kBAAkB,CAACpG,eAAe,CAACY,GAAG,CAACuF,KAAK,CAAC;MACjD,OAAO,MAAMC,kBAAkB,CAACpG,eAAe,CAAC0B,MAAM,CAACyE,KAAK,CAAC;IACjE;EACJ;EACA;AACJ;AACA;EACIE,QAAQA,CAAC5F,GAAG,EAAEC,KAAK,EAAE;IACjB;IACA,IAAIA,KAAK,KAAK,IAAI,CAAChC,MAAM,CAAC4H,GAAG,CAAC7F,GAAG,CAAC,EAAE;MAChC,IAAI,CAAC8F,WAAW,CAAC9F,GAAG,CAAC;MACrB,IAAI,CAACW,iBAAiB,CAACX,GAAG,EAAEC,KAAK,CAAC;IACtC;IACA,IAAI,CAAChC,MAAM,CAACiC,GAAG,CAACF,GAAG,EAAEC,KAAK,CAAC;IAC3B,IAAI,CAACvB,YAAY,CAACsB,GAAG,CAAC,GAAGC,KAAK,CAAC4F,GAAG,CAAC,CAAC;EACxC;EACA;AACJ;AACA;EACIC,WAAWA,CAAC9F,GAAG,EAAE;IACb,IAAI,CAAC/B,MAAM,CAACgD,MAAM,CAACjB,GAAG,CAAC;IACvB,MAAM+F,WAAW,GAAG,IAAI,CAAC3H,kBAAkB,CAACyH,GAAG,CAAC7F,GAAG,CAAC;IACpD,IAAI+F,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;MACb,IAAI,CAAC3H,kBAAkB,CAAC6C,MAAM,CAACjB,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAACtB,YAAY,CAACsB,GAAG,CAAC;IAC7B,IAAI,CAACgG,0BAA0B,CAAChG,GAAG,EAAE,IAAI,CAAClB,WAAW,CAAC;EAC1D;EACA;AACJ;AACA;EACImH,QAAQA,CAACjG,GAAG,EAAE;IACV,OAAO,IAAI,CAAC/B,MAAM,CAACoD,GAAG,CAACrB,GAAG,CAAC;EAC/B;EACAkG,QAAQA,CAAClG,GAAG,EAAEmG,YAAY,EAAE;IACxB,IAAI,IAAI,CAAC7I,KAAK,CAACW,MAAM,IAAI,IAAI,CAACX,KAAK,CAACW,MAAM,CAAC+B,GAAG,CAAC,EAAE;MAC7C,OAAO,IAAI,CAAC1C,KAAK,CAACW,MAAM,CAAC+B,GAAG,CAAC;IACjC;IACA,IAAIC,KAAK,GAAG,IAAI,CAAChC,MAAM,CAAC4H,GAAG,CAAC7F,GAAG,CAAC;IAChC,IAAIC,KAAK,KAAKrC,SAAS,IAAIuI,YAAY,KAAKvI,SAAS,EAAE;MACnDqC,KAAK,GAAGrE,WAAW,CAACuK,YAAY,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MAClD,IAAI,CAACR,QAAQ,CAAC5F,GAAG,EAAEC,KAAK,CAAC;IAC7B;IACA,OAAOA,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACIoG,SAASA,CAACrG,GAAG,EAAE;IACX,IAAIsG,EAAE;IACN,OAAO,IAAI,CAAC5H,YAAY,CAACsB,GAAG,CAAC,KAAKpC,SAAS,IAAI,CAAC,IAAI,CAACC,OAAO,GACtD,IAAI,CAACa,YAAY,CAACsB,GAAG,CAAC,GACtB,CAACsG,EAAE,GAAG,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACjJ,KAAK,EAAE0C,GAAG,CAAC,MAAM,IAAI,IAAIsG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC3I,OAAO,EAAEmC,GAAG,EAAE,IAAI,CAACtC,OAAO,CAAC;EAC1J;EACA;AACJ;AACA;AACA;EACI+I,aAAaA,CAACzG,GAAG,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACf,UAAU,CAACc,GAAG,CAAC,GAAGC,KAAK;EAChC;EACA;AACJ;AACA;AACA;EACIyG,aAAaA,CAAC1G,GAAG,EAAE;IACf,IAAIsG,EAAE;IACN,MAAM;MAAEjH;IAAQ,CAAC,GAAG,IAAI,CAAC/B,KAAK;IAC9B,MAAMqJ,gBAAgB,GAAG,OAAOtH,OAAO,KAAK,QAAQ,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAC7E,CAACiH,EAAE,GAAGlK,uBAAuB,CAAC,IAAI,CAACkB,KAAK,EAAE+B,OAAO,CAAC,MAAM,IAAI,IAAIiH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACtG,GAAG,CAAC,GAChGpC,SAAS;IACf;AACR;AACA;IACQ,IAAIyB,OAAO,IAAIsH,gBAAgB,KAAK/I,SAAS,EAAE;MAC3C,OAAO+I,gBAAgB;IAC3B;IACA;AACR;AACA;AACA;IACQ,MAAMrC,MAAM,GAAG,IAAI,CAACiC,sBAAsB,CAAC,IAAI,CAACjJ,KAAK,EAAE0C,GAAG,CAAC;IAC3D,IAAIsE,MAAM,KAAK1G,SAAS,IAAI,CAAC9B,aAAa,CAACwI,MAAM,CAAC,EAC9C,OAAOA,MAAM;IACjB;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAAClF,aAAa,CAACY,GAAG,CAAC,KAAKpC,SAAS,IACxC+I,gBAAgB,KAAK/I,SAAS,GAC5BA,SAAS,GACT,IAAI,CAACsB,UAAU,CAACc,GAAG,CAAC;EAC9B;EACAuB,EAAEA,CAACqF,SAAS,EAAEC,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAACvI,MAAM,CAACsI,SAAS,CAAC,EAAE;MACzB,IAAI,CAACtI,MAAM,CAACsI,SAAS,CAAC,GAAG,IAAIjL,mBAAmB,CAAC,CAAC;IACtD;IACA,OAAO,IAAI,CAAC2C,MAAM,CAACsI,SAAS,CAAC,CAACzG,GAAG,CAAC0G,QAAQ,CAAC;EAC/C;EACApI,MAAMA,CAACmI,SAAS,EAAW;IACvB,IAAI,IAAI,CAACtI,MAAM,CAACsI,SAAS,CAAC,EAAE;MAAA,SAAAE,IAAA,GAAAnJ,SAAA,CAAAZ,MAAA,EADXgK,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAtJ,SAAA,CAAAsJ,IAAA;MAAA;MAEjB,IAAI,CAAC3I,MAAM,CAACsI,SAAS,CAAC,CAACnI,MAAM,CAAC,GAAGsI,IAAI,CAAC;IAC1C;EACJ;AACJ;AAEA,SAAS7J,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}