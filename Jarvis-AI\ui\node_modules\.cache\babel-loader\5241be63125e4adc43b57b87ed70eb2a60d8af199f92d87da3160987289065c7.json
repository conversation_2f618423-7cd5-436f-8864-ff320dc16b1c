{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\", \"isValidProp\"];\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig(_ref) {\n  let {\n      children,\n      isValidProp\n    } = _ref,\n    config = _objectWithoutProperties(_ref, _excluded);\n  isValidProp && loadExternalIsValidProp(isValidProp);\n  /**\n   * Inherit props from any parent MotionConfig components\n   */\n  config = _objectSpread(_objectSpread({}, useContext(MotionConfigContext)), config);\n  /**\n   * Don't allow isStatic to change between renders as it affects how many hooks\n   * motion components fire.\n   */\n  config.isStatic = useConstant(() => config.isStatic);\n  /**\n   * Creating a new config context object will re-render every `motion` component\n   * every time it renders. So we only want to create a new one sparingly.\n   */\n  const context = useMemo(() => config, [JSON.stringify(config.transition), config.transformPagePoint, config.reducedMotion]);\n  return React.createElement(MotionConfigContext.Provider, {\n    value: context\n  }, children);\n}\nexport { MotionConfig };", "map": {"version": 3, "names": ["React", "useContext", "useMemo", "MotionConfigContext", "loadExternalIsValidProp", "useConstant", "MotionConfig", "_ref", "children", "isValidProp", "config", "_objectWithoutProperties", "_excluded", "_objectSpread", "isStatic", "context", "JSON", "stringify", "transition", "transformPagePoint", "reducedMotion", "createElement", "Provider", "value"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { loadExternalIsValidProp } from '../../render/dom/utils/filter-props.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction MotionConfig({ children, isValidProp, ...config }) {\n    isValidProp && loadExternalIsValidProp(isValidProp);\n    /**\n     * Inherit props from any parent MotionConfig components\n     */\n    config = { ...useContext(MotionConfigContext), ...config };\n    /**\n     * Don't allow isStatic to change between renders as it affects how many hooks\n     * motion components fire.\n     */\n    config.isStatic = useConstant(() => config.isStatic);\n    /**\n     * Creating a new config context object will re-render every `motion` component\n     * every time it renders. So we only want to create a new one sparingly.\n     */\n    const context = useMemo(() => config, [JSON.stringify(config.transition), config.transformPagePoint, config.reducedMotion]);\n    return (React.createElement(MotionConfigContext.Provider, { value: context }, children));\n}\n\nexport { MotionConfig };\n"], "mappings": ";;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAAC,IAAA,EAAuC;EAAA,IAAtC;MAAEC,QAAQ;MAAEC;IAAuB,CAAC,GAAAF,IAAA;IAARG,MAAM,GAAAC,wBAAA,CAAAJ,IAAA,EAAAK,SAAA;EACpDH,WAAW,IAAIL,uBAAuB,CAACK,WAAW,CAAC;EACnD;AACJ;AACA;EACIC,MAAM,GAAAG,aAAA,CAAAA,aAAA,KAAQZ,UAAU,CAACE,mBAAmB,CAAC,GAAKO,MAAM,CAAE;EAC1D;AACJ;AACA;AACA;EACIA,MAAM,CAACI,QAAQ,GAAGT,WAAW,CAAC,MAAMK,MAAM,CAACI,QAAQ,CAAC;EACpD;AACJ;AACA;AACA;EACI,MAAMC,OAAO,GAAGb,OAAO,CAAC,MAAMQ,MAAM,EAAE,CAACM,IAAI,CAACC,SAAS,CAACP,MAAM,CAACQ,UAAU,CAAC,EAAER,MAAM,CAACS,kBAAkB,EAAET,MAAM,CAACU,aAAa,CAAC,CAAC;EAC3H,OAAQpB,KAAK,CAACqB,aAAa,CAAClB,mBAAmB,CAACmB,QAAQ,EAAE;IAAEC,KAAK,EAAER;EAAQ,CAAC,EAAEP,QAAQ,CAAC;AAC3F;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}