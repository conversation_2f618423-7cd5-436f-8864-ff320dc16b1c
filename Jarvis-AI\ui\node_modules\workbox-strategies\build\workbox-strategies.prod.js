this.workbox=this.workbox||{},this.workbox.strategies=function(t,e,s,r,n,a,i,o,c){"use strict";try{self["workbox:strategies:6.5.4"]&&_()}catch(t){}function h(t){return"string"==typeof t?new Request(t):t}class l{constructor(t,e){this.vt={},Object.assign(this,e),this.event=e.event,this.ht=t,this.bt=new n.Deferred,this.Et=[],this._t=[...t.plugins],this.kt=new Map;for(const t of this._t)this.kt.set(t,{});this.event.waitUntil(this.bt.promise)}async fetch(t){const{event:s}=this;let r=h(t);if("navigate"===r.mode&&s instanceof FetchEvent&&s.preloadResponse){const t=await s.preloadResponse;if(t)return t}const n=this.hasCallback("fetchDidFail")?r.clone():null;try{for(const t of this.iterateCallbacks("requestWillFetch"))r=await t({request:r.clone(),event:s})}catch(t){if(t instanceof Error)throw new e.WorkboxError("plugin-error-request-will-fetch",{thrownErrorMessage:t.message})}const a=r.clone();try{let t;t=await fetch(r,"navigate"===r.mode?void 0:this.ht.fetchOptions);for(const e of this.iterateCallbacks("fetchDidSucceed"))t=await e({event:s,request:a,response:t});return t}catch(t){throw n&&await this.runCallbacks("fetchDidFail",{error:t,event:s,originalRequest:n.clone(),request:a.clone()}),t}}async fetchAndCachePut(t){const e=await this.fetch(t),s=e.clone();return this.waitUntil(this.cachePut(t,s)),e}async cacheMatch(t){const e=h(t);let s;const{cacheName:r,matchOptions:n}=this.ht,a=await this.getCacheKey(e,"read"),i=Object.assign(Object.assign({},n),{cacheName:r});s=await caches.match(a,i);for(const t of this.iterateCallbacks("cachedResponseWillBeUsed"))s=await t({cacheName:r,matchOptions:n,cachedResponse:s,request:a,event:this.event})||void 0;return s}async cachePut(t,s){const n=h(t);await c.timeout(0);const o=await this.getCacheKey(n,"write");if(!s)throw new e.WorkboxError("cache-put-with-no-response",{url:i.getFriendlyURL(o.url)});const l=await this.xt(s);if(!l)return!1;const{cacheName:w,matchOptions:u}=this.ht,f=await self.caches.open(w),d=this.hasCallback("cacheDidUpdate"),p=d?await r.cacheMatchIgnoreParams(f,o.clone(),["__WB_REVISION__"],u):null;try{await f.put(o,d?l.clone():l)}catch(t){if(t instanceof Error)throw"QuotaExceededError"===t.name&&await a.executeQuotaErrorCallbacks(),t}for(const t of this.iterateCallbacks("cacheDidUpdate"))await t({cacheName:w,oldResponse:p,newResponse:l.clone(),request:o,event:this.event});return!0}async getCacheKey(t,e){const s=`${t.url} | ${e}`;if(!this.vt[s]){let r=t;for(const t of this.iterateCallbacks("cacheKeyWillBeUsed"))r=h(await t({mode:e,request:r,event:this.event,params:this.params}));this.vt[s]=r}return this.vt[s]}hasCallback(t){for(const e of this.ht.plugins)if(t in e)return!0;return!1}async runCallbacks(t,e){for(const s of this.iterateCallbacks(t))await s(e)}*iterateCallbacks(t){for(const e of this.ht.plugins)if("function"==typeof e[t]){const s=this.kt.get(e),r=r=>{const n=Object.assign(Object.assign({},r),{state:s});return e[t](n)};yield r}}waitUntil(t){return this.Et.push(t),t}async doneWaiting(){let t;for(;t=this.Et.shift();)await t}destroy(){this.bt.resolve(null)}async xt(t){let e=t,s=!1;for(const t of this.iterateCallbacks("cacheWillUpdate"))if(e=await t({request:this.request,response:e,event:this.event})||void 0,s=!0,!e)break;return s||e&&200!==e.status&&(e=void 0),e}}class w{constructor(t={}){this.cacheName=s.cacheNames.getRuntimeName(t.cacheName),this.plugins=t.plugins||[],this.fetchOptions=t.fetchOptions,this.matchOptions=t.matchOptions}handle(t){const[e]=this.handleAll(t);return e}handleAll(t){t instanceof FetchEvent&&(t={event:t,request:t.request});const e=t.event,s="string"==typeof t.request?new Request(t.request):t.request,r="params"in t?t.params:void 0,n=new l(this,{event:e,request:s,params:r}),a=this.Rt(n,s,e);return[a,this.Wt(a,n,s,e)]}async Rt(t,s,r){let n;await t.runCallbacks("handlerWillStart",{event:r,request:s});try{if(n=await this._handle(s,t),!n||"error"===n.type)throw new e.WorkboxError("no-response",{url:s.url})}catch(e){if(e instanceof Error)for(const a of t.iterateCallbacks("handlerDidError"))if(n=await a({error:e,event:r,request:s}),n)break;if(!n)throw e}for(const e of t.iterateCallbacks("handlerWillRespond"))n=await e({event:r,request:s,response:n});return n}async Wt(t,e,s,r){let n,a;try{n=await t}catch(a){}try{await e.runCallbacks("handlerDidRespond",{event:r,request:s,response:n}),await e.doneWaiting()}catch(t){t instanceof Error&&(a=t)}if(await e.runCallbacks("handlerDidComplete",{event:r,request:s,response:n,error:a}),e.destroy(),a)throw a}}const u={cacheWillUpdate:async({response:t})=>200===t.status||0===t.status?t:null};return t.CacheFirst=class extends w{async _handle(t,s){let r,n=await s.cacheMatch(t);if(!n)try{n=await s.fetchAndCachePut(t)}catch(t){t instanceof Error&&(r=t)}if(!n)throw new e.WorkboxError("no-response",{url:t.url,error:r});return n}},t.CacheOnly=class extends w{async _handle(t,s){const r=await s.cacheMatch(t);if(!r)throw new e.WorkboxError("no-response",{url:t.url});return r}},t.NetworkFirst=class extends w{constructor(t={}){super(t),this.plugins.some((t=>"cacheWillUpdate"in t))||this.plugins.unshift(u),this.Ot=t.networkTimeoutSeconds||0}async _handle(t,s){const r=[],n=[];let a;if(this.Ot){const{id:e,promise:i}=this.Ut({request:t,logs:r,handler:s});a=e,n.push(i)}const i=this.Ct({timeoutId:a,request:t,logs:r,handler:s});n.push(i);const o=await s.waitUntil((async()=>await s.waitUntil(Promise.race(n))||await i)());if(!o)throw new e.WorkboxError("no-response",{url:t.url});return o}Ut({request:t,logs:e,handler:s}){let r;return{promise:new Promise((e=>{r=setTimeout((async()=>{e(await s.cacheMatch(t))}),1e3*this.Ot)})),id:r}}async Ct({timeoutId:t,request:e,logs:s,handler:r}){let n,a;try{a=await r.fetchAndCachePut(e)}catch(t){t instanceof Error&&(n=t)}return t&&clearTimeout(t),!n&&a||(a=await r.cacheMatch(e)),a}},t.NetworkOnly=class extends w{constructor(t={}){super(t),this.Ot=t.networkTimeoutSeconds||0}async _handle(t,s){let r,n;try{const e=[s.fetch(t)];if(this.Ot){const t=c.timeout(1e3*this.Ot);e.push(t)}if(n=await Promise.race(e),!n)throw new Error(`Timed out the network response after ${this.Ot} seconds.`)}catch(t){t instanceof Error&&(r=t)}if(!n)throw new e.WorkboxError("no-response",{url:t.url,error:r});return n}},t.StaleWhileRevalidate=class extends w{constructor(t={}){super(t),this.plugins.some((t=>"cacheWillUpdate"in t))||this.plugins.unshift(u)}async _handle(t,s){const r=s.fetchAndCachePut(t).catch((()=>{}));s.waitUntil(r);let n,a=await s.cacheMatch(t);if(a);else try{a=await r}catch(t){t instanceof Error&&(n=t)}if(!a)throw new e.WorkboxError("no-response",{url:t.url,error:n});return a}},t.Strategy=w,t.StrategyHandler=l,t}({},workbox.core._private,workbox.core._private,workbox.core._private,workbox.core._private,workbox.core._private,workbox.core._private,workbox.core._private,workbox.core._private);
//# sourceMappingURL=workbox-strategies.prod.js.map
