{"ast": null, "code": "const motionComponentSymbol = Symbol.for(\"motionComponentSymbol\");\nexport { motionComponentSymbol };", "map": {"version": 3, "names": ["motionComponentSymbol", "Symbol", "for"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs"], "sourcesContent": ["const motionComponentSymbol = Symbol.for(\"motionComponentSymbol\");\n\nexport { motionComponentSymbol };\n"], "mappings": "AAAA,MAAMA,qBAAqB,GAAGC,MAAM,CAACC,GAAG,CAAC,uBAAuB,CAAC;AAEjE,SAASF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}