use anyhow::Result;
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
use cpal::{<PERSON><PERSON>, Host, SampleFormat, SampleRate, StreamConfig};
use futures_util::{SinkExt, StreamExt};
use realfft::RealFftPlanner;
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::net::{TcpListener, TcpStream};
use tokio::time::interval;
use tokio_tungstenite::{accept_async, tungstenite::Message};

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AudioData {
    #[serde(rename = "audioLevel")]
    audio_level: f64,
    #[serde(rename = "bassLevel")]
    bass_level: f64,
    #[serde(rename = "midLevel")]
    mid_level: f64,
    #[serde(rename = "trebleLevel")]
    treble_level: f64,
    #[serde(rename = "beatDetected")]
    beat_detected: bool,
    source: String,
    status: String,
}

#[derive(Debug, <PERSON>lone, Serialize)]
struct ConnectionMessage {
    r#type: String,
    status: String,
    message: String,
    source: String,
    method: String,
}

#[derive(Debug, Clone, Serialize)]
struct AudioMessage {
    r#type: String,
    #[serde(rename = "audioLevel")]
    audio_level: f64,
    #[serde(rename = "bassLevel")]
    bass_level: f64,
    #[serde(rename = "midLevel")]
    mid_level: f64,
    #[serde(rename = "trebleLevel")]
    treble_level: f64,
    #[serde(rename = "beatDetected")]
    beat_detected: bool,
    source: String,
    status: String,
}

type SharedAudioData = Arc<Mutex<AudioData>>;

struct AudioProcessor {
    fft_planner: RealFftPlanner<f32>,
    fft_buffer: Vec<f32>,
    spectrum: Vec<f32>,
    sample_rate: f32,
    last_beat_time: Instant,
}

impl AudioProcessor {
    fn new(sample_rate: f32, buffer_size: usize) -> Self {
        let mut fft_planner = RealFftPlanner::<f32>::new();
        let fft = fft_planner.plan_fft_forward(buffer_size);
        let spectrum = fft.make_output_vec();
        
        Self {
            fft_planner,
            fft_buffer: vec![0.0; buffer_size],
            spectrum,
            sample_rate,
            last_beat_time: Instant::now(),
        }
    }

    fn process_audio(&mut self, samples: &[f32]) -> AudioData {
        // Calculate RMS for overall audio level
        let rms = (samples.iter().map(|&x| x * x).sum::<f32>() / samples.len() as f32).sqrt();
        let audio_level = (rms * 100.0).min(100.0) as f64;

        // Prepare FFT buffer
        let chunk_size = self.fft_buffer.len().min(samples.len());
        self.fft_buffer[..chunk_size].copy_from_slice(&samples[..chunk_size]);
        
        // Apply window function
        apodize::hanning_mut(&mut self.fft_buffer);

        // Perform FFT
        let fft = self.fft_planner.plan_fft_forward(self.fft_buffer.len());
        fft.process(&mut self.fft_buffer, &mut self.spectrum).unwrap();

        // Calculate frequency bands
        let nyquist = self.sample_rate / 2.0;
        let bin_width = nyquist / (self.spectrum.len() as f32);

        // Define frequency ranges (Hz)
        let bass_max = 250.0;
        let mid_max = 4000.0;
        // treble is everything above mid_max

        let bass_bins = (bass_max / bin_width) as usize;
        let mid_bins = (mid_max / bin_width) as usize;

        // Calculate power in each band
        let bass_power: f32 = self.spectrum[..bass_bins.min(self.spectrum.len())]
            .iter()
            .map(|c| c.norm_sqr())
            .sum();
        
        let mid_power: f32 = self.spectrum[bass_bins..mid_bins.min(self.spectrum.len())]
            .iter()
            .map(|c| c.norm_sqr())
            .sum();
        
        let treble_power: f32 = self.spectrum[mid_bins..self.spectrum.len()]
            .iter()
            .map(|c| c.norm_sqr())
            .sum();

        // Normalize to 0-100 range
        let total_power = bass_power + mid_power + treble_power;
        let bass_level = if total_power > 0.0 {
            ((bass_power / total_power).sqrt() * audio_level).min(100.0)
        } else {
            0.0
        };
        
        let mid_level = if total_power > 0.0 {
            ((mid_power / total_power).sqrt() * audio_level).min(100.0)
        } else {
            0.0
        };
        
        let treble_level = if total_power > 0.0 {
            ((treble_power / total_power).sqrt() * audio_level).min(100.0)
        } else {
            0.0
        };

        // Simple beat detection
        let now = Instant::now();
        let beat_detected = audio_level > 15.0 && 
            bass_level > 20.0 && 
            now.duration_since(self.last_beat_time) > Duration::from_millis(100);
        
        if beat_detected {
            self.last_beat_time = now;
        }

        AudioData {
            audio_level,
            bass_level,
            mid_level,
            treble_level,
            beat_detected,
            source: "Rust CPAL".to_string(),
            status: "CAPTURING".to_string(),
        }
    }
}

fn find_nvidia_hdmi_device(host: &Host) -> Option<Device> {
    println!("🔍 Searching for NVIDIA HDMI audio devices...");
    
    for device in host.output_devices().ok()? {
        if let Ok(name) = device.name() {
            println!("📱 Found device: {}", name);
            if name.to_lowercase().contains("nvidia") && 
               (name.to_lowercase().contains("hdmi") || name.to_lowercase().contains("high definition")) {
                println!("🎯 NVIDIA HDMI device found: {}", name);
                return Some(device);
            }
        }
    }
    
    // Fallback to default output device
    println!("⚠️  NVIDIA HDMI not found, using default output device");
    host.default_output_device()
}

async fn setup_audio_capture(audio_data: SharedAudioData) -> Result<()> {
    println!("🎵 Rust NVIDIA HDMI Audio Solution");
    println!("==================================================");
    println!("🚀 Using CPAL - Cross-Platform Audio Library!");
    println!("🎯 Optimized for NVIDIA HDMI with real FFT!");
    println!();

    let host = cpal::default_host();
    println!("🎧 Audio host: {}", host.id().name());

    let device = find_nvidia_hdmi_device(&host)
        .ok_or_else(|| anyhow::anyhow!("No suitable audio device found"))?;

    let device_name = device.name()?;
    println!("✅ Using device: {}", device_name);

    // Get the default input config
    let config = device.default_output_config()?;
    println!("🔧 Sample rate: {} Hz", config.sample_rate().0);
    println!("🔧 Channels: {}", config.channels());
    println!("🔧 Sample format: {:?}", config.sample_format());

    // Update audio data source
    {
        let mut data = audio_data.lock().unwrap();
        data.source = format!("Rust CPAL: {}", device_name);
    }

    let sample_rate = config.sample_rate().0 as f32;
    let buffer_size = 1024; // FFT buffer size
    let mut processor = AudioProcessor::new(sample_rate, buffer_size);

    let stream_config = StreamConfig {
        channels: config.channels(),
        sample_rate: SampleRate(config.sample_rate().0),
        buffer_size: cpal::BufferSize::Default,
    };

    let audio_data_clone = audio_data.clone();
    let stream = match config.sample_format() {
        SampleFormat::F32 => device.build_input_stream(
            &stream_config,
            move |data: &[f32], _: &cpal::InputCallbackInfo| {
                let processed = processor.process_audio(data);
                if let Ok(mut shared_data) = audio_data_clone.lock() {
                    *shared_data = processed;
                }
            },
            |err| eprintln!("❌ Audio stream error: {}", err),
            None,
        )?,
        SampleFormat::I16 => {
            let audio_data_clone = audio_data.clone();
            device.build_input_stream(
                &stream_config,
                move |data: &[i16], _: &cpal::InputCallbackInfo| {
                    let float_data: Vec<f32> = data.iter()
                        .map(|&sample| sample as f32 / i16::MAX as f32)
                        .collect();
                    let processed = processor.process_audio(&float_data);
                    if let Ok(mut shared_data) = audio_data_clone.lock() {
                        *shared_data = processed;
                    }
                },
                |err| eprintln!("❌ Audio stream error: {}", err),
                None,
            )?
        },
        SampleFormat::U16 => {
            let audio_data_clone = audio_data.clone();
            device.build_input_stream(
                &stream_config,
                move |data: &[u16], _: &cpal::InputCallbackInfo| {
                    let float_data: Vec<f32> = data.iter()
                        .map(|&sample| (sample as f32 - u16::MAX as f32 / 2.0) / (u16::MAX as f32 / 2.0))
                        .collect();
                    let processed = processor.process_audio(&float_data);
                    if let Ok(mut shared_data) = audio_data_clone.lock() {
                        *shared_data = processed;
                    }
                },
                |err| eprintln!("❌ Audio stream error: {}", err),
                None,
            )?
        },
    };

    stream.play()?;
    println!("✅ Audio stream started!");
    println!("🎵 Now capturing system audio with real FFT analysis!");

    // Keep the stream alive
    std::mem::forget(stream);
    Ok(())
}

async fn handle_websocket(stream: TcpStream, audio_data: SharedAudioData) -> Result<()> {
    let ws_stream = accept_async(stream).await?;
    println!("🔌 New WebSocket connection established");

    let (mut ws_sender, mut ws_receiver) = ws_stream.split();

    // Send connection message
    let connection_msg = ConnectionMessage {
        r#type: "connection".to_string(),
        status: "connected".to_string(),
        message: "Connected to Rust NVIDIA HDMI Audio Backend".to_string(),
        source: {
            let data = audio_data.lock().unwrap();
            data.source.clone()
        },
        method: "Rust CPAL with Real FFT".to_string(),
    };

    let msg = Message::Text(serde_json::to_string(&connection_msg)?);
    ws_sender.send(msg).await?;

    // Send audio data at 20 FPS
    let mut interval = interval(Duration::from_millis(50));
    
    loop {
        tokio::select! {
            _ = interval.tick() => {
                let current_data = {
                    let data = audio_data.lock().unwrap();
                    data.clone()
                };

                let audio_msg = AudioMessage {
                    r#type: "audioData".to_string(),
                    audio_level: current_data.audio_level,
                    bass_level: current_data.bass_level,
                    mid_level: current_data.mid_level,
                    treble_level: current_data.treble_level,
                    beat_detected: current_data.beat_detected,
                    source: current_data.source,
                    status: current_data.status,
                };

                let msg = Message::Text(serde_json::to_string(&audio_msg)?);
                if ws_sender.send(msg).await.is_err() {
                    break;
                }
            }
            msg = ws_receiver.next() => {
                if msg.is_none() {
                    break;
                }
            }
        }
    }

    println!("🔌 WebSocket connection closed");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    let audio_data: SharedAudioData = Arc::new(Mutex::new(AudioData {
        audio_level: 0.0,
        bass_level: 0.0,
        mid_level: 0.0,
        treble_level: 0.0,
        beat_detected: false,
        source: "Initializing...".to_string(),
        status: "STARTING".to_string(),
    }));

    // Setup audio capture
    let audio_data_clone = audio_data.clone();
    tokio::spawn(async move {
        if let Err(e) = setup_audio_capture(audio_data_clone).await {
            eprintln!("❌ Audio capture setup failed: {}", e);
        }
    });

    // Start WebSocket server
    let listener = TcpListener::bind("127.0.0.1:8765").await?;
    println!("✅ Rust WebSocket server running on ws://localhost:8765");
    println!("🎯 Ready for ultra-fast audio-reactive JARVIS!");

    while let Ok((stream, _)) = listener.accept().await {
        let audio_data_clone = audio_data.clone();
        tokio::spawn(async move {
            if let Err(e) = handle_websocket(stream, audio_data_clone).await {
                eprintln!("❌ WebSocket error: {}", e);
            }
        });
    }

    Ok(())
}
