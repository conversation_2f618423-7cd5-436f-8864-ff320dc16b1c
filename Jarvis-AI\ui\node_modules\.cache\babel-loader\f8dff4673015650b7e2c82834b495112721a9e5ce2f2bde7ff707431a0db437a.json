{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ExpandableSidebar=()=>{return/*#__PURE__*/_jsxs(\"ul\",{className:\"w-64 flex flex-col gap-1 border-l border-cyan-500/30 pl-1\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-purple-300 transition-all active:scale-95 hover:text-purple-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-lg border-2 border-purple-400/50 bg-purple-900/50 p-1\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold text-cyan-100\",children:\"Notifications\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-hidden\",children:/*#__PURE__*/_jsxs(\"ul\",{className:\"divide-y divide-gray-600/50 p-4 pt-0\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"py-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",children:\"Email\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-400\",children:\"2m ago\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500\",children:\"from Wanye Enterprises\"})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"py-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",children:\"Request\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-400\",children:\"14m ago\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500\",children:\"from Acme Corporation\"})]})]})})})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-blue-300 transition-all active:scale-95 hover:text-blue-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-lg border-2 border-blue-400/50 bg-blue-900/50 p-1\",children:/*#__PURE__*/_jsxs(\"svg\",{className:\"size-6\",stroke:\"currentColor\",strokeWidth:\"1.5\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\",strokeLinejoin:\"round\",strokeLinecap:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\",strokeLinejoin:\"round\",strokeLinecap:\"round\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold text-cyan-100\",children:\"Settings\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\",children:/*#__PURE__*/_jsx(\"div\",{className:\"overflow-hidden\",children:/*#__PURE__*/_jsxs(\"ul\",{className:\"divide-y divide-gray-600/50 p-4 pt-0\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"py-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",children:\"System Preferences\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-400 transition-all peer-hover:translate-x-1\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500\",children:\"Default Settings / Profile\"})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"py-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"group/title flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\",children:\"Theme\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-400 transition-all peer-hover:translate-x-1\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500\",children:\"Light / Dark Mode\"})]})]})})})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-green-300 transition-all active:scale-95 hover:text-green-200\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-lg border-2 border-green-400/50 bg-green-900/50 p-1\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"size-6\",stroke:\"currentColor\",strokeWidth:\"1.5\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\",strokeLinejoin:\"round\",strokeLinecap:\"round\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold text-cyan-100\",children:\"Chat\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"ul\",{className:\"border-t border-gray-600/50 p-4 pt-0.5\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"flex flex-col items-end\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-right text-xs text-gray-400\",children:\"8:34 AM\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-40 rounded-lg bg-cyan-600/70 px-2 py-1 text-right text-sm text-white\",children:\"Hey JARVIS, what's your status?\"})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex flex-col items-start\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-right text-xs text-gray-400\",children:\"8:37 AM\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-40 rounded-lg bg-gray-700/80 px-2 py-1 text-sm text-cyan-100\",children:\"All systems operational.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"input\",{className:\"h-8 w-full rounded-b-lg border border-gray-600/50 bg-gray-800/80 pl-2 text-sm text-cyan-100 placeholder-gray-400 focus:border-cyan-400/50 focus:outline-none focus:ring-1 focus:ring-cyan-400/30\",placeholder:\"Reply\",type:\"text\"}),/*#__PURE__*/_jsx(\"button\",{className:\"absolute bottom-0 right-2 top-0 my-auto size-fit cursor-pointer text-cyan-400 hover:text-cyan-300\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"size-5\",stroke:\"currentColor\",strokeWidth:\"1.5\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:/*#__PURE__*/_jsx(\"path\",{d:\"m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\",strokeLinejoin:\"round\",strokeLinecap:\"round\"})})})]})]})})]})]});};export default ExpandableSidebar;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "ExpandableSidebar", "className", "children", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "strokeLinecap", "strokeLinejoin", "d", "placeholder", "type"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ui/ExpandableSidebar.js"], "sourcesContent": ["import React from 'react';\n\nconst ExpandableSidebar = () => {\n  return (\n    <ul className=\"w-64 flex flex-col gap-1 border-l border-cyan-500/30 pl-1\">\n      <li className=\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\">\n        <button className=\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-purple-300 transition-all active:scale-95 hover:text-purple-200\">\n          <div className=\"rounded-lg border-2 border-purple-400/50 bg-purple-900/50 p-1\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"size-6\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\" />\n            </svg>\n          </div>\n          <div className=\"font-semibold text-cyan-100\">Notifications</div>\n        </button>\n        <div className=\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\">\n          <div className=\"overflow-hidden\">\n            <ul className=\"divide-y divide-gray-600/50 p-4 pt-0\">\n              <li className=\"py-2\">\n                <div className=\"flex items-center justify-between\">\n                  <button className=\"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    Email\n                  </button>\n                  <div className=\"text-sm text-gray-400\">2m ago</div>\n                </div>\n                <div className=\"text-xs text-gray-500\">from Wanye Enterprises</div>\n              </li>\n              <li className=\"py-1\">\n                <div className=\"flex items-center justify-between\">\n                  <button className=\"cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    Request\n                  </button>\n                  <div className=\"text-sm text-gray-400\">14m ago</div>\n                </div>\n                <div className=\"text-xs text-gray-500\">from Acme Corporation</div>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </li>\n      <li className=\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\">\n        <button className=\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-blue-300 transition-all active:scale-95 hover:text-blue-200\">\n          <div className=\"rounded-lg border-2 border-blue-400/50 bg-blue-900/50 p-1\">\n            <svg className=\"size-6\" stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n              <path d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n            </svg>\n          </div>\n          <div className=\"font-semibold text-cyan-100\">Settings</div>\n        </button>\n        <div className=\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\">\n          <div className=\"overflow-hidden\">\n            <ul className=\"divide-y divide-gray-600/50 p-4 pt-0\">\n              <li className=\"py-2\">\n                <div className=\"flex items-center justify-between\">\n                  <button className=\"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    System Preferences\n                  </button>\n                  <div className=\"text-sm text-gray-400 transition-all peer-hover:translate-x-1\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"size-4\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m8.25 4.5 7.5 7.5-7.5 7.5\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"text-xs text-gray-500\">Default Settings / Profile</div>\n              </li>\n              <li className=\"py-1\">\n                <div className=\"group/title flex items-center justify-between\">\n                  <button className=\"peer cursor-pointer font-semibold text-cyan-200 hover:text-cyan-100\">\n                    Theme\n                  </button>\n                  <div className=\"text-sm text-gray-400 transition-all peer-hover:translate-x-1\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"size-4\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m8.25 4.5 7.5 7.5-7.5 7.5\" />\n                    </svg>\n                  </div>\n                </div>\n                <div className=\"text-xs text-gray-500\">Light / Dark Mode</div>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </li>\n      <li className=\"group w-14 overflow-hidden rounded-lg border-l border-transparent bg-gray-900/80 backdrop-blur-sm transition-all duration-500 hover:w-64 hover:border-cyan-400/50 hover:shadow-lg hover:shadow-cyan-500/20 has-[:focus]:w-64 has-[:focus]:shadow-lg has-[:focus]:shadow-cyan-500/20\">\n        <button className=\"peer flex w-full cursor-pointer items-center gap-2.5 px-3 py-2 text-left text-green-300 transition-all active:scale-95 hover:text-green-200\">\n          <div className=\"rounded-lg border-2 border-green-400/50 bg-green-900/50 p-1\">\n            <svg className=\"size-6\" stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n            </svg>\n          </div>\n          <div className=\"font-semibold text-cyan-100\">Chat</div>\n        </button>\n        <div className=\"grid grid-rows-[0fr] overflow-hidden transition-all duration-500 peer-focus:grid-rows-[1fr]\">\n          <div className=\"overflow-hidden\">\n            <ul className=\"border-t border-gray-600/50 p-4 pt-0.5\">\n              <li className=\"flex flex-col items-end\">\n                <div className=\"text-right text-xs text-gray-400\">8:34 AM</div>\n                <div className=\"w-40 rounded-lg bg-cyan-600/70 px-2 py-1 text-right text-sm text-white\">\n                  Hey JARVIS, what's your status?\n                </div>\n              </li>\n              <li className=\"flex flex-col items-start\">\n                <div className=\"text-right text-xs text-gray-400\">8:37 AM</div>\n                <div className=\"w-40 rounded-lg bg-gray-700/80 px-2 py-1 text-sm text-cyan-100\">\n                  All systems operational.\n                </div>\n              </li>\n            </ul>\n            <div className=\"relative\">\n              <input className=\"h-8 w-full rounded-b-lg border border-gray-600/50 bg-gray-800/80 pl-2 text-sm text-cyan-100 placeholder-gray-400 focus:border-cyan-400/50 focus:outline-none focus:ring-1 focus:ring-cyan-400/30\" placeholder=\"Reply\" type=\"text\" />\n              <button className=\"absolute bottom-0 right-2 top-0 my-auto size-fit cursor-pointer text-cyan-400 hover:text-cyan-300\">\n                <svg className=\"size-5\" stroke=\"currentColor\" strokeWidth=\"1.5\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" strokeLinejoin=\"round\" strokeLinecap=\"round\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </li>\n    </ul>\n  );\n}\n\nexport default ExpandableSidebar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,mBACED,KAAA,OAAIE,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACvEH,KAAA,OAAIE,SAAS,CAAC,qRAAqR,CAAAC,QAAA,eACjSH,KAAA,WAAQE,SAAS,CAAC,+IAA+I,CAAAC,QAAA,eAC/JL,IAAA,QAAKI,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAC5EL,IAAA,QAAKM,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAC,KAAK,CAACC,MAAM,CAAC,cAAc,CAACN,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAChIL,IAAA,SAAMW,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,wNAAwN,CAAE,CAAC,CAC7Q,CAAC,CACH,CAAC,cACNb,IAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,EAC1D,CAAC,cACTL,IAAA,QAAKI,SAAS,CAAC,6FAA6F,CAAAC,QAAA,cAC1GL,IAAA,QAAKI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BH,KAAA,OAAIE,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eAClDH,KAAA,OAAIE,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClBH,KAAA,QAAKE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,IAAA,WAAQI,SAAS,CAAC,gEAAgE,CAAAC,QAAA,CAAC,OAEnF,CAAQ,CAAC,cACTL,IAAA,QAAKI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,QAAM,CAAK,CAAC,EAChD,CAAC,cACNL,IAAA,QAAKI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,wBAAsB,CAAK,CAAC,EACjE,CAAC,cACLH,KAAA,OAAIE,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClBH,KAAA,QAAKE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,IAAA,WAAQI,SAAS,CAAC,gEAAgE,CAAAC,QAAA,CAAC,SAEnF,CAAQ,CAAC,cACTL,IAAA,QAAKI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAAO,CAAK,CAAC,EACjD,CAAC,cACNL,IAAA,QAAKI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uBAAqB,CAAK,CAAC,EAChE,CAAC,EACH,CAAC,CACF,CAAC,CACH,CAAC,EACJ,CAAC,cACLH,KAAA,OAAIE,SAAS,CAAC,qRAAqR,CAAAC,QAAA,eACjSH,KAAA,WAAQE,SAAS,CAAC,2IAA2I,CAAAC,QAAA,eAC3JL,IAAA,QAAKI,SAAS,CAAC,2DAA2D,CAAAC,QAAA,cACxEH,KAAA,QAAKE,SAAS,CAAC,QAAQ,CAACM,MAAM,CAAC,cAAc,CAACD,WAAW,CAAC,KAAK,CAACD,OAAO,CAAC,WAAW,CAACD,IAAI,CAAC,MAAM,CAACD,KAAK,CAAC,4BAA4B,CAAAD,QAAA,eAChIL,IAAA,SAAMa,CAAC,CAAC,y+BAAy+B,CAACD,cAAc,CAAC,OAAO,CAACD,aAAa,CAAC,OAAO,CAAE,CAAC,cACjiCX,IAAA,SAAMa,CAAC,CAAC,qCAAqC,CAACD,cAAc,CAAC,OAAO,CAACD,aAAa,CAAC,OAAO,CAAE,CAAC,EAC1F,CAAC,CACH,CAAC,cACNX,IAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,UAAQ,CAAK,CAAC,EACrD,CAAC,cACTL,IAAA,QAAKI,SAAS,CAAC,6FAA6F,CAAAC,QAAA,cAC1GL,IAAA,QAAKI,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BH,KAAA,OAAIE,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eAClDH,KAAA,OAAIE,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClBH,KAAA,QAAKE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDL,IAAA,WAAQI,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CAAC,oBAExF,CAAQ,CAAC,cACTL,IAAA,QAAKI,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAC5EL,IAAA,QAAKM,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAC,KAAK,CAACC,MAAM,CAAC,cAAc,CAACN,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAChIL,IAAA,SAAMW,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2BAA2B,CAAE,CAAC,CAChF,CAAC,CACH,CAAC,EACH,CAAC,cACNb,IAAA,QAAKI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,4BAA0B,CAAK,CAAC,EACrE,CAAC,cACLH,KAAA,OAAIE,SAAS,CAAC,MAAM,CAAAC,QAAA,eAClBH,KAAA,QAAKE,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DL,IAAA,WAAQI,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CAAC,OAExF,CAAQ,CAAC,cACTL,IAAA,QAAKI,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAC5EL,IAAA,QAAKM,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAC,KAAK,CAACC,MAAM,CAAC,cAAc,CAACN,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAChIL,IAAA,SAAMW,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2BAA2B,CAAE,CAAC,CAChF,CAAC,CACH,CAAC,EACH,CAAC,cACNb,IAAA,QAAKI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mBAAiB,CAAK,CAAC,EAC5D,CAAC,EACH,CAAC,CACF,CAAC,CACH,CAAC,EACJ,CAAC,cACLH,KAAA,OAAIE,SAAS,CAAC,qRAAqR,CAAAC,QAAA,eACjSH,KAAA,WAAQE,SAAS,CAAC,6IAA6I,CAAAC,QAAA,eAC7JL,IAAA,QAAKI,SAAS,CAAC,6DAA6D,CAAAC,QAAA,cAC1EL,IAAA,QAAKI,SAAS,CAAC,QAAQ,CAACM,MAAM,CAAC,cAAc,CAACD,WAAW,CAAC,KAAK,CAACD,OAAO,CAAC,WAAW,CAACD,IAAI,CAAC,MAAM,CAACD,KAAK,CAAC,4BAA4B,CAAAD,QAAA,cAChIL,IAAA,SAAMa,CAAC,CAAC,4eAA4e,CAACD,cAAc,CAAC,OAAO,CAACD,aAAa,CAAC,OAAO,CAAE,CAAC,CACjiB,CAAC,CACH,CAAC,cACNX,IAAA,QAAKI,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,EACjD,CAAC,cACTL,IAAA,QAAKI,SAAS,CAAC,6FAA6F,CAAAC,QAAA,cAC1GH,KAAA,QAAKE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BH,KAAA,OAAIE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACpDH,KAAA,OAAIE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCL,IAAA,QAAKI,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,SAAO,CAAK,CAAC,cAC/DL,IAAA,QAAKI,SAAS,CAAC,wEAAwE,CAAAC,QAAA,CAAC,iCAExF,CAAK,CAAC,EACJ,CAAC,cACLH,KAAA,OAAIE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACvCL,IAAA,QAAKI,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,SAAO,CAAK,CAAC,cAC/DL,IAAA,QAAKI,SAAS,CAAC,gEAAgE,CAAAC,QAAA,CAAC,0BAEhF,CAAK,CAAC,EACJ,CAAC,EACH,CAAC,cACLH,KAAA,QAAKE,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBL,IAAA,UAAOI,SAAS,CAAC,kMAAkM,CAACU,WAAW,CAAC,OAAO,CAACC,IAAI,CAAC,MAAM,CAAE,CAAC,cACtPf,IAAA,WAAQI,SAAS,CAAC,mGAAmG,CAAAC,QAAA,cACnHL,IAAA,QAAKI,SAAS,CAAC,QAAQ,CAACM,MAAM,CAAC,cAAc,CAACD,WAAW,CAAC,KAAK,CAACD,OAAO,CAAC,WAAW,CAACD,IAAI,CAAC,MAAM,CAACD,KAAK,CAAC,4BAA4B,CAAAD,QAAA,cAChIL,IAAA,SAAMa,CAAC,CAAC,oEAAoE,CAACD,cAAc,CAAC,OAAO,CAACD,aAAa,CAAC,OAAO,CAAE,CAAC,CACzH,CAAC,CACA,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,EACJ,CAAC,EACH,CAAC,CAET,CAAC,CAED,cAAe,CAAAR,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}