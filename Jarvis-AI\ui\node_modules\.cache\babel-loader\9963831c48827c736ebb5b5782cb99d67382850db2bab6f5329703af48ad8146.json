{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\components\\\\layout\\\\Footer.js\";\nimport React from 'react';\nimport AnimatedButton from '../ui/AnimatedButton';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = ({\n  onHomeClick,\n  onSettingsClick,\n  onHelpClick,\n  showNavigation = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"fixed bottom-0 left-0 right-0 p-4 bg-black/20 backdrop-blur-sm\",\n    children: [showNavigation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatedButton, {\n        variant: \"secondary\",\n        size: \"small\",\n        onClick: onHomeClick,\n        children: \"\\uD83C\\uDFE0 Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AnimatedButton, {\n        variant: \"secondary\",\n        size: \"small\",\n        onClick: onSettingsClick,\n        children: \"\\u2699\\uFE0F Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AnimatedButton, {\n        variant: \"secondary\",\n        size: \"small\",\n        onClick: onHelpClick,\n        children: \"\\u2753 Help\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mt-2\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-400\",\n        children: \"\\xA9 2024 JARVIS UI - Powered by AI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "AnimatedButton", "jsxDEV", "_jsxDEV", "Footer", "onHomeClick", "onSettingsClick", "onHelpClick", "showNavigation", "className", "children", "variant", "size", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/layout/Footer.js"], "sourcesContent": ["import React from 'react';\nimport AnimatedButton from '../ui/AnimatedButton';\n\nconst Footer = ({ \n  onHomeClick, \n  onSettingsClick, \n  onHelpClick,\n  showNavigation = true \n}) => {\n  return (\n    <footer className=\"fixed bottom-0 left-0 right-0 p-4 bg-black/20 backdrop-blur-sm\">\n      {showNavigation && (\n        <div className=\"flex justify-center space-x-4\">\n          <AnimatedButton\n            variant=\"secondary\"\n            size=\"small\"\n            onClick={onHomeClick}\n          >\n            🏠 Home\n          </AnimatedButton>\n          \n          <AnimatedButton\n            variant=\"secondary\"\n            size=\"small\"\n            onClick={onSettingsClick}\n          >\n            ⚙️ Settings\n          </AnimatedButton>\n          \n          <AnimatedButton\n            variant=\"secondary\"\n            size=\"small\"\n            onClick={onHelpClick}\n          >\n            ❓ Help\n          </AnimatedButton>\n        </div>\n      )}\n      \n      <div className=\"text-center mt-2\">\n        <p className=\"text-xs text-gray-400\">\n          © 2024 JARVIS UI - Powered by AI\n        </p>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,MAAM,GAAGA,CAAC;EACdC,WAAW;EACXC,eAAe;EACfC,WAAW;EACXC,cAAc,GAAG;AACnB,CAAC,KAAK;EACJ,oBACEL,OAAA;IAAQM,SAAS,EAAC,gEAAgE;IAAAC,QAAA,GAC/EF,cAAc,iBACbL,OAAA;MAAKM,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CP,OAAA,CAACF,cAAc;QACbU,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,OAAO;QACZC,OAAO,EAAER,WAAY;QAAAK,QAAA,EACtB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC,eAEjBd,OAAA,CAACF,cAAc;QACbU,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEP,eAAgB;QAAAI,QAAA,EAC1B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC,eAEjBd,OAAA,CAACF,cAAc;QACbU,OAAO,EAAC,WAAW;QACnBC,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEN,WAAY;QAAAG,QAAA,EACtB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAEDd,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BP,OAAA;QAAGM,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAErC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACC,EAAA,GA3CId,MAAM;AA6CZ,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}