{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"when\", \"delay\", \"delayChildren\", \"staggerChildren\", \"staggerDirection\", \"repeat\", \"repeatType\", \"repeatDelay\", \"from\", \"elapsed\"];\n/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined(_ref) {\n  let {\n      when,\n      delay: _delay,\n      delayChildren,\n      staggerChildren,\n      staggerDirection,\n      repeat,\n      repeatType,\n      repeatDelay,\n      from,\n      elapsed\n    } = _ref,\n    transition = _objectWithoutProperties(_ref, _excluded);\n  return !!Object.keys(transition).length;\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] || transition[\"default\"] || transition;\n}\nexport { getValueTransition, isTransitionDefined };", "map": {"version": 3, "names": ["isTransitionDefined", "_ref", "when", "delay", "_delay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "repeat", "repeatType", "repeatDelay", "from", "elapsed", "transition", "_objectWithoutProperties", "_excluded", "Object", "keys", "length", "getValueTransition", "key"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/animation/utils/transitions.mjs"], "sourcesContent": ["/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, elapsed, ...transition }) {\n    return !!Object.keys(transition).length;\n}\nfunction getValueTransition(transition, key) {\n    return transition[key] || transition[\"default\"] || transition;\n}\n\nexport { getValueTransition, isTransitionDefined };\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmBA,CAAAC,IAAA,EAA2I;EAAA,IAA1I;MAAEC,IAAI;MAAEC,KAAK,EAAEC,MAAM;MAAEC,aAAa;MAAEC,eAAe;MAAEC,gBAAgB;MAAEC,MAAM;MAAEC,UAAU;MAAEC,WAAW;MAAEC,IAAI;MAAEC;IAAuB,CAAC,GAAAX,IAAA;IAAZY,UAAU,GAAAC,wBAAA,CAAAb,IAAA,EAAAc,SAAA;EAC/J,OAAO,CAAC,CAACC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,MAAM;AAC3C;AACA,SAASC,kBAAkBA,CAACN,UAAU,EAAEO,GAAG,EAAE;EACzC,OAAOP,UAAU,CAACO,GAAG,CAAC,IAAIP,UAAU,CAAC,SAAS,CAAC,IAAIA,UAAU;AACjE;AAEA,SAASM,kBAAkB,EAAEnB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}