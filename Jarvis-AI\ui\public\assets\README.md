# Assets Directory

This directory should contain the following files for the Jarvis UI:

## Required Files:
- `startup.gif` - Animation shown during system initialization
- `listening.gif` - Animation shown when <PERSON> is listening for voice input
- `thinking.gif` - Animation shown when <PERSON> is processing information
- `speaking.mp4` - Video/animation shown when <PERSON> is responding
- `rest.gif` - Animation shown when <PERSON> is in standby mode

## HomeScreen Assets:
- `HomeSettingsscreen.gif` - Animated wallpaper background for the homescreen (should loop seamlessly)
- `screenexit.gif` - Exit animation that plays when transitioning from homescreen to Jarvis interface

## File Specifications:
- All files should have a futuristic, holographic appearance
- Recommended size: 320x320 pixels for optimal display (except HomeSettingsscreen.gif which should be fullscreen)
- Use blue (#00d4ff) and cyan color schemes to match the UI
- GIF files should loop seamlessly
- MP4 should be short (2-4 seconds) and loop
- HomeSettingsscreen.gif should be optimized for additive dissolve blending
- screenexit.gif should be approximately 2 seconds long for smooth transitions

## Creating Assets:
You can create these assets using:
- After Effects for animations
- Blender for 3D holographic effects
- Online GIF generators
- AI image generators (DALL-E, Midjourney, etc.)

## Current Status:
- ✅ startup.gif (present)
- ✅ listening.gif (present)
- ✅ thinking.gif (present)
- ✅ speaking.gif (present - note: should be .mp4 format)
- ❌ rest.gif (missing - you have rest.mp4 instead)
- ❌ HomeSettingsscreen.gif (missing - this is your animated wallpaper!)
- ❌ screenexit.gif (missing)

## Placeholder Behavior:
Until these files are added, the UI will show CSS-based animations as fallbacks.

## MISSING: HomeSettingsscreen.gif
This is the animated wallpaper background you're asking about!
Place your looped animated background GIF here as `HomeSettingsscreen.gif`
