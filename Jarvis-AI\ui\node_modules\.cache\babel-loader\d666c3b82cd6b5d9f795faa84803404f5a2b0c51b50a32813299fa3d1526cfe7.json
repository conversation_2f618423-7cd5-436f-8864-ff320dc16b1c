{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * Jarvis API Service\n * Handles communication between React frontend and Python backend\n */const API_BASE_URL='http://localhost:5000/api';class JarvisApiService{constructor(){this.baseURL=API_BASE_URL;}/**\n   * Make HTTP request to API\n   */async makeRequest(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const url=\"\".concat(this.baseURL).concat(endpoint);const defaultOptions={headers:{'Content-Type':'application/json'}};const requestOptions=_objectSpread(_objectSpread({},defaultOptions),options);try{const response=await fetch(url,requestOptions);if(!response.ok){const errorData=await response.json().catch(()=>({}));throw new Error(errorData.error||\"HTTP \".concat(response.status,\": \").concat(response.statusText));}return await response.json();}catch(error){console.error(\"API request failed for \".concat(endpoint,\":\"),error);throw error;}}/**\n   * Health check\n   */async healthCheck(){return this.makeRequest('/health');}/**\n   * Get current Jarvis state\n   */async getState(){return this.makeRequest('/state');}/**\n   * Set Jarvis state\n   */async setState(state){return this.makeRequest('/state',{method:'POST',body:JSON.stringify({state})});}/**\n   * Send chat message and get response\n   */async sendMessage(message){return this.makeRequest('/chat',{method:'POST',body:JSON.stringify({message})});}/**\n   * Start speech recognition\n   */async startSpeechRecognition(){return this.makeRequest('/speech-to-text',{method:'POST'});}/**\n   * Convert text to speech\n   */async textToSpeech(text){return this.makeRequest('/text-to-speech',{method:'POST',body:JSON.stringify({text})});}/**\n   * Get conversation history\n   */async getConversationHistory(){return this.makeRequest('/conversation-history');}/**\n   * Clear conversation history\n   */async clearConversationHistory(){return this.makeRequest('/conversation-history',{method:'DELETE'});}/**\n   * Check if API server is running\n   */async isServerRunning(){try{await this.healthCheck();return true;}catch(error){return false;}}/**\n   * Poll state changes (for real-time updates)\n   */async pollState(callback){let interval=arguments.length>1&&arguments[1]!==undefined?arguments[1]:1000;const poll=async()=>{try{const state=await this.getState();callback(state);}catch(error){console.error('State polling error:',error);}};// Initial poll\nawait poll();// Set up interval polling\nreturn setInterval(poll,interval);}/**\n   * Stop polling\n   */stopPolling(intervalId){if(intervalId){clearInterval(intervalId);}}}// Create singleton instance\nconst jarvisApi=new JarvisApiService();export default jarvisApi;// Export individual methods for convenience\nexport const{healthCheck,getState,setState,sendMessage,startSpeechRecognition,textToSpeech,getConversationHistory,clearConversationHistory,isServerRunning,pollState,stopPolling}=jarvisApi;", "map": {"version": 3, "names": ["API_BASE_URL", "JarvisApiService", "constructor", "baseURL", "makeRequest", "endpoint", "options", "arguments", "length", "undefined", "url", "concat", "defaultOptions", "headers", "requestOptions", "_objectSpread", "response", "fetch", "ok", "errorData", "json", "catch", "Error", "error", "status", "statusText", "console", "healthCheck", "getState", "setState", "state", "method", "body", "JSON", "stringify", "sendMessage", "message", "startSpeechRecognition", "textToSpeech", "text", "getConversationHistory", "clearConversationHistory", "isServerRunning", "pollState", "callback", "interval", "poll", "setInterval", "stopPolling", "intervalId", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/services/jarvisApi.js"], "sourcesContent": ["/**\n * Jarvis API Service\n * Handles communication between React frontend and Python backend\n */\n\nconst API_BASE_URL = 'http://localhost:5000/api';\n\nclass JarvisApiService {\n  constructor() {\n    this.baseURL = API_BASE_URL;\n  }\n\n  /**\n   * Make HTTP request to API\n   */\n  async makeRequest(endpoint, options = {}) {\n    const url = `${this.baseURL}${endpoint}`;\n    const defaultOptions = {\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    };\n\n    const requestOptions = { ...defaultOptions, ...options };\n\n    try {\n      const response = await fetch(url, requestOptions);\n      \n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Health check\n   */\n  async healthCheck() {\n    return this.makeRequest('/health');\n  }\n\n  /**\n   * Get current Jarvis state\n   */\n  async getState() {\n    return this.makeRequest('/state');\n  }\n\n  /**\n   * Set Jarvis state\n   */\n  async setState(state) {\n    return this.makeRequest('/state', {\n      method: 'POST',\n      body: JSON.stringify({ state }),\n    });\n  }\n\n  /**\n   * Send chat message and get response\n   */\n  async sendMessage(message) {\n    return this.makeRequest('/chat', {\n      method: 'POST',\n      body: JSON.stringify({ message }),\n    });\n  }\n\n  /**\n   * Start speech recognition\n   */\n  async startSpeechRecognition() {\n    return this.makeRequest('/speech-to-text', {\n      method: 'POST',\n    });\n  }\n\n  /**\n   * Convert text to speech\n   */\n  async textToSpeech(text) {\n    return this.makeRequest('/text-to-speech', {\n      method: 'POST',\n      body: JSON.stringify({ text }),\n    });\n  }\n\n  /**\n   * Get conversation history\n   */\n  async getConversationHistory() {\n    return this.makeRequest('/conversation-history');\n  }\n\n  /**\n   * Clear conversation history\n   */\n  async clearConversationHistory() {\n    return this.makeRequest('/conversation-history', {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Check if API server is running\n   */\n  async isServerRunning() {\n    try {\n      await this.healthCheck();\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Poll state changes (for real-time updates)\n   */\n  async pollState(callback, interval = 1000) {\n    const poll = async () => {\n      try {\n        const state = await this.getState();\n        callback(state);\n      } catch (error) {\n        console.error('State polling error:', error);\n      }\n    };\n\n    // Initial poll\n    await poll();\n\n    // Set up interval polling\n    return setInterval(poll, interval);\n  }\n\n  /**\n   * Stop polling\n   */\n  stopPolling(intervalId) {\n    if (intervalId) {\n      clearInterval(intervalId);\n    }\n  }\n}\n\n// Create singleton instance\nconst jarvisApi = new JarvisApiService();\n\nexport default jarvisApi;\n\n// Export individual methods for convenience\nexport const {\n  healthCheck,\n  getState,\n  setState,\n  sendMessage,\n  startSpeechRecognition,\n  textToSpeech,\n  getConversationHistory,\n  clearConversationHistory,\n  isServerRunning,\n  pollState,\n  stopPolling,\n} = jarvisApi;\n"], "mappings": "2IAAA;AACA;AACA;AACA,GAEA,KAAM,CAAAA,YAAY,CAAG,2BAA2B,CAEhD,KAAM,CAAAC,gBAAiB,CACrBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAGH,YAAY,CAC7B,CAEA;AACF;AACA,KACE,KAAM,CAAAI,WAAWA,CAACC,QAAQ,CAAgB,IAAd,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACtC,KAAM,CAAAG,GAAG,IAAAC,MAAA,CAAM,IAAI,CAACR,OAAO,EAAAQ,MAAA,CAAGN,QAAQ,CAAE,CACxC,KAAM,CAAAO,cAAc,CAAG,CACrBC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAAC,aAAA,CAAAA,aAAA,IAAQH,cAAc,EAAKN,OAAO,CAAE,CAExD,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACP,GAAG,CAAEI,cAAc,CAAC,CAEjD,GAAI,CAACE,QAAQ,CAACE,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAO,CAAC,CAAC,CAAC,CAAC,CACzD,KAAM,IAAI,CAAAC,KAAK,CAACH,SAAS,CAACI,KAAK,UAAAZ,MAAA,CAAYK,QAAQ,CAACQ,MAAM,OAAAb,MAAA,CAAKK,QAAQ,CAACS,UAAU,CAAE,CAAC,CACvF,CAEA,MAAO,MAAM,CAAAT,QAAQ,CAACI,IAAI,CAAC,CAAC,CAC9B,CAAE,MAAOG,KAAK,CAAE,CACdG,OAAO,CAACH,KAAK,2BAAAZ,MAAA,CAA2BN,QAAQ,MAAKkB,KAAK,CAAC,CAC3D,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAI,WAAWA,CAAA,CAAG,CAClB,MAAO,KAAI,CAACvB,WAAW,CAAC,SAAS,CAAC,CACpC,CAEA;AACF;AACA,KACE,KAAM,CAAAwB,QAAQA,CAAA,CAAG,CACf,MAAO,KAAI,CAACxB,WAAW,CAAC,QAAQ,CAAC,CACnC,CAEA;AACF;AACA,KACE,KAAM,CAAAyB,QAAQA,CAACC,KAAK,CAAE,CACpB,MAAO,KAAI,CAAC1B,WAAW,CAAC,QAAQ,CAAE,CAChC2B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEJ,KAAM,CAAC,CAChC,CAAC,CAAC,CACJ,CAEA;AACF;AACA,KACE,KAAM,CAAAK,WAAWA,CAACC,OAAO,CAAE,CACzB,MAAO,KAAI,CAAChC,WAAW,CAAC,OAAO,CAAE,CAC/B2B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEE,OAAQ,CAAC,CAClC,CAAC,CAAC,CACJ,CAEA;AACF;AACA,KACE,KAAM,CAAAC,sBAAsBA,CAAA,CAAG,CAC7B,MAAO,KAAI,CAACjC,WAAW,CAAC,iBAAiB,CAAE,CACzC2B,MAAM,CAAE,MACV,CAAC,CAAC,CACJ,CAEA;AACF;AACA,KACE,KAAM,CAAAO,YAAYA,CAACC,IAAI,CAAE,CACvB,MAAO,KAAI,CAACnC,WAAW,CAAC,iBAAiB,CAAE,CACzC2B,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEK,IAAK,CAAC,CAC/B,CAAC,CAAC,CACJ,CAEA;AACF;AACA,KACE,KAAM,CAAAC,sBAAsBA,CAAA,CAAG,CAC7B,MAAO,KAAI,CAACpC,WAAW,CAAC,uBAAuB,CAAC,CAClD,CAEA;AACF;AACA,KACE,KAAM,CAAAqC,wBAAwBA,CAAA,CAAG,CAC/B,MAAO,KAAI,CAACrC,WAAW,CAAC,uBAAuB,CAAE,CAC/C2B,MAAM,CAAE,QACV,CAAC,CAAC,CACJ,CAEA;AACF;AACA,KACE,KAAM,CAAAW,eAAeA,CAAA,CAAG,CACtB,GAAI,CACF,KAAM,KAAI,CAACf,WAAW,CAAC,CAAC,CACxB,MAAO,KAAI,CACb,CAAE,MAAOJ,KAAK,CAAE,CACd,MAAO,MAAK,CACd,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAAoB,SAASA,CAACC,QAAQ,CAAmB,IAAjB,CAAAC,QAAQ,CAAAtC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CACvC,KAAM,CAAAuC,IAAI,CAAG,KAAAA,CAAA,GAAY,CACvB,GAAI,CACF,KAAM,CAAAhB,KAAK,CAAG,KAAM,KAAI,CAACF,QAAQ,CAAC,CAAC,CACnCgB,QAAQ,CAACd,KAAK,CAAC,CACjB,CAAE,MAAOP,KAAK,CAAE,CACdG,OAAO,CAACH,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CACF,CAAC,CAED;AACA,KAAM,CAAAuB,IAAI,CAAC,CAAC,CAEZ;AACA,MAAO,CAAAC,WAAW,CAACD,IAAI,CAAED,QAAQ,CAAC,CACpC,CAEA;AACF;AACA,KACEG,WAAWA,CAACC,UAAU,CAAE,CACtB,GAAIA,UAAU,CAAE,CACdC,aAAa,CAACD,UAAU,CAAC,CAC3B,CACF,CACF,CAEA;AACA,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAAlD,gBAAgB,CAAC,CAAC,CAExC,cAAe,CAAAkD,SAAS,CAExB;AACA,MAAO,MAAM,CACXxB,WAAW,CACXC,QAAQ,CACRC,QAAQ,CACRM,WAAW,CACXE,sBAAsB,CACtBC,YAAY,CACZE,sBAAsB,CACtBC,wBAAwB,CACxBC,eAAe,CACfC,SAAS,CACTK,WACF,CAAC,CAAGG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}