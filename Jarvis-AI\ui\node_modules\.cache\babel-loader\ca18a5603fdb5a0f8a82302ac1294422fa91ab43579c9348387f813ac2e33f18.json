{"ast": null, "code": "import React from'react';import AnimatedButton from'../ui/AnimatedButton';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Footer=_ref=>{let{onHomeClick,onSettingsClick,onHelpClick,showNavigation=true}=_ref;return/*#__PURE__*/_jsxs(\"footer\",{className:\"fixed bottom-0 left-0 right-0 p-4 bg-black/20 backdrop-blur-sm\",children:[showNavigation&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center space-x-4\",children:[/*#__PURE__*/_jsx(AnimatedButton,{variant:\"secondary\",size:\"small\",onClick:onHomeClick,children:\"\\uD83C\\uDFE0 Home\"}),/*#__PURE__*/_jsx(AnimatedButton,{variant:\"secondary\",size:\"small\",onClick:onSettingsClick,children:\"\\u2699\\uFE0F Settings\"}),/*#__PURE__*/_jsx(AnimatedButton,{variant:\"secondary\",size:\"small\",onClick:onHelpClick,children:\"\\u2753 Help\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center mt-2\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-400\",children:\"\\xA9 2024 JARVIS UI - Powered by AI\"})})]});};export default Footer;", "map": {"version": 3, "names": ["React", "AnimatedButton", "jsx", "_jsx", "jsxs", "_jsxs", "Footer", "_ref", "onHomeClick", "onSettingsClick", "onHelpClick", "showNavigation", "className", "children", "variant", "size", "onClick"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/layout/Footer.js"], "sourcesContent": ["import React from 'react';\nimport AnimatedButton from '../ui/AnimatedButton';\n\nconst Footer = ({ \n  onHomeClick, \n  onSettingsClick, \n  onHelpClick,\n  showNavigation = true \n}) => {\n  return (\n    <footer className=\"fixed bottom-0 left-0 right-0 p-4 bg-black/20 backdrop-blur-sm\">\n      {showNavigation && (\n        <div className=\"flex justify-center space-x-4\">\n          <AnimatedButton\n            variant=\"secondary\"\n            size=\"small\"\n            onClick={onHomeClick}\n          >\n            🏠 Home\n          </AnimatedButton>\n          \n          <AnimatedButton\n            variant=\"secondary\"\n            size=\"small\"\n            onClick={onSettingsClick}\n          >\n            ⚙️ Settings\n          </AnimatedButton>\n          \n          <AnimatedButton\n            variant=\"secondary\"\n            size=\"small\"\n            onClick={onHelpClick}\n          >\n            ❓ Help\n          </AnimatedButton>\n        </div>\n      )}\n      \n      <div className=\"text-center mt-2\">\n        <p className=\"text-xs text-gray-400\">\n          © 2024 JARVIS UI - Powered by AI\n        </p>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,cAAc,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAKT,IALU,CACdC,WAAW,CACXC,eAAe,CACfC,WAAW,CACXC,cAAc,CAAG,IACnB,CAAC,CAAAJ,IAAA,CACC,mBACEF,KAAA,WAAQO,SAAS,CAAC,gEAAgE,CAAAC,QAAA,EAC/EF,cAAc,eACbN,KAAA,QAAKO,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CV,IAAA,CAACF,cAAc,EACba,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAER,WAAY,CAAAK,QAAA,CACtB,mBAED,CAAgB,CAAC,cAEjBV,IAAA,CAACF,cAAc,EACba,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEP,eAAgB,CAAAI,QAAA,CAC1B,uBAED,CAAgB,CAAC,cAEjBV,IAAA,CAACF,cAAc,EACba,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEN,WAAY,CAAAG,QAAA,CACtB,aAED,CAAgB,CAAC,EACd,CACN,cAEDV,IAAA,QAAKS,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BV,IAAA,MAAGS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qCAErC,CAAG,CAAC,CACD,CAAC,EACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}