{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{motion,AnimatePresence}from'framer-motion';import ExpandableSidebar from'./ui/ExpandableSidebar';import MinimizedJarvis from'./ui/MinimizedJarvis';import{useJarvis}from'../hooks/useJarvis';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ChatScreen=_ref=>{let{onExitToHome,initialMessage=''}=_ref;const[messages,setMessages]=useState([]);const[inputText,setInputText]=useState('');const[isInputFocused,setIsInputFocused]=useState(false);const[isTyping,setIsTyping]=useState(false);const[showDropdown,setShowDropdown]=useState(false);const[showScrollButton,setShowScrollButton]=useState(false);const messagesEndRef=useRef(null);const inputRef=useRef(null);const messagesContainerRef=useRef(null);// Jarvis API integration\nconst{state,isProcessing,isServerConnected,error,sendMessage,speakText,conversationHistory,clearHistory}=useJarvis();// Add initial message if provided and send to backend\nuseEffect(()=>{if(initialMessage.trim()){const userMessage={id:Date.now(),text:initialMessage,sender:'user',timestamp:new Date()};setMessages([userMessage]);// Send to backend and get real response\nconst processInitialMessage=async()=>{try{setIsTyping(true);const response=await sendMessage(initialMessage);setMessages(prev=>[...prev,{id:Date.now()+1,text:response.response,sender:'jarvis',timestamp:new Date(response.timestamp)}]);}catch(error){console.error('Failed to process initial message:',error);setMessages(prev=>[...prev,{id:Date.now()+1,text:\"I'm sorry, I'm having trouble connecting to my backend systems. Please try again.\",sender:'jarvis',timestamp:new Date()}]);}finally{setIsTyping(false);}};processInitialMessage();}},[initialMessage,sendMessage]);// Auto-scroll to bottom when new messages arrive\nuseEffect(()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});},[messages,isTyping]);// Focus input on mount\nuseEffect(()=>{var _inputRef$current;(_inputRef$current=inputRef.current)===null||_inputRef$current===void 0?void 0:_inputRef$current.focus();},[]);const handleSendMessage=async()=>{if(!inputText.trim()||!isServerConnected)return;const messageText=inputText.trim();const newMessage={id:Date.now(),text:messageText,sender:'user',timestamp:new Date()};setMessages(prev=>[...prev,newMessage]);setInputText('');// Send to backend\ntry{setIsTyping(true);const response=await sendMessage(messageText);setMessages(prev=>[...prev,{id:Date.now()+1,text:response.response,sender:'jarvis',timestamp:new Date(response.timestamp)}]);}catch(error){console.error('Failed to send message:',error);setMessages(prev=>[...prev,{id:Date.now()+1,text:\"I'm sorry, I encountered an error processing your request. Please try again.\",sender:'jarvis',timestamp:new Date()}]);}finally{setIsTyping(false);}};const handleKeyPress=e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendMessage();}};// Scroll functionality\nconst handleScroll=e=>{const{scrollTop,scrollHeight,clientHeight}=e.target;const isNearBottom=scrollHeight-scrollTop-clientHeight<100;setShowScrollButton(!isNearBottom&&messages.length>0);};const scrollToBottom=()=>{var _messagesEndRef$curre2;(_messagesEndRef$curre2=messagesEndRef.current)===null||_messagesEndRef$curre2===void 0?void 0:_messagesEndRef$curre2.scrollIntoView({behavior:'smooth'});};// Animation variants\nconst messageVariants={hidden:{opacity:0,y:10,scale:0.95},visible:{opacity:1,y:0,scale:1,transition:{duration:0.3}}};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col h-screen bg-gradient-to-br from-gray-900 to-black text-white relative\",children:[!isServerConnected&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-4 left-1/2 transform -translate-x-1/2 z-20\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-red-900/80 border border-red-500/50 rounded-lg px-4 py-2\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-red-300\",children:\"Backend Disconnected - Chat functionality limited\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\",children:/*#__PURE__*/_jsx(ExpandableSidebar,{})}),/*#__PURE__*/_jsx(MinimizedJarvis,{onExpand:onExitToHome,isProcessing:isProcessing}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between px-4 py-3 border-b border-cyan-500/20\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-2\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowDropdown(!showDropdown),className:\"flex items-center space-x-2 text-white hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"JARVIS\"}),/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 9l-7 7-7-7\"})})]}),showDropdown&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-full left-0 mt-1 w-48 bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-filter backdrop-blur-12 rounded-lg shadow-lg border border-cyan-500/30 z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"py-1\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\",children:\"New Chat\"}),/*#__PURE__*/_jsx(\"button\",{className:\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\",children:\"Chat History\"}),/*#__PURE__*/_jsx(\"button\",{className:\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\",children:\"Settings\"})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"flex items-center space-x-2 bg-gradient-to-r from-cyan-600/20 to-blue-600/20 hover:from-cyan-500/30 hover:to-blue-500/30 border border-cyan-500/30 px-3 py-2 rounded-lg transition-all duration-300 text-cyan-100 hover:text-cyan-400\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"Share\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:onExitToHome,className:\"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\",title:\"Return to JARVIS\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M10 19l-7-7m0 0l7-7m-7 7h18\"})})}),/*#__PURE__*/_jsx(\"button\",{className:\"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 overflow-y-auto relative\",onScroll:handleScroll,ref:messagesContainerRef,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-3xl mx-auto\",children:[messages.length===0?/*#__PURE__*//* Welcome Message */_jsx(\"div\",{className:\"flex flex-col items-center justify-center h-full text-center py-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mb-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-cyan-400\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-gray-300 mb-2\",children:\"How can I help you today?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500\",children:\"I'm JARVIS, your AI assistant. Ask me anything!\"})]})}):/*#__PURE__*/_jsxs(\"div\",{className:\"py-8\",children:[/*#__PURE__*/_jsx(AnimatePresence,{children:messages.map(message=>/*#__PURE__*/_jsx(motion.div,{variants:messageVariants,initial:\"hidden\",animate:\"visible\",className:\"group mb-8 \".concat(message.sender==='user'?'ml-auto':''),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4 px-4\",children:[message.sender==='jarvis'&&/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 rounded-full bg-cyan-400\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 \".concat(message.sender==='user'?'max-w-2xl ml-auto':'max-w-2xl'),children:[message.sender==='user'&&/*#__PURE__*/_jsx(\"div\",{className:\"text-right mb-2\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-400\",children:\"You\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(message.sender==='user'?'bg-gray-700 text-white rounded-2xl px-4 py-3 ml-auto inline-block max-w-fit':'text-gray-100'),children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm leading-relaxed whitespace-pre-wrap\",children:message.text})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 \".concat(message.sender==='user'?'text-right':'text-left'),children:/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:message.timestamp.toLocaleTimeString()})})]}),message.sender==='user'&&/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium\",children:\"U\"})})})]})},message.id))}),isTyping&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:\"mb-8 px-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 rounded-full bg-cyan-400\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 max-w-2xl\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-1 py-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",style:{animationDelay:'0.1s'}}),/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",style:{animationDelay:'0.2s'}})]})})]})})]}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]}),showScrollButton&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-4 right-4\",children:/*#__PURE__*/_jsx(\"button\",{onClick:scrollToBottom,className:\"bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow-lg transition-colors\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 14l-7 7m0 0l-7-7m7 7V3\"})})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"form-control \".concat(isInputFocused?'focused':''),children:[/*#__PURE__*/_jsx(\"input\",{ref:inputRef,type:\"text\",className:\"input\",placeholder:\"Type something intelligent (Press Enter to chat)\",value:inputText,onChange:e=>setInputText(e.target.value),onFocus:()=>setIsInputFocused(true),onBlur:()=>setIsInputFocused(false),onKeyDown:handleKeyPress}),/*#__PURE__*/_jsx(\"div\",{className:\"input-border\"})]})})]});};export default ChatScreen;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "ExpandableSidebar", "Mini<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "ChatScreen", "_ref", "onExitToHome", "initialMessage", "messages", "setMessages", "inputText", "setInputText", "isInputFocused", "setIsInputFocused", "isTyping", "setIsTyping", "showDropdown", "setShowDropdown", "showScrollButton", "setShowScrollButton", "messagesEndRef", "inputRef", "messagesContainerRef", "state", "isProcessing", "isServerConnected", "error", "sendMessage", "speakText", "conversationHistory", "clearHistory", "trim", "userMessage", "id", "Date", "now", "text", "sender", "timestamp", "processInitialMessage", "response", "prev", "console", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "_inputRef$current", "focus", "handleSendMessage", "messageText", "newMessage", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "handleScroll", "scrollTop", "scrollHeight", "clientHeight", "target", "isNearBottom", "length", "scrollToBottom", "_messagesEndRef$curre2", "messageVariants", "hidden", "opacity", "y", "scale", "visible", "transition", "duration", "className", "children", "onExpand", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "title", "onScroll", "ref", "map", "message", "div", "variants", "initial", "animate", "concat", "toLocaleTimeString", "style", "animationDelay", "type", "placeholder", "value", "onChange", "onFocus", "onBlur", "onKeyDown"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ChatScreen.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ExpandableSidebar from './ui/ExpandableSidebar';\nimport MinimizedJarvis from './ui/MinimizedJarvis';\nimport { useJarvis } from '../hooks/useJarvis';\n\nconst ChatScreen = ({ onExitToHome, initialMessage = '' }) => {\n  const [messages, setMessages] = useState([]);\n  const [inputText, setInputText] = useState('');\n  const [isInputFocused, setIsInputFocused] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showDropdown, setShowDropdown] = useState(false);\n  const [showScrollButton, setShowScrollButton] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Jarvis API integration\n  const {\n    state,\n    isProcessing,\n    isServerConnected,\n    error,\n    sendMessage,\n    speakText,\n    conversationHistory,\n    clearHistory\n  } = useJarvis();\n\n  // Add initial message if provided and send to backend\n  useEffect(() => {\n    if (initialMessage.trim()) {\n      const userMessage = {\n        id: Date.now(),\n        text: initialMessage,\n        sender: 'user',\n        timestamp: new Date()\n      };\n\n      setMessages([userMessage]);\n\n      // Send to backend and get real response\n      const processInitialMessage = async () => {\n        try {\n          setIsTyping(true);\n          const response = await sendMessage(initialMessage);\n\n          setMessages(prev => [...prev, {\n            id: Date.now() + 1,\n            text: response.response,\n            sender: 'jarvis',\n            timestamp: new Date(response.timestamp)\n          }]);\n        } catch (error) {\n          console.error('Failed to process initial message:', error);\n          setMessages(prev => [...prev, {\n            id: Date.now() + 1,\n            text: \"I'm sorry, I'm having trouble connecting to my backend systems. Please try again.\",\n            sender: 'jarvis',\n            timestamp: new Date()\n          }]);\n        } finally {\n          setIsTyping(false);\n        }\n      };\n\n      processInitialMessage();\n    }\n  }, [initialMessage, sendMessage]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages, isTyping]);\n\n  // Focus input on mount\n  useEffect(() => {\n    inputRef.current?.focus();\n  }, []);\n\n  const handleSendMessage = async () => {\n    if (!inputText.trim() || !isServerConnected) return;\n\n    const messageText = inputText.trim();\n    const newMessage = {\n      id: Date.now(),\n      text: messageText,\n      sender: 'user',\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, newMessage]);\n    setInputText('');\n\n    // Send to backend\n    try {\n      setIsTyping(true);\n      const response = await sendMessage(messageText);\n\n      setMessages(prev => [...prev, {\n        id: Date.now() + 1,\n        text: response.response,\n        sender: 'jarvis',\n        timestamp: new Date(response.timestamp)\n      }]);\n    } catch (error) {\n      console.error('Failed to send message:', error);\n      setMessages(prev => [...prev, {\n        id: Date.now() + 1,\n        text: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        sender: 'jarvis',\n        timestamp: new Date()\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Scroll functionality\n  const handleScroll = (e) => {\n    const { scrollTop, scrollHeight, clientHeight } = e.target;\n    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;\n    setShowScrollButton(!isNearBottom && messages.length > 0);\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  // Animation variants\n  const messageVariants = {\n    hidden: { opacity: 0, y: 10, scale: 0.95 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-screen bg-gradient-to-br from-gray-900 to-black text-white relative\">\n      {/* Connection Status Indicator */}\n      {!isServerConnected && (\n        <div className=\"fixed top-4 left-1/2 transform -translate-x-1/2 z-20\">\n          <div className=\"bg-red-900/80 border border-red-500/50 rounded-lg px-4 py-2\">\n            <span className=\"text-sm text-red-300\">Backend Disconnected - Chat functionality limited</span>\n          </div>\n        </div>\n      )}\n\n      {/* Expandable Sidebar - Left Side */}\n      <div className=\"fixed left-0 top-1/2 transform -translate-y-1/2 z-10\">\n        <ExpandableSidebar />\n      </div>\n\n      {/* Minimized JARVIS - Top Right */}\n      <MinimizedJarvis\n        onExpand={onExitToHome}\n        isProcessing={isProcessing}\n      />\n\n      {/* Header - JARVIS Style */}\n      <div className=\"flex items-center justify-between px-4 py-3 border-b border-cyan-500/20\">\n        {/* Left side - Title with dropdown */}\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowDropdown(!showDropdown)}\n              className=\"flex items-center space-x-2 text-white hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors\"\n            >\n              <span className=\"font-semibold\">JARVIS</span>\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </button>\n\n            {/* Dropdown Menu */}\n            {showDropdown && (\n              <div className=\"absolute top-full left-0 mt-1 w-48 bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-filter backdrop-blur-12 rounded-lg shadow-lg border border-cyan-500/30 z-50\">\n                <div className=\"py-1\">\n                  <button className=\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\">\n                    New Chat\n                  </button>\n                  <button className=\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\">\n                    Chat History\n                  </button>\n                  <button className=\"w-full text-left px-4 py-2 hover:bg-cyan-500/10 text-sm text-cyan-100 hover:text-cyan-400 transition-colors\">\n                    Settings\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Right side - Share button and menu */}\n        <div className=\"flex items-center space-x-2\">\n          <button className=\"flex items-center space-x-2 bg-gradient-to-r from-cyan-600/20 to-blue-600/20 hover:from-cyan-500/30 hover:to-blue-500/30 border border-cyan-500/30 px-3 py-2 rounded-lg transition-all duration-300 text-cyan-100 hover:text-cyan-400\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\" />\n            </svg>\n            <span className=\"text-sm\">Share</span>\n          </button>\n\n          <button\n            onClick={onExitToHome}\n            className=\"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\"\n            title=\"Return to JARVIS\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n            </svg>\n          </button>\n\n          <button className=\"p-2 hover:bg-cyan-500/20 rounded-lg transition-colors text-cyan-100 hover:text-cyan-400\">\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      {/* Messages Container - ChatGPT Style */}\n      <div className=\"flex-1 overflow-y-auto relative\" onScroll={handleScroll} ref={messagesContainerRef}>\n        <div className=\"max-w-3xl mx-auto\">\n          {messages.length === 0 ? (\n            /* Welcome Message */\n            <div className=\"flex flex-col items-center justify-center h-full text-center py-20\">\n              <div className=\"mb-8\">\n                <div className=\"w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center mx-auto mb-4\">\n                  <div className=\"w-8 h-8 rounded-full bg-cyan-400\"></div>\n                </div>\n                <h2 className=\"text-2xl font-semibold text-gray-300 mb-2\">How can I help you today?</h2>\n                <p className=\"text-gray-500\">I'm JARVIS, your AI assistant. Ask me anything!</p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"py-8\">\n              <AnimatePresence>\n                {messages.map((message) => (\n                  <motion.div\n                    key={message.id}\n                    variants={messageVariants}\n                    initial=\"hidden\"\n                    animate=\"visible\"\n                    className={`group mb-8 ${message.sender === 'user' ? 'ml-auto' : ''}`}\n                  >\n                    <div className=\"flex items-start space-x-4 px-4\">\n                      {message.sender === 'jarvis' && (\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\">\n                            <div className=\"w-4 h-4 rounded-full bg-cyan-400\"></div>\n                          </div>\n                        </div>\n                      )}\n\n                      <div className={`flex-1 ${message.sender === 'user' ? 'max-w-2xl ml-auto' : 'max-w-2xl'}`}>\n                        {message.sender === 'user' && (\n                          <div className=\"text-right mb-2\">\n                            <span className=\"text-sm text-gray-400\">You</span>\n                          </div>\n                        )}\n\n                        <div className={`${message.sender === 'user'\n                          ? 'bg-gray-700 text-white rounded-2xl px-4 py-3 ml-auto inline-block max-w-fit'\n                          : 'text-gray-100'\n                          }`}>\n                          <p className=\"text-sm leading-relaxed whitespace-pre-wrap\">{message.text}</p>\n                        </div>\n\n                        <div className={`mt-2 ${message.sender === 'user' ? 'text-right' : 'text-left'}`}>\n                          <span className=\"text-xs text-gray-500\">\n                            {message.timestamp.toLocaleTimeString()}\n                          </span>\n                        </div>\n                      </div>\n\n                      {message.sender === 'user' && (\n                        <div className=\"flex-shrink-0\">\n                          <div className=\"w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center\">\n                            <span className=\"text-sm font-medium\">U</span>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n\n              {/* Typing indicator */}\n              {isTyping && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"mb-8 px-4\"\n                >\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-r from-cyan-500 to-blue-600 flex items-center justify-center\">\n                        <div className=\"w-4 h-4 rounded-full bg-cyan-400\"></div>\n                      </div>\n                    </div>\n                    <div className=\"flex-1 max-w-2xl\">\n                      <div className=\"flex space-x-1 py-2\">\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                        <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          )}\n\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Scroll to bottom button */}\n        {showScrollButton && (\n          <div className=\"absolute bottom-4 right-4\">\n            <button\n              onClick={scrollToBottom}\n              className=\"bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-full shadow-lg transition-colors\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n              </svg>\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Input Area - JARVIS Style */}\n      <div className=\"flex justify-center p-6\">\n        <div className={`form-control ${isInputFocused ? 'focused' : ''}`}>\n          <input\n            ref={inputRef}\n            type=\"text\"\n            className=\"input\"\n            placeholder=\"Type something intelligent (Press Enter to chat)\"\n            value={inputText}\n            onChange={(e) => setInputText(e.target.value)}\n            onFocus={() => setIsInputFocused(true)}\n            onBlur={() => setIsInputFocused(false)}\n            onKeyDown={handleKeyPress}\n          />\n          <div className=\"input-border\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,MAAO,CAAAC,iBAAiB,KAAM,wBAAwB,CACtD,MAAO,CAAAC,eAAe,KAAM,sBAAsB,CAClD,OAASC,SAAS,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,YAAY,CAAEC,cAAc,CAAG,EAAG,CAAC,CAAAF,IAAA,CACvD,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACoB,cAAc,CAAEC,iBAAiB,CAAC,CAAGrB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACsB,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACwB,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC0B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAA4B,cAAc,CAAG3B,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA4B,QAAQ,CAAG5B,MAAM,CAAC,IAAI,CAAC,CAC7B,KAAM,CAAA6B,oBAAoB,CAAG7B,MAAM,CAAC,IAAI,CAAC,CAEzC;AACA,KAAM,CACJ8B,KAAK,CACLC,YAAY,CACZC,iBAAiB,CACjBC,KAAK,CACLC,WAAW,CACXC,SAAS,CACTC,mBAAmB,CACnBC,YACF,CAAC,CAAG/B,SAAS,CAAC,CAAC,CAEf;AACAL,SAAS,CAAC,IAAM,CACd,GAAIa,cAAc,CAACwB,IAAI,CAAC,CAAC,CAAE,CACzB,KAAM,CAAAC,WAAW,CAAG,CAClBC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,IAAI,CAAE7B,cAAc,CACpB8B,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CACtB,CAAC,CAEDzB,WAAW,CAAC,CAACuB,WAAW,CAAC,CAAC,CAE1B;AACA,KAAM,CAAAO,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACFxB,WAAW,CAAC,IAAI,CAAC,CACjB,KAAM,CAAAyB,QAAQ,CAAG,KAAM,CAAAb,WAAW,CAACpB,cAAc,CAAC,CAElDE,WAAW,CAACgC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CAC5BR,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBC,IAAI,CAAEI,QAAQ,CAACA,QAAQ,CACvBH,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAACM,QAAQ,CAACF,SAAS,CACxC,CAAC,CAAC,CAAC,CACL,CAAE,MAAOZ,KAAK,CAAE,CACdgB,OAAO,CAAChB,KAAK,CAAC,oCAAoC,CAAEA,KAAK,CAAC,CAC1DjB,WAAW,CAACgC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CAC5BR,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBC,IAAI,CAAE,mFAAmF,CACzFC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CACtB,CAAC,CAAC,CAAC,CACL,CAAC,OAAS,CACRnB,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAEDwB,qBAAqB,CAAC,CAAC,CACzB,CACF,CAAC,CAAE,CAAChC,cAAc,CAAEoB,WAAW,CAAC,CAAC,CAEjC;AACAjC,SAAS,CAAC,IAAM,KAAAiD,qBAAA,CACd,CAAAA,qBAAA,CAAAvB,cAAc,CAACwB,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAAE,CAACtC,QAAQ,CAAEM,QAAQ,CAAC,CAAC,CAExB;AACApB,SAAS,CAAC,IAAM,KAAAqD,iBAAA,CACd,CAAAA,iBAAA,CAAA1B,QAAQ,CAACuB,OAAO,UAAAG,iBAAA,iBAAhBA,iBAAA,CAAkBC,KAAK,CAAC,CAAC,CAC3B,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACvC,SAAS,CAACqB,IAAI,CAAC,CAAC,EAAI,CAACN,iBAAiB,CAAE,OAE7C,KAAM,CAAAyB,WAAW,CAAGxC,SAAS,CAACqB,IAAI,CAAC,CAAC,CACpC,KAAM,CAAAoB,UAAU,CAAG,CACjBlB,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,IAAI,CAAEc,WAAW,CACjBb,MAAM,CAAE,MAAM,CACdC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CACtB,CAAC,CAEDzB,WAAW,CAACgC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEU,UAAU,CAAC,CAAC,CAC1CxC,YAAY,CAAC,EAAE,CAAC,CAEhB;AACA,GAAI,CACFI,WAAW,CAAC,IAAI,CAAC,CACjB,KAAM,CAAAyB,QAAQ,CAAG,KAAM,CAAAb,WAAW,CAACuB,WAAW,CAAC,CAE/CzC,WAAW,CAACgC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CAC5BR,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBC,IAAI,CAAEI,QAAQ,CAACA,QAAQ,CACvBH,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAACM,QAAQ,CAACF,SAAS,CACxC,CAAC,CAAC,CAAC,CACL,CAAE,MAAOZ,KAAK,CAAE,CACdgB,OAAO,CAAChB,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CjB,WAAW,CAACgC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CAC5BR,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBC,IAAI,CAAE,8EAA8E,CACpFC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CACtB,CAAC,CAAC,CAAC,CACL,CAAC,OAAS,CACRnB,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAqC,cAAc,CAAIC,CAAC,EAAK,CAC5B,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,EAAI,CAACD,CAAC,CAACE,QAAQ,CAAE,CACpCF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBP,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,YAAY,CAAIJ,CAAC,EAAK,CAC1B,KAAM,CAAEK,SAAS,CAAEC,YAAY,CAAEC,YAAa,CAAC,CAAGP,CAAC,CAACQ,MAAM,CAC1D,KAAM,CAAAC,YAAY,CAAGH,YAAY,CAAGD,SAAS,CAAGE,YAAY,CAAG,GAAG,CAClEzC,mBAAmB,CAAC,CAAC2C,YAAY,EAAItD,QAAQ,CAACuD,MAAM,CAAG,CAAC,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,KAAAC,sBAAA,CAC3B,CAAAA,sBAAA,CAAA7C,cAAc,CAACwB,OAAO,UAAAqB,sBAAA,iBAAtBA,sBAAA,CAAwBpB,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAED;AACA,KAAM,CAAAoB,eAAe,CAAG,CACtBC,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAE,CAAEC,KAAK,CAAE,IAAK,CAAC,CAC1CC,OAAO,CAAE,CACPH,OAAO,CAAE,CAAC,CACVC,CAAC,CAAE,CAAC,CACJC,KAAK,CAAE,CAAC,CACRE,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CACF,CAAC,CAED,mBACEtE,KAAA,QAAKuE,SAAS,CAAC,qFAAqF,CAAAC,QAAA,EAEjG,CAAClD,iBAAiB,eACjBxB,IAAA,QAAKyE,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnE1E,IAAA,QAAKyE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,cAC1E1E,IAAA,SAAMyE,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,mDAAiD,CAAM,CAAC,CAC5F,CAAC,CACH,CACN,cAGD1E,IAAA,QAAKyE,SAAS,CAAC,sDAAsD,CAAAC,QAAA,cACnE1E,IAAA,CAACJ,iBAAiB,GAAE,CAAC,CAClB,CAAC,cAGNI,IAAA,CAACH,eAAe,EACd8E,QAAQ,CAAEtE,YAAa,CACvBkB,YAAY,CAAEA,YAAa,CAC5B,CAAC,cAGFrB,KAAA,QAAKuE,SAAS,CAAC,yEAAyE,CAAAC,QAAA,eAEtF1E,IAAA,QAAKyE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1CxE,KAAA,QAAKuE,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBxE,KAAA,WACE0E,OAAO,CAAEA,CAAA,GAAM5D,eAAe,CAAC,CAACD,YAAY,CAAE,CAC9C0D,SAAS,CAAC,iGAAiG,CAAAC,QAAA,eAE3G1E,IAAA,SAAMyE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7C1E,IAAA,QAAKyE,SAAS,CAAC,SAAS,CAACI,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5E1E,IAAA,SAAMgF,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,gBAAgB,CAAE,CAAC,CACrF,CAAC,EACA,CAAC,CAGRpE,YAAY,eACXf,IAAA,QAAKyE,SAAS,CAAC,wKAAwK,CAAAC,QAAA,cACrLxE,KAAA,QAAKuE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1E,IAAA,WAAQyE,SAAS,CAAC,6GAA6G,CAAAC,QAAA,CAAC,UAEhI,CAAQ,CAAC,cACT1E,IAAA,WAAQyE,SAAS,CAAC,6GAA6G,CAAAC,QAAA,CAAC,cAEhI,CAAQ,CAAC,cACT1E,IAAA,WAAQyE,SAAS,CAAC,6GAA6G,CAAAC,QAAA,CAAC,UAEhI,CAAQ,CAAC,EACN,CAAC,CACH,CACN,EACE,CAAC,CACH,CAAC,cAGNxE,KAAA,QAAKuE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CxE,KAAA,WAAQuE,SAAS,CAAC,uOAAuO,CAAAC,QAAA,eACvP1E,IAAA,QAAKyE,SAAS,CAAC,SAAS,CAACI,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5E1E,IAAA,SAAMgF,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,uOAAuO,CAAE,CAAC,CAC5S,CAAC,cACNnF,IAAA,SAAMyE,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,EAChC,CAAC,cAET1E,IAAA,WACE4E,OAAO,CAAEvE,YAAa,CACtBoE,SAAS,CAAC,yFAAyF,CACnGW,KAAK,CAAC,kBAAkB,CAAAV,QAAA,cAExB1E,IAAA,QAAKyE,SAAS,CAAC,SAAS,CAACI,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5E1E,IAAA,SAAMgF,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,6BAA6B,CAAE,CAAC,CAClG,CAAC,CACA,CAAC,cAETnF,IAAA,WAAQyE,SAAS,CAAC,yFAAyF,CAAAC,QAAA,cACzG1E,IAAA,QAAKyE,SAAS,CAAC,SAAS,CAACI,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5E1E,IAAA,SAAMgF,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,uHAAuH,CAAE,CAAC,CAC5L,CAAC,CACA,CAAC,EACN,CAAC,EACH,CAAC,cAGNjF,KAAA,QAAKuE,SAAS,CAAC,iCAAiC,CAACY,QAAQ,CAAE7B,YAAa,CAAC8B,GAAG,CAAEjE,oBAAqB,CAAAqD,QAAA,eACjGxE,KAAA,QAAKuE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/BnE,QAAQ,CAACuD,MAAM,GAAK,CAAC,cACpB,qBACA9D,IAAA,QAAKyE,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjFxE,KAAA,QAAKuE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1E,IAAA,QAAKyE,SAAS,CAAC,iHAAiH,CAAAC,QAAA,cAC9H1E,IAAA,QAAKyE,SAAS,CAAC,kCAAkC,CAAM,CAAC,CACrD,CAAC,cACNzE,IAAA,OAAIyE,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cACxF1E,IAAA,MAAGyE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,EAC7E,CAAC,CACH,CAAC,cAENxE,KAAA,QAAKuE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1E,IAAA,CAACL,eAAe,EAAA+E,QAAA,CACbnE,QAAQ,CAACgF,GAAG,CAAEC,OAAO,eACpBxF,IAAA,CAACN,MAAM,CAAC+F,GAAG,EAETC,QAAQ,CAAEzB,eAAgB,CAC1B0B,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAC,SAAS,CACjBnB,SAAS,eAAAoB,MAAA,CAAgBL,OAAO,CAACpD,MAAM,GAAK,MAAM,CAAG,SAAS,CAAG,EAAE,CAAG,CAAAsC,QAAA,cAEtExE,KAAA,QAAKuE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,EAC7Cc,OAAO,CAACpD,MAAM,GAAK,QAAQ,eAC1BpC,IAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B1E,IAAA,QAAKyE,SAAS,CAAC,kGAAkG,CAAAC,QAAA,cAC/G1E,IAAA,QAAKyE,SAAS,CAAC,kCAAkC,CAAM,CAAC,CACrD,CAAC,CACH,CACN,cAEDvE,KAAA,QAAKuE,SAAS,WAAAoB,MAAA,CAAYL,OAAO,CAACpD,MAAM,GAAK,MAAM,CAAG,mBAAmB,CAAG,WAAW,CAAG,CAAAsC,QAAA,EACvFc,OAAO,CAACpD,MAAM,GAAK,MAAM,eACxBpC,IAAA,QAAKyE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B1E,IAAA,SAAMyE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,CAC/C,CACN,cAED1E,IAAA,QAAKyE,SAAS,IAAAoB,MAAA,CAAKL,OAAO,CAACpD,MAAM,GAAK,MAAM,CACxC,6EAA6E,CAC7E,eAAe,CACd,CAAAsC,QAAA,cACH1E,IAAA,MAAGyE,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAAEc,OAAO,CAACrD,IAAI,CAAI,CAAC,CAC1E,CAAC,cAENnC,IAAA,QAAKyE,SAAS,SAAAoB,MAAA,CAAUL,OAAO,CAACpD,MAAM,GAAK,MAAM,CAAG,YAAY,CAAG,WAAW,CAAG,CAAAsC,QAAA,cAC/E1E,IAAA,SAAMyE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpCc,OAAO,CAACnD,SAAS,CAACyD,kBAAkB,CAAC,CAAC,CACnC,CAAC,CACJ,CAAC,EACH,CAAC,CAELN,OAAO,CAACpD,MAAM,GAAK,MAAM,eACxBpC,IAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B1E,IAAA,QAAKyE,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF1E,IAAA,SAAMyE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CAC3C,CAAC,CACH,CACN,EACE,CAAC,EA3CDc,OAAO,CAACxD,EA4CH,CACb,CAAC,CACa,CAAC,CAGjBnB,QAAQ,eACPb,IAAA,CAACN,MAAM,CAAC+F,GAAG,EACTE,OAAO,CAAE,CAAExB,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,EAAG,CAAE,CAC/BwB,OAAO,CAAE,CAAEzB,OAAO,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAC9BK,SAAS,CAAC,WAAW,CAAAC,QAAA,cAErBxE,KAAA,QAAKuE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC1E,IAAA,QAAKyE,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B1E,IAAA,QAAKyE,SAAS,CAAC,kGAAkG,CAAAC,QAAA,cAC/G1E,IAAA,QAAKyE,SAAS,CAAC,kCAAkC,CAAM,CAAC,CACrD,CAAC,CACH,CAAC,cACNzE,IAAA,QAAKyE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BxE,KAAA,QAAKuE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1E,IAAA,QAAKyE,SAAS,CAAC,iDAAiD,CAAM,CAAC,cACvEzE,IAAA,QAAKyE,SAAS,CAAC,iDAAiD,CAACsB,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAM,CAAC,cAC1GhG,IAAA,QAAKyE,SAAS,CAAC,iDAAiD,CAACsB,KAAK,CAAE,CAAEC,cAAc,CAAE,MAAO,CAAE,CAAM,CAAC,EACvG,CAAC,CACH,CAAC,EACH,CAAC,CACI,CACb,EACE,CACN,cAEDhG,IAAA,QAAKsF,GAAG,CAAEnE,cAAe,CAAE,CAAC,EACzB,CAAC,CAGLF,gBAAgB,eACfjB,IAAA,QAAKyE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC1E,IAAA,WACE4E,OAAO,CAAEb,cAAe,CACxBU,SAAS,CAAC,uFAAuF,CAAAC,QAAA,cAEjG1E,IAAA,QAAKyE,SAAS,CAAC,SAAS,CAACI,IAAI,CAAC,MAAM,CAACC,MAAM,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAC5E1E,IAAA,SAAMgF,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,4BAA4B,CAAE,CAAC,CACjG,CAAC,CACA,CAAC,CACN,CACN,EACE,CAAC,cAGNnF,IAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtCxE,KAAA,QAAKuE,SAAS,iBAAAoB,MAAA,CAAkBlF,cAAc,CAAG,SAAS,CAAG,EAAE,CAAG,CAAA+D,QAAA,eAChE1E,IAAA,UACEsF,GAAG,CAAElE,QAAS,CACd6E,IAAI,CAAC,MAAM,CACXxB,SAAS,CAAC,OAAO,CACjByB,WAAW,CAAC,kDAAkD,CAC9DC,KAAK,CAAE1F,SAAU,CACjB2F,QAAQ,CAAGhD,CAAC,EAAK1C,YAAY,CAAC0C,CAAC,CAACQ,MAAM,CAACuC,KAAK,CAAE,CAC9CE,OAAO,CAAEA,CAAA,GAAMzF,iBAAiB,CAAC,IAAI,CAAE,CACvC0F,MAAM,CAAEA,CAAA,GAAM1F,iBAAiB,CAAC,KAAK,CAAE,CACvC2F,SAAS,CAAEpD,cAAe,CAC3B,CAAC,cACFnD,IAAA,QAAKyE,SAAS,CAAC,cAAc,CAAM,CAAC,EACjC,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}