{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\My Code Work\\\\AI Adventures\\\\Jarvis-AI\\\\ui\\\\src\\\\components\\\\ui\\\\AnimatedButton.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimatedButton = ({\n  children,\n  onClick,\n  variant = 'primary',\n  size = 'medium',\n  disabled = false,\n  loading = false,\n  className = ''\n}) => {\n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',\n    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800',\n    success: 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700',\n    danger: 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700'\n  };\n  const sizes = {\n    small: 'px-4 py-2 text-sm',\n    medium: 'px-6 py-3 text-base',\n    large: 'px-8 py-4 text-lg'\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    onClick: onClick,\n    disabled: disabled || loading,\n    className: `\n        ${variants[variant]}\n        ${sizes[size]}\n        text-white font-semibold rounded-lg\n        transform transition-all duration-200\n        hover:scale-105 hover:shadow-lg\n        active:scale-95\n        disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\n        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\n        ${className}\n      `,\n    children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this) : children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_c = AnimatedButton;\nexport default AnimatedButton;\nvar _c;\n$RefreshReg$(_c, \"AnimatedButton\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AnimatedButton", "children", "onClick", "variant", "size", "disabled", "loading", "className", "variants", "primary", "secondary", "success", "danger", "sizes", "small", "medium", "large", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/src/components/ui/AnimatedButton.js"], "sourcesContent": ["import React from 'react';\n\nconst AnimatedButton = ({ \n  children, \n  onClick, \n  variant = 'primary', \n  size = 'medium',\n  disabled = false,\n  loading = false,\n  className = ''\n}) => {\n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',\n    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800',\n    success: 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700',\n    danger: 'bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700'\n  };\n\n  const sizes = {\n    small: 'px-4 py-2 text-sm',\n    medium: 'px-6 py-3 text-base',\n    large: 'px-8 py-4 text-lg'\n  };\n\n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled || loading}\n      className={`\n        ${variants[variant]}\n        ${sizes[size]}\n        text-white font-semibold rounded-lg\n        transform transition-all duration-200\n        hover:scale-105 hover:shadow-lg\n        active:scale-95\n        disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\n        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\n        ${className}\n      `}\n    >\n      {loading ? (\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n          <span>Loading...</span>\n        </div>\n      ) : (\n        children\n      )}\n    </button>\n  );\n};\n\nexport default AnimatedButton;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAC;EACtBC,QAAQ;EACRC,OAAO;EACPC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,QAAQ;EACfC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,sFAAsF;IAC/FC,SAAS,EAAE,kFAAkF;IAC7FC,OAAO,EAAE,0FAA0F;IACnGC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,KAAK,GAAG;IACZC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE,qBAAqB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAED,oBACEjB,OAAA;IACEG,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAC9BC,SAAS,EAAE;AACjB,UAAUC,QAAQ,CAACL,OAAO,CAAC;AAC3B,UAAUU,KAAK,CAACT,IAAI,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,UAAUG,SAAS;AACnB,OAAQ;IAAAN,QAAA,EAEDK,OAAO,gBACNP,OAAA;MAAKQ,SAAS,EAAC,6BAA6B;MAAAN,QAAA,gBAC1CF,OAAA;QAAKQ,SAAS,EAAC;MAA8E;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpGrB,OAAA;QAAAE,QAAA,EAAM;MAAU;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,GAENnB;EACD;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACC,EAAA,GAhDIrB,cAAc;AAkDpB,eAAeA,cAAc;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}