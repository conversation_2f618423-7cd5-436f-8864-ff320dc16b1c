{"ast": null, "code": "import { createContext } from 'react';\nconst MotionContext = createContext({});\nexport { MotionContext };", "map": {"version": 3, "names": ["createContext", "MotionContext"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\nconst MotionContext = createContext({});\n\nexport { MotionContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,aAAa,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC;AAEvC,SAASC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}