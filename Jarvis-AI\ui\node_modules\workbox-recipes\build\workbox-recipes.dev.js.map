{"version": 3, "file": "workbox-recipes.dev.js", "sources": ["../_version.js", "../googleFontsCache.js", "../warmStrategyCache.js", "../imageCache.js", "../staticResourceCache.js", "../pageCache.js", "../offlineFallback.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:recipes:6.5.4'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { StaleWhileRevalidate } from 'workbox-strategies/StaleWhileRevalidate.js';\nimport { CacheFirst } from 'workbox-strategies/CacheFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport { ExpirationPlugin } from 'workbox-expiration/ExpirationPlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [Google fonts]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#google_fonts} caching recipe\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cachePrefix] Cache prefix for caching stylesheets and webfonts. Defaults to google-fonts\n * @param {number} [options.maxAgeSeconds] Maximum age, in seconds, that font entries will be cached for. Defaults to 1 year\n * @param {number} [options.maxEntries] Maximum number of fonts that will be cached. Defaults to 30\n */\nfunction googleFontsCache(options = {}) {\n    const sheetCacheName = `${options.cachePrefix || 'google-fonts'}-stylesheets`;\n    const fontCacheName = `${options.cachePrefix || 'google-fonts'}-webfonts`;\n    const maxAgeSeconds = options.maxAgeSeconds || 60 * 60 * 24 * 365;\n    const maxEntries = options.maxEntries || 30;\n    // Cache the Google Fonts stylesheets with a stale-while-revalidate strategy.\n    registerRoute(({ url }) => url.origin === 'https://fonts.googleapis.com', new StaleWhileRevalidate({\n        cacheName: sheetCacheName,\n    }));\n    // Cache the underlying font files with a cache-first strategy for 1 year.\n    registerRoute(({ url }) => url.origin === 'https://fonts.gstatic.com', new CacheFirst({\n        cacheName: fontCacheName,\n        plugins: [\n            new CacheableResponsePlugin({\n                statuses: [0, 200],\n            }),\n            new ExpirationPlugin({\n                maxAgeSeconds,\n                maxEntries,\n            }),\n        ],\n    }));\n}\nexport { googleFontsCache };\n", "import './_version.js';\n/**\n * @memberof workbox-recipes\n \n * @param {Object} options\n * @param {string[]} options.urls Paths to warm the strategy's cache with\n * @param {Strategy} options.strategy Strategy to use\n */\nfunction warmStrategyCache(options) {\n    self.addEventListener('install', (event) => {\n        const done = options.urls.map((path) => options.strategy.handleAll({\n            event,\n            request: new Request(path),\n        })[1]);\n        event.waitUntil(Promise.all(done));\n    });\n}\nexport { warmStrategyCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { CacheFirst } from 'workbox-strategies/CacheFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport { ExpirationPlugin } from 'workbox-expiration/ExpirationPlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [image caching recipe]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#caching_images}\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to images\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.destination === 'image';\n * @param {number} [options.maxAgeSeconds] Maximum age, in seconds, that font entries will be cached for. Defaults to 30 days\n * @param {number} [options.maxEntries] Maximum number of images that will be cached. Defaults to 60\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction imageCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.destination === 'image';\n    const cacheName = options.cacheName || 'images';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const maxAgeSeconds = options.maxAgeSeconds || 30 * 24 * 60 * 60;\n    const maxEntries = options.maxEntries || 60;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    plugins.push(new ExpirationPlugin({\n        maxEntries,\n        maxAgeSeconds,\n    }));\n    const strategy = new CacheFirst({\n        cacheName,\n        plugins,\n    });\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { imageCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { StaleWhileRevalidate } from 'workbox-strategies/StaleWhileRevalidate.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport './_version.js';\n/**\n * An implementation of the [CSS and JavaScript files recipe]{@link https://developers.google.com/web/tools/workbox/guides/common-recipes#cache_css_and_javascript_files}\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to static-resources\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.destination === 'style' || request.destination === 'script' || request.destination === 'worker';\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction staticResourceCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.destination === 'style' ||\n        request.destination === 'script' ||\n        request.destination === 'worker';\n    const cacheName = options.cacheName || 'static-resources';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    const strategy = new StaleWhileRevalidate({\n        cacheName,\n        plugins,\n    });\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { staticResourceCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { warmStrategyCache } from './warmStrategyCache';\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { NetworkFirst } from 'workbox-strategies/NetworkFirst.js';\nimport { CacheableResponsePlugin } from 'workbox-cacheable-response/CacheableResponsePlugin.js';\nimport './_version.js';\n/**\n * An implementation of a page caching recipe with a network timeout\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.cacheName] Name for cache. Defaults to pages\n * @param {RouteMatchCallback} [options.matchCallback] Workbox callback function to call to match to. Defaults to request.mode === 'navigate';\n * @param {number} [options.networkTimoutSeconds] Maximum amount of time, in seconds, to wait on the network before falling back to cache. Defaults to 3\n * @param {WorkboxPlugin[]} [options.plugins] Additional plugins to use for this recipe\n * @param {string[]} [options.warmCache] Paths to call to use to warm this cache\n */\nfunction pageCache(options = {}) {\n    const defaultMatchCallback = ({ request }) => request.mode === 'navigate';\n    const cacheName = options.cacheName || 'pages';\n    const matchCallback = options.matchCallback || defaultMatchCallback;\n    const networkTimeoutSeconds = options.networkTimeoutSeconds || 3;\n    const plugins = options.plugins || [];\n    plugins.push(new CacheableResponsePlugin({\n        statuses: [0, 200],\n    }));\n    const strategy = new NetworkFirst({\n        networkTimeoutSeconds,\n        cacheName,\n        plugins,\n    });\n    // Registers the route\n    registerRoute(matchCallback, strategy);\n    // Warms the cache\n    if (options.warmCache) {\n        warmStrategyCache({ urls: options.warmCache, strategy });\n    }\n}\nexport { pageCache };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { setCatchHandler } from 'workbox-routing/setCatchHandler.js';\nimport { matchPrecache } from 'workbox-precaching/matchPrecache.js';\nimport './_version.js';\n/**\n * An implementation of the [comprehensive fallbacks recipe]{@link https://developers.google.com/web/tools/workbox/guides/advanced-recipes#comprehensive_fallbacks}. Be sure to include the fallbacks in your precache injection\n *\n * @memberof workbox-recipes\n *\n * @param {Object} [options]\n * @param {string} [options.pageFallback] Precache name to match for pag fallbacks. Defaults to offline.html\n * @param {string} [options.imageFallback] Precache name to match for image fallbacks.\n * @param {string} [options.fontFallback] Precache name to match for font fallbacks.\n */\nfunction offlineFallback(options = {}) {\n    const pageFallback = options.pageFallback || 'offline.html';\n    const imageFallback = options.imageFallback || false;\n    const fontFallback = options.fontFallback || false;\n    self.addEventListener('install', (event) => {\n        const files = [pageFallback];\n        if (imageFallback) {\n            files.push(imageFallback);\n        }\n        if (fontFallback) {\n            files.push(fontFallback);\n        }\n        event.waitUntil(self.caches\n            .open('workbox-offline-fallbacks')\n            .then((cache) => cache.addAll(files)));\n    });\n    const handler = async (options) => {\n        const dest = options.request.destination;\n        const cache = await self.caches.open('workbox-offline-fallbacks');\n        if (dest === 'document') {\n            const match = (await matchPrecache(pageFallback)) ||\n                (await cache.match(pageFallback));\n            return match || Response.error();\n        }\n        if (dest === 'image' && imageFallback !== false) {\n            const match = (await matchPrecache(imageFallback)) ||\n                (await cache.match(imageFallback));\n            return match || Response.error();\n        }\n        if (dest === 'font' && fontFallback !== false) {\n            const match = (await matchPrecache(fontFallback)) ||\n                (await cache.match(fontFallback));\n            return match || Response.error();\n        }\n        return Response.error();\n    };\n    setCatchHandler(handler);\n}\nexport { offlineFallback };\n"], "names": ["self", "_", "e", "googleFontsCache", "options", "sheetCacheName", "cachePrefix", "fontCacheName", "maxAgeSeconds", "maxEntries", "registerRoute", "url", "origin", "StaleWhileRevalidate", "cacheName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plugins", "CacheableResponsePlugin", "statuses", "ExpirationPlugin", "warmStrategyCache", "addEventListener", "event", "done", "urls", "map", "path", "strategy", "handleAll", "request", "Request", "waitUntil", "Promise", "all", "imageCache", "defaultMatchCallback", "destination", "matchCallback", "push", "warmCache", "staticResourceCache", "pageCache", "mode", "networkTimeoutSeconds", "NetworkFirst", "offlineFallback", "pageFallback", "imageFallback", "fontFallback", "files", "caches", "open", "then", "cache", "addAll", "handler", "dest", "match", "matchPrecache", "Response", "error", "setCatchHandler"], "mappings": ";;;;IAEA,IAAI;IACAA,EAAAA,IAAI,CAAC,uBAAD,CAAJ,IAAiCC,CAAC,EAAlC;IACH,CAFD,CAGA,OAAOC,CAAP,EAAU;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASC,gBAAT,CAA0BC,OAAO,GAAG,EAApC,EAAwC;IACpC,QAAMC,cAAc,GAAI,GAAED,OAAO,CAACE,WAAR,IAAuB,cAAe,cAAhE;IACA,QAAMC,aAAa,GAAI,GAAEH,OAAO,CAACE,WAAR,IAAuB,cAAe,WAA/D;IACA,QAAME,aAAa,GAAGJ,OAAO,CAACI,aAAR,IAAyB,KAAK,EAAL,GAAU,EAAV,GAAe,GAA9D;IACA,QAAMC,UAAU,GAAGL,OAAO,CAACK,UAAR,IAAsB,EAAzC,CAJoC;;IAMpCC,EAAAA,8BAAa,CAAC,CAAC;IAAEC,IAAAA;IAAF,GAAD,KAAaA,GAAG,CAACC,MAAJ,KAAe,8BAA7B,EAA6D,IAAIC,4CAAJ,CAAyB;IAC/FC,IAAAA,SAAS,EAAET;IADoF,GAAzB,CAA7D,CAAb,CANoC;;IAUpCK,EAAAA,8BAAa,CAAC,CAAC;IAAEC,IAAAA;IAAF,GAAD,KAAaA,GAAG,CAACC,MAAJ,KAAe,2BAA7B,EAA0D,IAAIG,wBAAJ,CAAe;IAClFD,IAAAA,SAAS,EAAEP,aADuE;IAElFS,IAAAA,OAAO,EAAE,CACL,IAAIC,kDAAJ,CAA4B;IACxBC,MAAAA,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ;IADc,KAA5B,CADK,EAIL,IAAIC,oCAAJ,CAAqB;IACjBX,MAAAA,aADiB;IAEjBC,MAAAA;IAFiB,KAArB,CAJK;IAFyE,GAAf,CAA1D,CAAb;IAYH;;IC5CD;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASW,iBAAT,CAA2BhB,OAA3B,EAAoC;IAChCJ,EAAAA,IAAI,CAACqB,gBAAL,CAAsB,SAAtB,EAAkCC,KAAD,IAAW;IACxC,UAAMC,IAAI,GAAGnB,OAAO,CAACoB,IAAR,CAAaC,GAAb,CAAkBC,IAAD,IAAUtB,OAAO,CAACuB,QAAR,CAAiBC,SAAjB,CAA2B;IAC/DN,MAAAA,KAD+D;IAE/DO,MAAAA,OAAO,EAAE,IAAIC,OAAJ,CAAYJ,IAAZ;IAFsD,KAA3B,EAGrC,CAHqC,CAA3B,CAAb;IAIAJ,IAAAA,KAAK,CAACS,SAAN,CAAgBC,OAAO,CAACC,GAAR,CAAYV,IAAZ,CAAhB;IACH,GAND;IAOH;;IChBD;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASW,UAAT,CAAoB9B,OAAO,GAAG,EAA9B,EAAkC;IAC9B,QAAM+B,oBAAoB,GAAG,CAAC;IAAEN,IAAAA;IAAF,GAAD,KAAiBA,OAAO,CAACO,WAAR,KAAwB,OAAtE;;IACA,QAAMtB,SAAS,GAAGV,OAAO,CAACU,SAAR,IAAqB,QAAvC;IACA,QAAMuB,aAAa,GAAGjC,OAAO,CAACiC,aAAR,IAAyBF,oBAA/C;IACA,QAAM3B,aAAa,GAAGJ,OAAO,CAACI,aAAR,IAAyB,KAAK,EAAL,GAAU,EAAV,GAAe,EAA9D;IACA,QAAMC,UAAU,GAAGL,OAAO,CAACK,UAAR,IAAsB,EAAzC;IACA,QAAMO,OAAO,GAAGZ,OAAO,CAACY,OAAR,IAAmB,EAAnC;IACAA,EAAAA,OAAO,CAACsB,IAAR,CAAa,IAAIrB,kDAAJ,CAA4B;IACrCC,IAAAA,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ;IAD2B,GAA5B,CAAb;IAGAF,EAAAA,OAAO,CAACsB,IAAR,CAAa,IAAInB,oCAAJ,CAAqB;IAC9BV,IAAAA,UAD8B;IAE9BD,IAAAA;IAF8B,GAArB,CAAb;IAIA,QAAMmB,QAAQ,GAAG,IAAIZ,wBAAJ,CAAe;IAC5BD,IAAAA,SAD4B;IAE5BE,IAAAA;IAF4B,GAAf,CAAjB;IAIAN,EAAAA,8BAAa,CAAC2B,aAAD,EAAgBV,QAAhB,CAAb,CAlB8B;;IAoB9B,MAAIvB,OAAO,CAACmC,SAAZ,EAAuB;IACnBnB,IAAAA,iBAAiB,CAAC;IAAEI,MAAAA,IAAI,EAAEpB,OAAO,CAACmC,SAAhB;IAA2BZ,MAAAA;IAA3B,KAAD,CAAjB;IACH;IACJ;;ICjDD;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASa,mBAAT,CAA6BpC,OAAO,GAAG,EAAvC,EAA2C;IACvC,QAAM+B,oBAAoB,GAAG,CAAC;IAAEN,IAAAA;IAAF,GAAD,KAAiBA,OAAO,CAACO,WAAR,KAAwB,OAAxB,IAC1CP,OAAO,CAACO,WAAR,KAAwB,QADkB,IAE1CP,OAAO,CAACO,WAAR,KAAwB,QAF5B;;IAGA,QAAMtB,SAAS,GAAGV,OAAO,CAACU,SAAR,IAAqB,kBAAvC;IACA,QAAMuB,aAAa,GAAGjC,OAAO,CAACiC,aAAR,IAAyBF,oBAA/C;IACA,QAAMnB,OAAO,GAAGZ,OAAO,CAACY,OAAR,IAAmB,EAAnC;IACAA,EAAAA,OAAO,CAACsB,IAAR,CAAa,IAAIrB,kDAAJ,CAA4B;IACrCC,IAAAA,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ;IAD2B,GAA5B,CAAb;IAGA,QAAMS,QAAQ,GAAG,IAAId,4CAAJ,CAAyB;IACtCC,IAAAA,SADsC;IAEtCE,IAAAA;IAFsC,GAAzB,CAAjB;IAIAN,EAAAA,8BAAa,CAAC2B,aAAD,EAAgBV,QAAhB,CAAb,CAduC;;IAgBvC,MAAIvB,OAAO,CAACmC,SAAZ,EAAuB;IACnBnB,IAAAA,iBAAiB,CAAC;IAAEI,MAAAA,IAAI,EAAEpB,OAAO,CAACmC,SAAhB;IAA2BZ,MAAAA;IAA3B,KAAD,CAAjB;IACH;IACJ;;IC1CD;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASc,SAAT,CAAmBrC,OAAO,GAAG,EAA7B,EAAiC;IAC7B,QAAM+B,oBAAoB,GAAG,CAAC;IAAEN,IAAAA;IAAF,GAAD,KAAiBA,OAAO,CAACa,IAAR,KAAiB,UAA/D;;IACA,QAAM5B,SAAS,GAAGV,OAAO,CAACU,SAAR,IAAqB,OAAvC;IACA,QAAMuB,aAAa,GAAGjC,OAAO,CAACiC,aAAR,IAAyBF,oBAA/C;IACA,QAAMQ,qBAAqB,GAAGvC,OAAO,CAACuC,qBAAR,IAAiC,CAA/D;IACA,QAAM3B,OAAO,GAAGZ,OAAO,CAACY,OAAR,IAAmB,EAAnC;IACAA,EAAAA,OAAO,CAACsB,IAAR,CAAa,IAAIrB,kDAAJ,CAA4B;IACrCC,IAAAA,QAAQ,EAAE,CAAC,CAAD,EAAI,GAAJ;IAD2B,GAA5B,CAAb;IAGA,QAAMS,QAAQ,GAAG,IAAIiB,4BAAJ,CAAiB;IAC9BD,IAAAA,qBAD8B;IAE9B7B,IAAAA,SAF8B;IAG9BE,IAAAA;IAH8B,GAAjB,CAAjB,CAT6B;;IAe7BN,EAAAA,8BAAa,CAAC2B,aAAD,EAAgBV,QAAhB,CAAb,CAf6B;;IAiB7B,MAAIvB,OAAO,CAACmC,SAAZ,EAAuB;IACnBnB,IAAAA,iBAAiB,CAAC;IAAEI,MAAAA,IAAI,EAAEpB,OAAO,CAACmC,SAAhB;IAA2BZ,MAAAA;IAA3B,KAAD,CAAjB;IACH;IACJ;;IC5CD;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,SAASkB,eAAT,CAAyBzC,OAAO,GAAG,EAAnC,EAAuC;IACnC,QAAM0C,YAAY,GAAG1C,OAAO,CAAC0C,YAAR,IAAwB,cAA7C;IACA,QAAMC,aAAa,GAAG3C,OAAO,CAAC2C,aAAR,IAAyB,KAA/C;IACA,QAAMC,YAAY,GAAG5C,OAAO,CAAC4C,YAAR,IAAwB,KAA7C;IACAhD,EAAAA,IAAI,CAACqB,gBAAL,CAAsB,SAAtB,EAAkCC,KAAD,IAAW;IACxC,UAAM2B,KAAK,GAAG,CAACH,YAAD,CAAd;;IACA,QAAIC,aAAJ,EAAmB;IACfE,MAAAA,KAAK,CAACX,IAAN,CAAWS,aAAX;IACH;;IACD,QAAIC,YAAJ,EAAkB;IACdC,MAAAA,KAAK,CAACX,IAAN,CAAWU,YAAX;IACH;;IACD1B,IAAAA,KAAK,CAACS,SAAN,CAAgB/B,IAAI,CAACkD,MAAL,CACXC,IADW,CACN,2BADM,EAEXC,IAFW,CAELC,KAAD,IAAWA,KAAK,CAACC,MAAN,CAAaL,KAAb,CAFL,CAAhB;IAGH,GAXD;;IAYA,QAAMM,OAAO,GAAG,MAAOnD,OAAP,IAAmB;IAC/B,UAAMoD,IAAI,GAAGpD,OAAO,CAACyB,OAAR,CAAgBO,WAA7B;IACA,UAAMiB,KAAK,GAAG,MAAMrD,IAAI,CAACkD,MAAL,CAAYC,IAAZ,CAAiB,2BAAjB,CAApB;;IACA,QAAIK,IAAI,KAAK,UAAb,EAAyB;IACrB,YAAMC,KAAK,GAAG,CAAC,MAAMC,8BAAa,CAACZ,YAAD,CAApB,MACT,MAAMO,KAAK,CAACI,KAAN,CAAYX,YAAZ,CADG,CAAd;IAEA,aAAOW,KAAK,IAAIE,QAAQ,CAACC,KAAT,EAAhB;IACH;;IACD,QAAIJ,IAAI,KAAK,OAAT,IAAoBT,aAAa,KAAK,KAA1C,EAAiD;IAC7C,YAAMU,KAAK,GAAG,CAAC,MAAMC,8BAAa,CAACX,aAAD,CAApB,MACT,MAAMM,KAAK,CAACI,KAAN,CAAYV,aAAZ,CADG,CAAd;IAEA,aAAOU,KAAK,IAAIE,QAAQ,CAACC,KAAT,EAAhB;IACH;;IACD,QAAIJ,IAAI,KAAK,MAAT,IAAmBR,YAAY,KAAK,KAAxC,EAA+C;IAC3C,YAAMS,KAAK,GAAG,CAAC,MAAMC,8BAAa,CAACV,YAAD,CAApB,MACT,MAAMK,KAAK,CAACI,KAAN,CAAYT,YAAZ,CADG,CAAd;IAEA,aAAOS,KAAK,IAAIE,QAAQ,CAACC,KAAT,EAAhB;IACH;;IACD,WAAOD,QAAQ,CAACC,KAAT,EAAP;IACH,GAnBD;;IAoBAC,EAAAA,kCAAe,CAACN,OAAD,CAAf;IACH;;;;;;;;;;;;;;;"}