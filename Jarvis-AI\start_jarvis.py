#!/usr/bin/env python3
"""
Jarvis AI Startup Script
Launches both the Python backend API server and React frontend
"""

import os
import sys
import subprocess
import time
import threading
import signal
import webbrowser
from pathlib import Path

# Get the directory where this script is located
SCRIPT_DIR = Path(__file__).parent.absolute()
BACKEND_DIR = SCRIPT_DIR / "Backend"
UI_DIR = SCRIPT_DIR / "ui"

class JarvisLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True

    def check_dependencies(self):
        """Check if required dependencies are installed"""
        print("🔍 Checking dependencies...")
        
        # Check Python dependencies
        try:
            import flask
            import flask_cors
            print("✅ Python dependencies found")
        except ImportError as e:
            print(f"❌ Missing Python dependency: {e}")
            print("📦 Installing Python dependencies...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(BACKEND_DIR / "requirements.txt")])

        # Check Node.js and npm
        try:
            subprocess.run(["node", "--version"], check=True, capture_output=True)
            subprocess.run(["npm", "--version"], check=True, capture_output=True)
            print("✅ Node.js and npm found")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Node.js or npm not found. Please install Node.js first.")
            return False

        # Check if node_modules exists
        if not (UI_DIR / "node_modules").exists():
            print("📦 Installing React dependencies...")
            subprocess.run(["npm", "install"], cwd=UI_DIR, check=True)

        return True

    def start_backend(self):
        """Start the Python backend API server"""
        print("🚀 Starting Jarvis Backend API Server...")
        
        try:
            # Change to backend directory and start the API server
            self.backend_process = subprocess.Popen(
                [sys.executable, "api_server.py"],
                cwd=BACKEND_DIR,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a moment for the server to start
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("✅ Backend API Server started successfully on http://localhost:5000")
                return True
            else:
                stdout, stderr = self.backend_process.communicate()
                print(f"❌ Backend failed to start:")
                print(f"STDOUT: {stdout}")
                print(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False

    def start_frontend(self):
        """Start the React frontend"""
        print("🚀 Starting React Frontend...")
        
        try:
            # Start the React development server
            self.frontend_process = subprocess.Popen(
                ["npm", "start"],
                cwd=UI_DIR,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for the frontend to start
            print("⏳ Waiting for React frontend to start...")
            time.sleep(10)
            
            if self.frontend_process.poll() is None:
                print("✅ React Frontend started successfully")
                print("🌐 Opening browser...")
                time.sleep(2)
                webbrowser.open("http://localhost:3000")
                return True
            else:
                stdout, stderr = self.frontend_process.communicate()
                print(f"❌ Frontend failed to start:")
                print(f"STDOUT: {stdout}")
                print(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False

    def monitor_processes(self):
        """Monitor both processes and restart if needed"""
        while self.running:
            time.sleep(5)
            
            # Check backend
            if self.backend_process and self.backend_process.poll() is not None:
                print("⚠️ Backend process stopped unexpectedly")
                
            # Check frontend
            if self.frontend_process and self.frontend_process.poll() is not None:
                print("⚠️ Frontend process stopped unexpectedly")

    def stop_all(self):
        """Stop all processes"""
        print("\n🛑 Stopping Jarvis AI...")
        self.running = False
        
        if self.backend_process:
            print("🔄 Stopping backend...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
        
        if self.frontend_process:
            print("🔄 Stopping frontend...")
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
        
        print("✅ Jarvis AI stopped successfully")

    def run(self):
        """Main run method"""
        print("🤖 Starting Jarvis AI...")
        print("=" * 50)
        
        # Check dependencies
        if not self.check_dependencies():
            return False
        
        # Start backend
        if not self.start_backend():
            return False
        
        # Start frontend
        if not self.start_frontend():
            self.stop_all()
            return False
        
        print("\n" + "=" * 50)
        print("🎉 Jarvis AI is now running!")
        print("🌐 Frontend: http://localhost:3000")
        print("🔧 Backend API: http://localhost:5000")
        print("💡 Press Ctrl+C to stop")
        print("=" * 50)
        
        try:
            # Start monitoring in a separate thread
            monitor_thread = threading.Thread(target=self.monitor_processes)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # Keep the main thread alive
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all()
        
        return True

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print("\n🛑 Received interrupt signal...")
    launcher.stop_all()
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handler for graceful shutdown
    launcher = JarvisLauncher()
    signal.signal(signal.SIGINT, signal_handler)
    
    # Run the launcher
    success = launcher.run()
    sys.exit(0 if success else 1)
