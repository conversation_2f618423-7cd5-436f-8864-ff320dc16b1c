from groq import Groq
from json import load, dump
import datetime
from dotenv import dotenv_values

env_vars = dotenv_values(".env")

Username = env_vars.get("Username")
Assistantname = env_vars.get("Assistantname")
GroqAPIKey = env_vars.get("GroqAPIKey")

# Initialize client lazily to avoid import-time errors
client = None

def get_groq_client():
    global client
    if client is None:
        try:
            client = Groq(api_key=GroqAPIKey)
        except Exception as e:
            print(f"Error initializing Groq client: {e}")
            client = None
    return client

messages = []

System = f"""Hello, I am {Username}, You are a very accurate and advanced AI named {Assistantname} which also has real-time up-to-date information from the internet and can provide answers to any question also you can perform various task.
*** You will answer the question from the provided data in a professional way and also you will answer in multilines not in a single line, there should be minimum 2 lines and if sentence is some big or on single line you will make it mutiline before answering ***
*** Answer in multi lines , i am saying multiline not ulti sentence  , there should be minimum 2 lines and can go more ***
*** Provide Answers In a sci-fi and a Professional Way, make sure to add full stops, commas, question marks, and use proper grammar also use advance english to impress the user.***
*** Do not tell time until I ask, just answer the question.***
*** Reply in only English, even if the question is in Hindi, reply in English.***
*** Do not provide notes in the output, just answer the question and never mention your training data. ***
*** You have a assistance and friendly nature and also you have some feeling and some favourable choices when user asks you something based on logic. You are also cherish in nature***
"""

SystemChatBot = [
    {"role": "system", "content": System}
]

try: 
    with open(r"Data\ChatLog.json", "r") as f:
        messages = load(f)
except FileNotFoundError:
    with open(r"Data\ChatLog.json", "w") as f:
        dump([], f)

def RealtimeInformation():
    current_date_time = datetime.datetime.now()
    day = current_date_time.strftime("%A")
    date = current_date_time.strftime("%d")
    month = current_date_time.strftime("%B")
    year = current_date_time.strftime("%Y")
    hour = current_date_time.strftime("%H")
    minute = current_date_time.strftime("%M")
    second = current_date_time.strftime("%S")

    data = f"Please use this real-time information if needed,\n"
    data += f"Day: {day}\nDate: {date}\nMonth: {month}\nYear: {year}\n"
    data += f"Time: {hour} hours: {minute} minutes: {second} seconds.\n"
    return data

def AnswerModifier(Answer):
    lines = Answer.split('\n')
    non_empty_lines = [line for line in lines if line.strip()]
    modified_answer = '\n'.join(non_empty_lines)
    return modified_answer

def ChatBot(Query):
    """ This function sends the user's query to the chatbot and returns the AI's response. """

    try:
        client = get_groq_client()
        if client is None:
            return "Sorry, the AI service is currently unavailable."

        with open(r"Data\ChatLog.json", "r") as f:
            messages = load(f)

            messages.append({"role": "user", "content": f"{Query}"})

            completion = client.chat.completions.create(
                model="llama3-70b-8192",
                messages=SystemChatBot + [{"role": "system", "content": RealtimeInformation()}] + messages,
                max_tokens=1024,
                temperature=0.7,
                top_p=1,
                stream=True,
                stop=None
            )

            Answer = ""

            for chunk in completion:
                if chunk.choices[0].delta.content:
                    Answer += chunk.choices[0].delta.content

            Answer = Answer.replace("</s>", "")

            messages.append({"role": "assistant", "content": Answer})

            with open(r"Data\ChatLog.json", "w") as f:
                dump(messages, f, indent=4)

            return AnswerModifier(Answer=Answer)

    except Exception as e:

        print(f"Error: {e}")
        with open(r"Data\ChatLog.json", "w") as f:
            dump([], f, indent=4)
        return ChatBot(Query)
    
if __name__ == "__main__":
    while True:
        user_input = input("Enter Your Question: ")
        print(ChatBot(user_input))