{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"transitionEnd\", \"transition\"];\nimport { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nfunction makeState(_ref, props, context, presenceContext) {\n  let {\n    scrapeMotionValuesFromProps,\n    createRenderState,\n    onMount\n  } = _ref;\n  const state = {\n    latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n    renderState: createRenderState()\n  };\n  if (onMount) {\n    state.mount = instance => onMount(props, instance, state);\n  }\n  return state;\n}\nconst makeUseVisualState = config => (props, isStatic) => {\n  const context = useContext(MotionContext);\n  const presenceContext = useContext(PresenceContext);\n  const make = () => makeState(config, props, context, presenceContext);\n  return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n  const values = {};\n  const motionValues = scrapeMotionValues(props, {});\n  for (const key in motionValues) {\n    values[key] = resolveMotionValue(motionValues[key]);\n  }\n  let {\n    initial,\n    animate\n  } = props;\n  const isControllingVariants$1 = isControllingVariants(props);\n  const isVariantNode$1 = isVariantNode(props);\n  if (context && isVariantNode$1 && !isControllingVariants$1 && props.inherit !== false) {\n    if (initial === undefined) initial = context.initial;\n    if (animate === undefined) animate = context.animate;\n  }\n  let isInitialAnimationBlocked = presenceContext ? presenceContext.initial === false : false;\n  isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n  const variantToSet = isInitialAnimationBlocked ? animate : initial;\n  if (variantToSet && typeof variantToSet !== \"boolean\" && !isAnimationControls(variantToSet)) {\n    const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n    list.forEach(definition => {\n      const resolved = resolveVariantFromProps(props, definition);\n      if (!resolved) return;\n      const {\n          transitionEnd,\n          transition\n        } = resolved,\n        target = _objectWithoutProperties(resolved, _excluded);\n      for (const key in target) {\n        let valueTarget = target[key];\n        if (Array.isArray(valueTarget)) {\n          /**\n           * Take final keyframe if the initial animation is blocked because\n           * we want to initialise at the end of that blocked animation.\n           */\n          const index = isInitialAnimationBlocked ? valueTarget.length - 1 : 0;\n          valueTarget = valueTarget[index];\n        }\n        if (valueTarget !== null) {\n          values[key] = valueTarget;\n        }\n      }\n      for (const key in transitionEnd) values[key] = transitionEnd[key];\n    });\n  }\n  return values;\n}\nexport { makeUseVisualState };", "map": {"version": 3, "names": ["useContext", "isAnimationControls", "PresenceContext", "resolveVariantFromProps", "useConstant", "resolveMotionValue", "MotionContext", "isControllingVariants", "isVariantNode", "makeState", "_ref", "props", "context", "presenceContext", "scrapeMotionValuesFromProps", "createRenderState", "onMount", "state", "latestValues", "makeLatestValues", "renderState", "mount", "instance", "makeUseVisualState", "config", "isStatic", "make", "scrapeMotionValues", "values", "motionValues", "key", "initial", "animate", "isControllingVariants$1", "isVariantNode$1", "inherit", "undefined", "isInitialAnimationBlocked", "variantToSet", "list", "Array", "isArray", "for<PERSON>ach", "definition", "resolved", "transitionEnd", "transition", "target", "_objectWithoutProperties", "_excluded", "valueTarget", "index", "length"], "sources": ["C:/Users/<USER>/My Code Work/AI Adventures/Jarvis-AI/ui/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs"], "sourcesContent": ["import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, onMount, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    if (onMount) {\n        state.mount = (instance) => onMount(props, instance, state);\n    }\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = useContext(MotionContext);\n    const presenceContext = useContext(PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = resolveMotionValue(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = isControllingVariants(props);\n    const isVariantNode$1 = isVariantNode(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !isAnimationControls(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        list.forEach((definition) => {\n            const resolved = resolveVariantFromProps(props, definition);\n            if (!resolved)\n                return;\n            const { transitionEnd, transition, ...target } = resolved;\n            for (const key in target) {\n                let valueTarget = target[key];\n                if (Array.isArray(valueTarget)) {\n                    /**\n                     * Take final keyframe if the initial animation is blocked because\n                     * we want to initialise at the end of that blocked animation.\n                     */\n                    const index = isInitialAnimationBlocked\n                        ? valueTarget.length - 1\n                        : 0;\n                    valueTarget = valueTarget[index];\n                }\n                if (valueTarget !== null) {\n                    values[key] = valueTarget;\n                }\n            }\n            for (const key in transitionEnd)\n                values[key] = transitionEnd[key];\n        });\n    }\n    return values;\n}\n\nexport { makeUseVisualState };\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,qBAAqB,EAAEC,aAAa,QAAQ,gDAAgD;AAErG,SAASC,SAASA,CAAAC,IAAA,EAA+DC,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAE;EAAA,IAA/F;IAAEC,2BAA2B;IAAEC,iBAAiB;IAAEC;EAAS,CAAC,GAAAN,IAAA;EAC3E,MAAMO,KAAK,GAAG;IACVC,YAAY,EAAEC,gBAAgB,CAACR,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEC,2BAA2B,CAAC;IAC5FM,WAAW,EAAEL,iBAAiB,CAAC;EACnC,CAAC;EACD,IAAIC,OAAO,EAAE;IACTC,KAAK,CAACI,KAAK,GAAIC,QAAQ,IAAKN,OAAO,CAACL,KAAK,EAAEW,QAAQ,EAAEL,KAAK,CAAC;EAC/D;EACA,OAAOA,KAAK;AAChB;AACA,MAAMM,kBAAkB,GAAIC,MAAM,IAAK,CAACb,KAAK,EAAEc,QAAQ,KAAK;EACxD,MAAMb,OAAO,GAAGZ,UAAU,CAACM,aAAa,CAAC;EACzC,MAAMO,eAAe,GAAGb,UAAU,CAACE,eAAe,CAAC;EACnD,MAAMwB,IAAI,GAAGA,CAAA,KAAMjB,SAAS,CAACe,MAAM,EAAEb,KAAK,EAAEC,OAAO,EAAEC,eAAe,CAAC;EACrE,OAAOY,QAAQ,GAAGC,IAAI,CAAC,CAAC,GAAGtB,WAAW,CAACsB,IAAI,CAAC;AAChD,CAAC;AACD,SAASP,gBAAgBA,CAACR,KAAK,EAAEC,OAAO,EAAEC,eAAe,EAAEc,kBAAkB,EAAE;EAC3E,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,YAAY,GAAGF,kBAAkB,CAAChB,KAAK,EAAE,CAAC,CAAC,CAAC;EAClD,KAAK,MAAMmB,GAAG,IAAID,YAAY,EAAE;IAC5BD,MAAM,CAACE,GAAG,CAAC,GAAGzB,kBAAkB,CAACwB,YAAY,CAACC,GAAG,CAAC,CAAC;EACvD;EACA,IAAI;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGrB,KAAK;EAChC,MAAMsB,uBAAuB,GAAG1B,qBAAqB,CAACI,KAAK,CAAC;EAC5D,MAAMuB,eAAe,GAAG1B,aAAa,CAACG,KAAK,CAAC;EAC5C,IAAIC,OAAO,IACPsB,eAAe,IACf,CAACD,uBAAuB,IACxBtB,KAAK,CAACwB,OAAO,KAAK,KAAK,EAAE;IACzB,IAAIJ,OAAO,KAAKK,SAAS,EACrBL,OAAO,GAAGnB,OAAO,CAACmB,OAAO;IAC7B,IAAIC,OAAO,KAAKI,SAAS,EACrBJ,OAAO,GAAGpB,OAAO,CAACoB,OAAO;EACjC;EACA,IAAIK,yBAAyB,GAAGxB,eAAe,GACzCA,eAAe,CAACkB,OAAO,KAAK,KAAK,GACjC,KAAK;EACXM,yBAAyB,GAAGA,yBAAyB,IAAIN,OAAO,KAAK,KAAK;EAC1E,MAAMO,YAAY,GAAGD,yBAAyB,GAAGL,OAAO,GAAGD,OAAO;EAClE,IAAIO,YAAY,IACZ,OAAOA,YAAY,KAAK,SAAS,IACjC,CAACrC,mBAAmB,CAACqC,YAAY,CAAC,EAAE;IACpC,MAAMC,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;IACxEC,IAAI,CAACG,OAAO,CAAEC,UAAU,IAAK;MACzB,MAAMC,QAAQ,GAAGzC,uBAAuB,CAACQ,KAAK,EAAEgC,UAAU,CAAC;MAC3D,IAAI,CAACC,QAAQ,EACT;MACJ,MAAM;UAAEC,aAAa;UAAEC;QAAsB,CAAC,GAAGF,QAAQ;QAAnBG,MAAM,GAAAC,wBAAA,CAAKJ,QAAQ,EAAAK,SAAA;MACzD,KAAK,MAAMnB,GAAG,IAAIiB,MAAM,EAAE;QACtB,IAAIG,WAAW,GAAGH,MAAM,CAACjB,GAAG,CAAC;QAC7B,IAAIU,KAAK,CAACC,OAAO,CAACS,WAAW,CAAC,EAAE;UAC5B;AACpB;AACA;AACA;UACoB,MAAMC,KAAK,GAAGd,yBAAyB,GACjCa,WAAW,CAACE,MAAM,GAAG,CAAC,GACtB,CAAC;UACPF,WAAW,GAAGA,WAAW,CAACC,KAAK,CAAC;QACpC;QACA,IAAID,WAAW,KAAK,IAAI,EAAE;UACtBtB,MAAM,CAACE,GAAG,CAAC,GAAGoB,WAAW;QAC7B;MACJ;MACA,KAAK,MAAMpB,GAAG,IAAIe,aAAa,EAC3BjB,MAAM,CAACE,GAAG,CAAC,GAAGe,aAAa,CAACf,GAAG,CAAC;IACxC,CAAC,CAAC;EACN;EACA,OAAOF,MAAM;AACjB;AAEA,SAASL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}